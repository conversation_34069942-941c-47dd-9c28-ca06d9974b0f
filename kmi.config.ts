import {defineConfig} from '@kmi/es';

import theme from "./src/theme";
import pkg from "./package.json";

// const ProxyUrl = "lego.staging.kuaishou.com";
// console.log('ProxyUrl:', ProxyUrl);
const ProxyUrl = "flowlive-admin.prt.kuaishou.com";
// const ProxyUrl = "flowlive-admin.staging.kuaishou.com";
export default defineConfig({
  rspack: {},
  https: {},

  server: {
    host: `dev.${ProxyUrl}`
  },

  proxy: {
    "/rest/*": {
      target: `https://${ProxyUrl}/`,
      changeOrigin: true,
      secure: false,
    },
    "/gateway/*": {
      target: `https://${ProxyUrl}/`,
      changeOrigin: true,
      secure: false,
    },
  },

  publicPath: (
    {
      mode: mode
    }
  ) => {
    return mode === "production"
      ? `https://w2.eckwai.com/kos/nlav12333/web-assets/${pkg.name}/`
      : `https://w2.eckwai.com/kos/nlav12333/web-assets-staging/${pkg.name}/`;
  },

  mountElementId: "main_root",
  forkTSChecker: {},

  theme: {
    ...theme
  },

  autoExternals: {},

  compat: {
    disableCoreJSLock: true
  }
});