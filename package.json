{"name": "kwaishop-baomai-live-admin-main-pc", "version": "0.1.0", "description": "", "template": "micro-main", "license": "MIT", "keywords": [], "publishConfig": {"access": "public"}, "scripts": {"prepare": "kmi setup"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "4.8.3", "@es/design-token": "^1.0.3", "@es/image": "1.1.2", "@es/kconf-web": "^1.0.4", "@es/kpro-baomai-workbench": "1.0.22-beta.13", "@es/kpro-tech-common-event-collector": "^1.3.0", "@es/logger": "2.2.6", "@es/request": "2.3.4", "@es/traceid": "^1.1.0", "@ks-hourglass/record": "2.5.5-beta.1", "@ks-radar/radar-core": "^1.2.9", "@ks-radar/radar-event-collect": "^1.2.9", "@ks-radar/radar-util": "^1.2.9", "@ks-video/kwai-player-web": "1.1.29", "@m-ui/icons": "4.0.2", "@m-ui/react": "2.2.2", "@monaco-editor/react": "^4.7.0", "@types/safe-json-stringify": "^1.1.5", "antd": "^5.24.0", "braft-editor": "^2.3.9", "classnames": "^2.3.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "js-cookie": "^3.0.1", "moment": "^2.30.1", "monaco-editor": "^0.52.2", "react": "^17.0.2", "react-dom": "^17.0.2", "react-loading-skeleton": "^3.5.0", "react-router-config": "5.1.1", "react-router-dom": "6.4.5", "react-sticky-box": "^2.0.5", "safe-json-parse-and-stringify": "^0.2.0"}, "devDependencies": {"@kmi/es": "^2.0.29", "@types/node": "^17.0.34", "@types/react": "17.0.80", "@types/react-dom": "^17.0.11", "@types/react-router-config": "^5.0.6", "@types/react-router-dom": "^5.3.3", "typescript": "^5.4.5"}, "repository": {"type": "git", "url": "https://git.corp.kuaishou.com/plateco-dev-fe/kwaishop-baomai/kwaishop-baomai-live-admin-main-pc.git"}, "radarKey": "b0429675a6", "resolutions": {"@types/react": "17.0.80", "@types/lodash": "4.14", "dayjs": "1.11.13"}}