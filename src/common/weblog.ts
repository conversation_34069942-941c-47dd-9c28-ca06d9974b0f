/** @format */

import { createEsLogger } from '@es/logger';
import { isProd } from './env';
import Cookies from 'js-cookie';
import { jsonParse } from 'safe-json-parse-and-stringify';
import KsRecord, { startRecord, addCustomEvent } from '@ks-hourglass/record';
import { getInitDimensions } from '@es/kpro-baomai-workbench';

// jia已经预置了默认配置，详见 https://npm.corp.kuaishou.com/-/web/detail/@es/logger/

const user_id = Cookies.get('userName') || Cookies.get('_did');

export const weblog = createEsLogger({
  env: isProd ? 'production' : 'logger',
  radarKey: import.meta.RADAR_KEY, // jia会自动申请radarKey并预置
  projectName: import.meta.PROJECT_NAME, // jia会预置
  realm: import.meta.REALM, // jia会预置
  subRealm: import.meta.SUBREALM, // jia会预置
  web_version: import.meta.WEB_VERSION,
  baseOptions: { user_id },
  pluginConfig: {
    radar: {
      customDimensions: getInitDimensions(),
      APIHook(apiData) {
        const { request, response } = apiData ?? {};
        try {
          const data = jsonParse(response?.data, {});
          const message =
            data.msg ||
            data.error_msg ||
            data.errorMsg ||
            (response.status === 200 ? '调用成功' : '系统异常');
          return {
            response_code: data.result ? data.result : response.status,
            response_msg: data.code ? `[${data.code}]${message}` : message, // 新增的错误信息，真实的错误原因，用户不感知，只上报
            status: response.status,
            custom_failed: data.result ? data.result !== 1 : response.status !== 200,
            broadcast_info: {
              // 雷达定义的广播信息，不会被上报到雷达数据中
              requestBody: request._config,
              responseBody: data,
            },
          };
        } catch (e) {
          return {
            response_code: response?.status,
            response_msg: '',
            status: response?.status,
            custom_failed: response?.status !== 200,
          };
        }
      },
    },
  },
  // extraPlugins: [new KsRecord()],
  errorCodeConfig: {
    initKtrace: true,
    bizCode: '0021',
  },
  logConfig: {
    wait: 60 * 1000, // 异步上报1分钟一次

    // 沙漏线上环境移除调试 https://docs.corp.kuaishou.com/k/home/<USER>/fcADl00Gc12wmEcSe1tmzDppr#section=h.hdru04kuhda7
    // sender(data) {
    //   fetch(
    //     // fetch 地址固定写死
    //     'https://zt-log.staging.kuaishou.com/rest/wd/common/log/collect/radar?kpn=voice&debug=1',
    //     {
    //       headers: {
    //         'content-type': 'text/plain;charset=UTF-8',
    //       },
    //       body: jsonStringify(data.data || {}),
    //       method: 'POST',
    //     },
    //   );
    // },
  },
});

// startRecord({
//   radar: weblog.plugins?.radar,
//   userId: user_id,
// });

// @ts-ignore
window.weblog = weblog;
// @ts-ignore
// window.hourglass = { addCustomEvent };
