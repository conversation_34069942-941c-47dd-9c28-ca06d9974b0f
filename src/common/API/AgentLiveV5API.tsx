export const LiveInfo = async (params: any) => {
    const response = await fetch('/gateway/c/agent/manage/service/invoke', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            api: "/api/smartlive/random/recording/query",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("LiveInfo：", result);
    return JSON.parse(result.data);
//     return {
//         "mainImage": [
//             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-2732061830-78d1bfb5efaf47bd98afd76196dce197.jpg?x-kcdn-pid=11072",
//             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-2732061830-7618f602f4344b0daf614df51af765dd.jpg?x-kcdn-pid=11072",
//             "https://p2-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-2732061830-c7c18be097eb48b18b7b3ac12f7ae3d9.jpg?x-kcdn-pid=11072"
//         ],
//         "liveItemSimilarityList": [
//             {
//                 "itemId": 25240176900830,
//                 "fineTuneScore": 0.8665179946663442,
//                 "imgUrl": "https://p2-ec.ecukwai.com/bs2/image-kwaishop-product/ITEM_IMAGE-2732061830-c7c18be097eb48b18b7b3ac12f7ae3d9.jpg",
//                 "missStreak": 0
//             }
//         ],
//         "screenSnapshot": [
//             "https://alivod.a.yximgs.com/livedvr/gifshow/2uUnU_xaKz0.1758722279014.txhbp1.jpg?auth_key=1759325297-1651128647-0-83a9bec12bfc1397e16e092738199599&caller=kwaishop-c-agent-manage-service&kpn=kwaishop-live-ai-record",
//             "https://alivod.a.yximgs.com/livedvr/gifshow/2uUnU_xaKz0.1758722288383.txhbp1.jpg?auth_key=1759325297-433713015-0-632ebbc7589ab8cd3c0d55dcee95bd8d&caller=kwaishop-c-agent-manage-service&kpn=kwaishop-live-ai-record"
//         ],
//         "userId": 460942663
//     };
}
