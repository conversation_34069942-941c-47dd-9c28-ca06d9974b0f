import { jsonStringify } from 'safe-json-parse-and-stringify';

// 无权限统一处理：当接口返回 30001/30002 时跳转无权限页
const handleNoPermission = (result: any) => {
  const code = result?.result;
  if (code === 30001 || code === '30001' || code === 30002 || code === '30002') {
    try {
      window.location.href = '/NoPermission?layoutType=1';
    } catch {}
    return true;
  }
  return false;
};

// 获取视频列表
//查询标注列表记录
export const LabelListQuery = async (params: any) => {
  console.log('params:', params);
  const response = await fetch('gateway/kwaishop/slice/query', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: jsonStringify({
      api: '/slice/label/list/query',
      param: jsonStringify(params),
    }),
  });
  const result = await response.json();
  console.log('---queryList:', result);
  if (handleNoPermission(result)) {
    throw new Error('无权限');
  }
  if (result?.result !== 1 && result?.result !== '1') {
    const msg = result?.error_msg || result?.errorMsg || '接口返回失败';
    console.error('VideoListAPI.LabelListQuery error:', msg, result);
    throw new Error(msg);
  }
  return result;
};
// 删除视频
export const DeleteVideo = async (params: { id: string }) => {
  console.log('DeleteVideo params:', params);
  const response = await fetch('gateway/kwaishop/slice/mutation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: jsonStringify({
      api: '/slice/video/delete',
      param: jsonStringify(params),
    }),
  });
  const result = await response.json();
  console.log('---DeleteVideo:', result);
  if (handleNoPermission(result)) {
    throw new Error('无权限');
  }
  if (result?.result !== 1 && result?.result !== '1') {
    const msg = result?.error_msg || result?.errorMsg || '接口返回失败';
    console.error('VideoListAPI.DeleteVideo error:', msg, result);
    throw new Error(msg);
  }
  return result;
};

// 批量操作
export const BatchOperation = async (params: {
  action: 'delete' | 'update_status';
  ids: string[];
  status?: number;
}) => {
  console.log('BatchOperation params:', params);
  const response = await fetch('gateway/kwaishop/slice/mutation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: jsonStringify({
      api: '/slice/video/batch/operation',
      param: jsonStringify(params),
    }),
  });
  const result = await response.json();
  console.log('---BatchOperation:', result);
  if (handleNoPermission(result)) {
    throw new Error('无权限');
  }
  if (result?.result !== 1 && result?.result !== '1') {
    const msg = result?.error_msg || result?.errorMsg || '接口返回失败';
    console.error('VideoListAPI.BatchOperation error:', msg, result);
    throw new Error(msg);
  }
  return result;
};
