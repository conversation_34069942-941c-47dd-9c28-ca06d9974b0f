export const LiveInfoV2 = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/query/v3",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("LiveInfo：", result);
    const obj =  JSON.parse(result.data);
    // obj.segmentCompareInfo =  [
    //     {
    //         "replayStartTime": "2025-07-29 14:27:11",
    //         "replayEndTime": "2025-07-29 14:29:11",
    //         "replayItemId": 24898547922446,
    //         "labelStartTime": "2025-07-29 14:20:29",
    //         "labelEndTime": "2025-07-29 14:27:54",
    //         "labelItemId": 24885310867446
    //     },
    //     {
    //         "replayStartTime": "2025-07-29 14:27:11",
    //         "replayEndTime": "2025-07-29 14:29:11",
    //         "replayItemId": 24898547922446,
    //         "labelStartTime": "2025-07-29 14:28:02",
    //         "labelEndTime": "2025-07-29 14:32:01",
    //         "labelItemId": 24898547922446
    //     }
    // ];

    obj.replayWindowData = [
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826867015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826877015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826887015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826897015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826907015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826917015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826927015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826937015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826947015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826957015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826967015},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826977015},
        {"scoreList": [{"itemId": 23782195634348, "fineTuneScore": 0.8}], "timestamp": 1753826977115},
        {"scoreList": [{"itemId": 23782195634347, "fineTuneScore": 0.8}], "timestamp": 1753826987015},
        {"scoreList": [{"itemId": 24472156479347, "fineTuneScore": 0.7}], "timestamp": 1753828387015},
        {"scoreList": [{"itemId": 24472156479347, "fineTuneScore": 0.7}], "timestamp": 1753827767000},
        {"scoreList": [{"itemId": 24472156479347, "fineTuneScore": 0.7}], "timestamp": 1753828427000},
    ];
    return obj;
}

export const LiveInfoV2ForReplay = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/query/v2forReply",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("LiveInfoForReplay：", result);
    return JSON.parse(result.data);
}

export const OnSaleItemUpdate = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/label/onSaleItem",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("itemInfos：", result);
    return JSON.parse(result.data);
}
export const LiveStreamInfo = async (params: any = {}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/liveStreamId/query",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:", params);
    console.log("LiveStreamInfo：", result);
    return result.data || result;
}

// 商品标签插入接口
export const InsertLabel = async (params: {
    liveStreamId: number;
    relatedTimestamp: number;
    itemId: number | string;
    labelFlag: number; // 1-正确, 2-错误, 3-无商品
    extraItemId?: number | string; // 当labelFlag为2时需要传入正确的商品ID
    sourceType: number; // 来源类型：tmItemId存在则为1，mmuItemId则为0
    modelVersion: string; // 模型版本，默认为"vvvvv"
    taskId?: string; // 可选的任务ID
}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/label/insert",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("标签插入参数:", params);
    console.log("标签插入结果：", result);
    return result.data || result;
}

export const QueryLabel = async (params: {
    liveStreamId: number;
    relatedTimestamp: number;
}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/label/query",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("标签查询参数:", params);
    console.log("标签查询结果：", result);
    return result.data || result;
}

export const QueryLabelForReplay = async (params: {
    replayId: number;
}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/label/query/forReply",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("回放 查询参数:", params);
    console.log("回放 查询结果：", result);
    return result.data || result;
}
