export const tagInfoList = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/team/tag/info/list",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("tagList:", result);
    return JSON.parse(result.data);
}


// 新增标签
export const tagInfoAdd= async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/team/tag/info/insert",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("Add:", result);
    return result;
}

// 删除标签
export const tagInfoDel = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/team/tag/info/delete",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("Delete:", result);
    return result.data;
}

export const tagInfoUpdate = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/team/tag/info/update",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("Update:", result);
    return result;
}