
export const insightList = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/team/schedule/board",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("insightList:", result);
    return JSON.parse(result.data);

    // return {
    //     "statistics": {
    //         "teamStats": [
    //             {
    //                 "name": "待开发需求数",
    //                 "value": 0,
    //                 "history": null
    //             },
    //             {
    //                 "name": "进行中需求数",
    //                 "value": 5,
    //                 "history": null
    //             },
    //             {
    //                 "name": "完成需求数",
    //                 "value": 25,
    //                 "history": [5, 16, 29, 23]
    //             }
    //         ],
    //         "pdStats": {
    //             "totalPd": 219,
    //             "history": [25, 91, 155, 116],
    //             "internPd": 18,
    //             "onLoanPd": 0,
    //             "overtimePd": 4
    //         },
    //         "headCountStats": {
    //             "totalCount": 6,
    //             "history": [5, 5, 6, 5],
    //             "teamMembers": [
    //                 {
    //                     "userName": "yemengying",
    //                     "name": "叶梦萦",
    //                     "avatar": "https://static.yximgs.com/bs2/kimAvatar/96ede050a79147ce91126999c656580d",
    //                     "role": "正式工"
    //                 },
    //                 {
    //                     "userName": "liuquansheng",
    //                     "name": "刘泉晟",
    //                     "avatar": "https://static.yximgs.com/bs2/kimAvatar/bcd46746adb94454b41e9e5d5d9bdcfe",
    //                     "role": "正式工"
    //                 },
    //                 {
    //                     "userName": "yanhao03",
    //                     "name": "闫浩",
    //                     "avatar": "https://static.yximgs.com/bs2/kimAvatar/1e3e3ace0e024c9fa29d7fb993111506",
    //                     "role": "正式工"
    //                 },
    //                 {
    //                     "userName": "guanshengling",
    //                     "name": "管盛灵",
    //                     "avatar": "https://static.yximgs.com/bs2/kimAvatar/47e5f9655d9144beacc4174e81498ea9",
    //                     "role": "实习生"
    //                 },
    //                 {
    //                     "userName": "qihanshi",
    //                     "name": "祁汉仕",
    //                     "avatar": "https://static.yximgs.com/bs2/kimAvatar/5d20418e5e8141d6887fdda3641271d5",
    //                     "role": "实习生"
    //                 },
    //                 {
    //                     "userName": "zhangdewei",
    //                     "name": "张德伟",
    //                     "avatar": "https://static.yximgs.com/bs2/kimAvatar/609b785e0fc44e8485eea2f4a112942d",
    //                     "role": "正式工"
    //                 }
    //             ]
    //         }
    //     },
    //     "categories": [
    //         {
    //             "category": "其他需求",
    //             "totalQuantity": 7,
    //             "totalPdCount": 34,
    //             "sources": [
    //                 {
    //                     "category": "其他需求",
    //                     "sourceName": "",
    //                     "quantity": 2,
    //                     "pdCount": 7
    //                 },
    //                 {
    //                     "category": "其他需求",
    //                     "sourceName": "for功能常规迭代",
    //                     "quantity": 3,
    //                     "pdCount": 16
    //                 },
    //                 {
    //                     "category": "其他需求",
    //                     "sourceName": "其他",
    //                     "quantity": 2,
    //                     "pdCount": 11
    //                 }
    //             ]
    //         },
    //         {
    //             "category": "外部需求",
    //             "totalQuantity": 13,
    //             "totalPdCount": 103,
    //             "sources": [
    //                 {
    //                     "category": "外部需求",
    //                     "sourceName": "for外部FT需求",
    //                     "quantity": 8,
    //                     "pdCount": 62
    //                 },
    //                 {
    //                     "category": "外部需求",
    //                     "sourceName": "for班委会下发需求",
    //                     "quantity": 5,
    //                     "pdCount": 41
    //                 }
    //             ]
    //         },
    //         {
    //             "category": "C端用户需求",
    //             "totalQuantity": 8,
    //             "totalPdCount": 51,
    //             "sources": [
    //                 {
    //                     "category": "C端用户需求",
    //                     "sourceName": "for功能常规迭代",
    //                     "quantity": 4,
    //                     "pdCount": 20
    //                 },
    //                 {
    //                     "category": "C端用户需求",
    //                     "sourceName": "for部门（事业部减一层）OKR",
    //                     "quantity": 4,
    //                     "pdCount": 31
    //                 }
    //             ]
    //         },
    //         {
    //             "category": "技术需求",
    //             "totalQuantity": 3,
    //             "totalPdCount": 31,
    //             "sources": [
    //                 {
    //                     "category": "技术需求",
    //                     "sourceName": "",
    //                     "quantity": 3,
    //                     "pdCount": 31
    //                 }
    //             ]
    //         }
    //     ],
    //     "details": [
    //         {
    //             "name": "鸿蒙商详拼接serverexptag",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8984792",
    //             "teamId": "T8984792",
    //             "owner": ["叶梦萦"],
    //             "status": "已完成",
    //             "pdCount": 4,
    //             "category": "技术需求",
    //             "source": "",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-06-05",
    //                 "endDate": "2025-06-10"
    //             }
    //         },
    //         {
    //             "name": "小黄车凑单TAB-过滤待开价商品",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8835639",
    //             "teamId": "T8835639",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 3,
    //             "category": "C端用户需求",
    //             "source": "for功能常规迭代",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-16",
    //                 "endDate": "2025-05-21"
    //             }
    //         },
    //         {
    //             "name": "浏览定位接入反爬",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8767142",
    //             "teamId": "T8767142",
    //             "owner": ["闫浩"],
    //             "status": "已完成",
    //             "pdCount": 2,
    //             "category": "其他需求",
    //             "source": "其他",
    //             "priority": "中等",
    //             "timeRange": {
    //                 "startDate": "2025-05-08",
    //                 "endDate": "2025-05-12"
    //             }
    //         },
    //         {
    //             "name": "直播好友推荐",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8746123",
    //             "teamId": "T8746123",
    //             "owner": ["闫浩", "刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 4,
    //             "category": "其他需求",
    //             "source": "",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-04-21",
    //                 "endDate": "2025-05-12"
    //             }
    //         },
    //         {
    //             "name": "AI试衣的直播间下值不正确",
    //             "teamLink": "https://team.corp.kuaishou.com/task/B2211220",
    //             "teamId": "B2211220",
    //             "owner": ["管盛灵"],
    //             "status": "已完成",
    //             "pdCount": 3,
    //             "category": "其他需求",
    //             "source": "",
    //             "priority": "中等",
    //             "timeRange": {
    //                 "startDate": "2025-04-17",
    //                 "endDate": "2025-05-14"
    //             }
    //         },
    //         {
    //             "name": "国补专项-直播间",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8745097",
    //             "teamId": "T8745097",
    //             "owner": ["叶梦萦"],
    //             "status": "已完成",
    //             "pdCount": 7,
    //             "category": "外部需求",
    //             "source": "for班委会下发需求",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-04-25",
    //                 "endDate": "2025-05-14"
    //             }
    //         },
    //         {
    //             "name": "618互动玩法",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8679126",
    //             "teamId": "T8679126",
    //             "owner": ["闫浩", "祁汉仕"],
    //             "status": "已完成",
    //             "pdCount": 10,
    //             "category": "外部需求",
    //             "source": "for外部FT需求",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-04-23",
    //                 "endDate": "2025-05-16"
    //             }
    //         },
    //         {
    //             "name": "达人新客首单工具建设",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8826149",
    //             "teamId": "T8826149",
    //             "owner": ["刘泉晟"],
    //             "status": "进行中",
    //             "pdCount": 9,
    //             "category": "外部需求",
    //             "source": "for外部FT需求",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-06-04",
    //                 "endDate": "2025-06-23"
    //             }
    //         },
    //         {
    //             "name": "小黄车秒杀商品卡直降气泡优化",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8891652",
    //             "teamId": "T8891652",
    //             "owner": ["闫浩"],
    //             "status": "已完成",
    //             "pdCount": 4,
    //             "category": "C端用户需求",
    //             "source": "for功能常规迭代",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-22",
    //                 "endDate": "2025-05-27"
    //             }
    //         },
    //         {
    //             "name": "电商直播间正品感透传",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8822118",
    //             "teamId": "T8822118",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 8,
    //             "category": "外部需求",
    //             "source": "for班委会下发需求",
    //             "priority": "中等",
    //             "timeRange": {
    //                 "startDate": "2025-05-27",
    //                 "endDate": "2025-06-06"
    //             }
    //         },
    //         {
    //             "name": "直播广场接口迁移",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8925005",
    //             "teamId": "T8925005",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 12,
    //             "category": "外部需求",
    //             "source": "for外部FT需求",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-20",
    //                 "endDate": "2025-06-11"
    //             }
    //         },
    //         {
    //             "name": "小黄车成本优化-字段回迁",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8575827",
    //             "teamId": "T8575827",
    //             "owner": ["闫浩"],
    //             "status": "进行中",
    //             "pdCount": 22,
    //             "category": "技术需求",
    //             "source": "",
    //             "priority": "中等",
    //             "timeRange": {
    //                 "startDate": "2025-04-16",
    //                 "endDate": "2025-06-20"
    //             }
    //         },
    //         {
    //             "name": "领券专区膨胀券文案调整.",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8945875",
    //             "teamId": "T8945875",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 1,
    //             "category": "其他需求",
    //             "source": "for功能常规迭代",
    //             "priority": "中等",
    //             "timeRange": {
    //                 "startDate": "2025-05-29",
    //                 "endDate": "2025-05-29"
    //             }
    //         },
    //         {
    //             "name": "主播卖点增加违规词校验-商详",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8868880",
    //             "teamId": "T8868880",
    //             "owner": ["闫浩"],
    //             "status": "已完成",
    //             "pdCount": 5,
    //             "category": "C端用户需求",
    //             "source": "for功能常规迭代",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-21",
    //                 "endDate": "2025-05-28"
    //             }
    //         },
    //         {
    //             "name": "发货时效BC全链路表达优化",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8746636",
    //             "teamId": "T8746636",
    //             "owner": ["管盛灵"],
    //             "status": "已完成",
    //             "pdCount": 5,
    //             "category": "外部需求",
    //             "source": "for外部FT需求",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-04-17",
    //                 "endDate": "2025-05-13"
    //             }
    //         },
    //         {
    //             "name": "拉车引导自动/手动承接明确",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8758273",
    //             "teamId": "T8758273",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 8,
    //             "category": "C端用户需求",
    //             "source": "for部门（事业部减一层）OKR",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-04-28",
    //                 "endDate": "2025-05-15"
    //             }
    //         },
    //         {
    //             "name": "店铺直播元素拓展深度",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8646187",
    //             "teamId": "T8646187",
    //             "owner": ["刘泉晟"],
    //             "status": "未知",
    //             "pdCount": 1,
    //             "category": "外部需求",
    //             "source": "for外部FT需求",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-04-21",
    //                 "endDate": "2025-05-06"
    //             }
    //         },
    //         {
    //             "name": "【待解决】小黄车非置顶商品，显示红框Bug",
    //             "teamLink": "https://team.corp.kuaishou.com/task/B2507751",
    //             "teamId": "B2507751",
    //             "owner": ["闫浩"],
    //             "status": "已完成",
    //             "pdCount": 5,
    //             "category": "技术需求",
    //             "source": "",
    //             "priority": "中等",
    //             "timeRange": {
    //                 "startDate": "2025-05-21",
    //                 "endDate": "2025-05-27"
    //             }
    //         },
    //         {
    //             "name": "讲解回放页-下发讲解分辨率",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8811998",
    //             "teamId": "T8811998",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 6,
    //             "category": "其他需求",
    //             "source": "for功能常规迭代",
    //             "priority": "中等",
    //             "timeRange": {
    //                 "startDate": "2025-05-07",
    //                 "endDate": "2025-05-21"
    //             }
    //         },
    //         {
    //             "name": "618大促混资膨胀券",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8797328",
    //             "teamId": "T8797328",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 9,
    //             "category": "外部需求",
    //             "source": "for外部FT需求",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-04-27",
    //                 "endDate": "2025-05-15"
    //             }
    //         },
    //         {
    //             "name": "单单返链路优化",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8689859",
    //             "teamId": "T8689859",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 9,
    //             "category": "其他需求",
    //             "source": "其他",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-07",
    //                 "endDate": "2025-05-19"
    //             }
    //         },
    //         {
    //             "name": "主播卖点加违规词校验",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8763057",
    //             "teamId": "T8763057",
    //             "owner": ["闫浩"],
    //             "status": "已完成",
    //             "pdCount": 8,
    //             "category": "C端用户需求",
    //             "source": "for功能常规迭代",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-09",
    //                 "endDate": "2025-05-23"
    //             }
    //         },
    //         {
    //             "name": "国补修改地址后逻辑",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8936361",
    //             "teamId": "T8936361",
    //             "owner": ["叶梦萦"],
    //             "status": "进行中",
    //             "pdCount": 8,
    //             "category": "外部需求",
    //             "source": "for班委会下发需求",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-06-11",
    //                 "endDate": "2025-06-20"
    //             }
    //         },
    //         {
    //             "name": "拉车引导呈现左下角/讲解卡点位探索+利益点丰富",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8758636",
    //             "teamId": "T8758636",
    //             "owner": ["叶梦萦"],
    //             "status": "已完成",
    //             "pdCount": 6,
    //             "category": "C端用户需求",
    //             "source": "for部门（事业部减一层）OKR",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-16",
    //                 "endDate": "2025-05-25"
    //             }
    //         },
    //         {
    //             "name": "reco信息透传",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8821541",
    //             "teamId": "T8821541",
    //             "owner": ["张德伟"],
    //             "status": "已完成",
    //             "pdCount": 7,
    //             "category": "C端用户需求",
    //             "source": "for部门（事业部减一层）OKR",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-26",
    //                 "endDate": "2025-06-05"
    //             }
    //         },
    //         {
    //             "name": "分销-未拼接cpsTrack参数",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8821641",
    //             "teamId": "T8821641",
    //             "owner": ["刘泉晟"],
    //             "status": "进行中",
    //             "pdCount": 8,
    //             "category": "外部需求",
    //             "source": "for外部FT需求",
    //             "priority": "高优",
    //             "timeRange": {
    //                 "startDate": "2025-06-04",
    //                 "endDate": "2025-06-13"
    //             }
    //         },
    //         {
    //             "name": "直播间设计大基建需求群",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8846537",
    //             "teamId": "T8846537",
    //             "owner": ["叶梦萦"],
    //             "status": "已完成",
    //             "pdCount": 8,
    //             "category": "外部需求",
    //             "source": "for外部FT需求",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-04-23",
    //                 "endDate": "2025-05-22"
    //             }
    //         },
    //         {
    //             "name": "直播间动态领券页",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8901105",
    //             "teamId": "T8901105",
    //             "owner": ["叶梦萦"],
    //             "status": "已完成",
    //             "pdCount": 9,
    //             "category": "其他需求",
    //             "source": "for功能常规迭代",
    //             "priority": "中等",
    //             "timeRange": {
    //                 "startDate": "2025-05-21",
    //                 "endDate": "2025-06-06"
    //             }
    //         },
    //         {
    //             "name": "直播电商-福利购支持双任务设置",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8575732",
    //             "teamId": "T8575732",
    //             "owner": ["叶梦萦"],
    //             "status": "已完成",
    //             "pdCount": 14,
    //             "category": "外部需求",
    //             "source": "for班委会下发需求",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-23",
    //                 "endDate": "2025-06-12"
    //             }
    //         },
    //         {
    //             "name": "小黄车列表底部商品推荐（二期）",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8551880",
    //             "teamId": "T8551880",
    //             "owner": ["闫浩"],
    //             "status": "进行中",
    //             "pdCount": 10,
    //             "category": "C端用户需求",
    //             "source": "for部门（事业部减一层）OKR",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-06-03",
    //                 "endDate": "2025-06-26"
    //             }
    //         },
    //         {
    //             "name": "直播间好友推荐（二期）",
    //             "teamLink": "https://team.corp.kuaishou.com/task/T8791591",
    //             "teamId": "T8791591",
    //             "owner": ["刘泉晟"],
    //             "status": "已完成",
    //             "pdCount": 4,
    //             "category": "外部需求",
    //             "source": "for班委会下发需求",
    //             "priority": "最高优",
    //             "timeRange": {
    //                 "startDate": "2025-05-22",
    //                 "endDate": "2025-05-26"
    //             }
    //         }
    //     ],
    //     "historyPeriods": [
    //         {
    //             "startDate": "2025-01-01",
    //             "endDate": "2025-01-31"
    //         },
    //         {
    //             "startDate": "2025-02-01",
    //             "endDate": "2025-02-28"
    //         },
    //         {
    //             "startDate": "2025-03-01",
    //             "endDate": "2025-03-31"
    //         },
    //         {
    //             "startDate": "2025-04-01",
    //             "endDate": "2025-04-30"
    //         }
    //     ]
    // }

}