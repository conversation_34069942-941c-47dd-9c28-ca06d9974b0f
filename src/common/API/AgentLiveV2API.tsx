export const LiveInfoV2 = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/query/v2",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("LiveInfo：", result);
    return JSON.parse(result.data);
}

export const LiveInfoV2ForReplay = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/query/v2forReply",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("LiveInfoForReplay：", result);
    return JSON.parse(result.data);
}

export const OnSaleItemUpdate = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/label/onSaleItem",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("itemInfos：", result);
    return JSON.parse(result.data);
}
export const LiveStreamInfo = async (params: any = {}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/liveStreamId/query",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:", params);
    console.log("LiveStreamInfo：", result);
    return result.data || result;
}

// 商品标签插入接口
export const InsertLabel = async (params: {
    liveStreamId: number;
    relatedTimestamp: number;
    itemId: number | string;
    labelFlag: number; // 1-正确, 2-错误, 3-无商品
    extraItemId?: number | string; // 当labelFlag为2时需要传入正确的商品ID
    sourceType: number; // 来源类型：tmItemId存在则为1，mmuItemId则为0
    modelVersion: string; // 模型版本，默认为"vvvvv"
    taskId?: string; // 可选的任务ID
}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/label/insert",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("标签插入参数:", params);
    console.log("标签插入结果：", result);
    return result.data || result;
}

export const QueryLabel = async (params: {
    liveStreamId: number;
    relatedTimestamp: number;
}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/label/query",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("标签查询参数:", params);
    console.log("标签查询结果：", result);
    return result.data || result;
}

export const QueryLabelForReplay = async (params: {
    replayId: number;
}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/label/query/forReply",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("回放 查询参数:", params);
    console.log("回放 查询结果：", result);
    return result.data;
}

// 回放标注 新增or更新
export const UpdateLabelForReplay = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/labelForReply/update",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("UpdateLabelForReplay 请求参数:", params);
    console.log("UpdateLabelForReplay 响应结果：", result);
    return result;
}

// 回放标注 查询标注人信息
export const QueryLabelUserInfoForReplay = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/labelForReply/labelUserInfo",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("QueryLabelUserInfoForReplay  请求参数:", params);
    console.log("QueryLabelUserInfoForReplay  响应结果：", result);
    return result;
}