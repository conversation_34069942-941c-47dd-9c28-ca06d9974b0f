export const LiveInfo = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/label/sellingVideo/queryRecord",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("LiveInfo：", result);
    return JSON.parse(result.data);
    
    // 返回LiveReplayLabelRecord对象
    // return {
    //     "id": 1001,
    //     "replayId": 25964248,
    //     "labelStatus": 0,
    //     "labelResult": JSON.stringify([{
    //         "userId": null,
    //         "liveStreamId": 13855297631,
    //         "videoUrl": "https://alivod.a.yximgs.com/livedvr3/flv2ts/gifshow/i-FRfeamy04_vodcnallavcsdL1sdr.1753803732684-10000.0-152.m3u8?auth_key=1758761823-1831142874-0-e6a37acc6022a22a3d3e58a470ca47fd&caller=kwaishop-c-agent-manage-service-task&kpn=",
    //         "isShowGood": 1,
    //         "frameTraceInfoList": [
    //             {
    //                 "index": 0,
    //                 "asrText": "五号链接就没有人反映掉色问题。好吧，裤子白色会透，身上穿的颜色叫麻本啊。麻本色。一百三十八斤的话，那个叫云里看海，直接去拍我们家的一个l就够了。上身的话它是一个哈伦裤，全部做什么上宽，然后呢整个裤子下面窄的一个设计啊，这条裤子是上新福利。然后如果说你跟主播一样，肚子上面有肉。",
    //                 "frame2imageList": [
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f00.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f01.jpg",
    //                         "frameIndex": 1,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f02.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f03.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f04.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f05.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f06.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f07.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f08.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     },
    //                     {
    //                         "imageUrl": "https://bs3-hb1.corp.kuaishou.com/kwaishop-langbridge-evaluation/markyi/reject/live13855297631_video552_f09.jpg",
    //                         "frameIndex": 0,
    //                         "isGoodFrame": 1
    //                     }
    //                 ]
    //             }
    //         ],
    //         "videoId": "live13855297631_video552",
    //         "replayId": 25964248
    //     }]),
    //     "labelUserName": "张三",
    //     "createTime": 1758761823000,
    //     "updateTime": 1758761823000,
    //     "extend": ""
    // };
}

export const submitLabelResult = async (params: any = {}) => {
    try {
        console.log('提交标注结果API调用，参数:', params);
        
        const response = await fetch('/gateway/live/control/common/api/invoke', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                api: "api/live/intelligent/label/sellingVideo/updateLabelRecord",
                param: JSON.stringify(params)
            }),
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('提交标注结果API原始响应:', result);
        
        // 安全地解析data字段
        if (typeof result.data === 'string') {
            if (result.data.trim() === '') {
                // 空字符串，返回空对象
                return { code: result.code, message: result.message || result.error_msg };
            }
            try {
                return JSON.parse(result.data);
            } catch (parseError) {
                console.error('解析result.data失败:', parseError, '原始数据:', result.data);
                return { code: result.code, message: result.message || result.error_msg };
            }
        }
        
        return result.data || result;
    } catch (error) {
        console.error('提交标注结果API调用失败:', error);
        throw error;
    }
}

// 回放标注 查询标注人信息
export const QueryLabelUserInfoForReplay = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/labelForReply/labelUserInfo",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("QueryLabelUserInfoForReplay  请求参数:", params);
    console.log("QueryLabelUserInfoForReplay  响应结果：", result);
    return result;
}

