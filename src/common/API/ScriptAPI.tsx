export const ScriptDebug = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            api: "api/script/debug",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:", params)
    console.log("ScriptDebug:", result); // 修正了这里的中文冒号
    return JSON.parse(result.data);
}

export const scriptGet = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/script/get",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("scriptList:", JSON.parse(result.data));
    return JSON.parse(result.data);
}

export const scriptList = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/script/list",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("scriptList:", JSON.parse(result.data));
    return JSON.parse(result.data);
}

// 新增脚本
export const scriptAdd = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/script/insert",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("Add:", result);
    return result;
}

// 删除脚本
export const scriptDel = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/script/delete",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("Delete:", result);
    return result.data;
}

export const scriptUpdate = async (params) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        // 将对象转换为 JSON 字符串
        body: JSON.stringify(
            {
                api: "api/script/update",
                param: JSON.stringify(params)
            })
    });
    const result = await response.json();
    console.log("Update:", result);
    return result;
}
