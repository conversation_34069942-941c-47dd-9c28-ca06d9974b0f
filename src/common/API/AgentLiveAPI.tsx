export const LiveInfo = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/query",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("LiveInfo：", result);
    return JSON.parse(result.data);
    // return [{
    //     "userId": 514875590,
    //     "liveStreamId": 14004600779,
    //     "videoUrl": [
    //         "https://alivod.a.yximgs.com/livedvr/flv2ts/gifshow/SE0YHyUnj14_avc1000m.1758111936648-3146491.0-16.m3u8?auth_key=1758718084-664381245-0-fe912d2874a5fe7d9cef96c9b3bf576f&caller=kwaishop-resource-center&kpn=",
    //         "https://txvod.a.yximgs.com/livedvr/flv2ts/gifshow/SE0YHyUnj14_avc1000m.1758111936648-3146491.0-16.m3u8?sign=1758716284-3918974604195099265-0-98b2d0050cecaba0cadf78c830a241e3&caller=kwaishop-resource-center&kpn="
    //     ],
    //     "liveStream": {
    //         "id": 1.40046008E10,
    //         "userId": 5.14875584E8,
    //         "cover": "{\"isServerCompose\":true,\"height\":1280,\"originalUrl\":\"head2:2025/09/16/18/BMjAyNTA5MTYxODA3NTNfNTE0ODc1NTkwX29jMTQwMDEzOTQ1ODFfbHY=.jpg\",\"mainColor\":\"3E3940\",\"isOriginalAudited\":true,\"width\":720,\"safeUrl\":\"head2:2025/08/21/12/BMjAyNTA4MjExMjExMzFfNTE0ODc1NTkwX29jMTM5MjY0NzIwMzNfbHY=.jpg\",\"rawUrl\":\"head2:2025/09/17/20/BMjAyNTA5MTcyMDI1MjdfNTE0ODc1NTkwXzE0MDA0NjAwNzc5X2x2.jpg\",\"kpgUrl\":\"head2:2025/09/17/20/BMjAyNTA5MTcyMDI1MjdfNTE0ODc1NTkwXzE0MDA0NjAwNzc5X2x2.kpg\",\"webpUrl\":\"head2:2025/09/17/20/BMjAyNTA5MTcyMDI1MjdfNTE0ODc1NTkwXzE0MDA0NjAwNzc5X2x2.webp\",\"coverAudited\":true}",
    //         "status": 1.0,
    //         "provider": 104.0,
    //         "time": 1.75811199E12,
    //         "endTime": null,
    //         "idc": "Zhaowei",
    //         "endType": 0.0,
    //         "data": "{\"dispatchOriginTranscode\":true,\"originSiteIdc\":\"aliyun\",\"clientId\":1,\"enableVoiceComment\":false,\"useMerchantAuthorApi\":true,\"hasLandscape\":false,\"caption\":\"开播啦\",\"transcodeInOrigin\":true,\"enableOriginSiteFreeTrafficPull\":false,\"isShopLive\":true,\"streamType\":1,\"currentWishListId\":12238140406,\"isPushWatchingList\":true,\"properties\":128,\"videoQualityType\":2}",
    //         "coverAudited": true,
    //         "serverComposeCover": true,
    //         "originalCoverAudited": true,
    //         "liveMatePushSource": "UNKNOWN",
    //         "pushWatchingList": true,
    //         "roomLevel": "LEVEL_UNKNOWN",
    //         "roomLevelNew": 0.0,
    //         "announcement": null,
    //         "suspectedViolation": false,
    //         "currentWishListId": 1.22381404E10,
    //         "currentPkId": null,
    //         "shopCommodityIds": [],
    //         "courseLive": false,
    //         "courseId": 0.0,
    //         "lessonId": 0.0,
    //         "liveMateNewReport": false,
    //         "clientId": "IPHONE",
    //         "glassesLiving": false,
    //         "liveDelayMillis": 0.0,
    //         "delayLive": false,
    //         "shopLive": true,
    //         "authorBanReason": null,
    //         "audienceBanReason": null,
    //         "banImageKey": null,
    //         "activityType": 0.0,
    //         "repushDeviceId": null,
    //         "privateType": "UNKNOWN",
    //         "privateTypeValue": 0.0,
    //         "paidShowId": 0.0,
    //         "paidShowFreeDuration": 0.0,
    //         "unionLiveId": 0.0,
    //         "bitProperties": [
    //             "useStateSignalStorageInfo"
    //         ],
    //         "properties": 128.0
    //     },
    //     "windowTimestamp": 1758112776289,
    //     "windowRelatedTime": 851907,
    //     "topSimilarity": null,
    //     "matchedGoodsAgent": null,
    //     "matchedGoodsAlgo": null,
    //     "matchedGoodsMmu": null,
    //     "matchedGoodsReplay": null,
    //     "frameTraceInfoList": [
    //         {
    //             "index": 0,
    //             "clipVideoUrl": "https://data.playground.edgeone.ai/resource/video/flv/demo-1/index.flv",
    //             "internalClipVideoUrl": "http://livedvr-meida.voip.yximgs.internal:80/livedvr/gifshow/SE0YHyUnj14_avc1000m.1758112767187.alihbs2.flv?internal=true",
    //             "screenSnapshotTimeStamp": 1758112776289,
    //             "screenSnapshotRelatedTime": 851907,
    //             "screenSnapshotUrls": [
    //                 "https://d4-ec.ecukwai.com/bs2/ad-kwaishop-live-test/YWQta3dhaXNob3AtbGl2ZS10ZXN0Omt3YWlzaG9wX3NtYXJ0X2xpdmU6MTpNRVJDSEFOVDpbQkA3MzQ1YmZlOToxMjgyNzc5ODMzNDAwMQ==.png?x-kcdn-pid=112636"
    //             ],
    //             "asrText": "和黑色显瘦针织短裤是二百四十九的，这个等下格了啊。",
    //             "frameEmbeddingRequest": null,
    //             "frameEmbeddingResponse": null,
    //             "candidateItemList": [
    //                 24221401597590,
    //                 25171856183590,
    //                 24906255339590,
    //                 25202001121590,
    //                 21621514504590,
    //                 23001832920919,
    //                 25030640120590,
    //                 25030584696590,
    //                 24836386822590,
    //                 23652763680590,
    //                 21748505227837,
    //                 21748872237837,
    //                 22918420718837,
    //                 23725785406590,
    //                 24784457921590,
    //                 24785773231590,
    //                 21095862285501,
    //                 20483816014777,
    //                 20394691857046,
    //                 24455027227590,
    //                 21698172649345,
    //                 21828978744590,
    //                 21829420089590,
    //                 21937127837590
    //             ],
    //             "topSimilarItemIds": [
    //                 {
    //                     "itemId": 21937127837590,
    //                     "fineTuneScore": 0.4530042571182448,
    //                     "imgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-9a8ec4d456c643c784c83b1ccf53f63a.jpg"
    //                 },
    //                 {
    //                     "itemId": 21621514504590,
    //                     "fineTuneScore": 0.37050392553092826,
    //                     "imgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-c4d03b36f7684222ba6a8759ee7e12e3.jpg"
    //                 },
    //                 {
    //                     "itemId": 25030584696590,
    //                     "fineTuneScore": 0.36609887466524793,
    //                     "imgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/white_base-514875590-f529e211776e479d99fc510302f7fc5d.png"
    //                 },
    //                 {
    //                     "itemId": 25171856183590,
    //                     "fineTuneScore": 0.35455158664146524,
    //                     "imgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/white_base-514875590-a432cff3ffcb45d88e005918f19d716d.png"
    //                 },
    //                 {
    //                     "itemId": 25030640120590,
    //                     "fineTuneScore": 0.3217419342638931,
    //                     "imgUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/white_base-514875590-b00d0fcf8d1c41d8b4e71be6ac17ebf8.png"
    //                 }
    //             ],
    //             "topSimilarItems": [
    //                 {
    //                     "itemId": 21937127837590,
    //                     "score": 0.4530042571182448,
    //                     "goodDetail": {
    //                         "itemId": 21937127837590,
    //                         "title": "福利款no退#百搭皮u短裤",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e6830eb43f614caaa5835ec0b265e825.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "女装-裤子-短裤",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e6830eb43f614caaa5835ec0b265e825.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-3c5fa403b66749c4bf7d94020b3adfac.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-27900a1413944b0dbb329d0642fb8ab5.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-9a8ec4d456c643c784c83b1ccf53f63a.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-343005eae68d44e0936b3657e99d9b32.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-412fcf449eba48a69191647f8f39bade.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-c8221ca28f4e43719d26be28c7abf728.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "品牌": "其他秋哥/qiuqiugg",
    //                             "弹力": "无弹",
    //                             "短裤分类": "其他",
    //                             "服饰材质": "其他"
    //                         }
    //                     },
    //                     "carItemIndex": 24
    //                 },
    //                 {
    //                     "itemId": 21621514504590,
    //                     "score": 0.37050392553092826,
    //                     "goodDetail": {
    //                         "itemId": 21621514504590,
    //                         "title": "no退no换#韩国正品T恤",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-dc664eec19c34203ba827529a25244fc.jpg",
    //                         "itemDesc": "商品原价6.9w韩币\n现在特价福利\n不支持退换\n谨慎下单\n退换拉黑拒收拉黑\n永不接单\n",
    //                         "itemCategoryTree": "女装-T恤",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-dc664eec19c34203ba827529a25244fc.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-3d5473ad507341de8691cb28df8804d9.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-6325a089e87e41ea8d70f2bfc57c18c2.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-c4d03b36f7684222ba6a8759ee7e12e3.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-7f93285560164cbc8c30bd0a2114ebba.jpg",
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-8fe72a82355748bbbcd3e74e827a4bbe.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "品牌": "其他/other",
    //                             "面料材质": "棉",
    //                             "领型": "圆领",
    //                             "袖长": "长袖",
    //                             "服装版型": "宽松",
    //                             "衣长": "长款"
    //                         }
    //                     },
    //                     "carItemIndex": 5
    //                 },
    //                 {
    //                     "itemId": 25030584696590,
    //                     "score": 0.36609887466524793,
    //                     "goodDetail": {
    //                         "itemId": 25030584696590,
    //                         "title": "222#韩国拼色袜子（no退换）",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-a10029ba384d4980ad188f11a06caef7.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "内衣/家居服/袜子-袜子-中筒袜",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-a10029ba384d4980ad188f11a06caef7.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "适用性别": "男女通用",
    //                             "图案": "拼色",
    //                             "厚薄": "常规",
    //                             "品牌": "无品牌",
    //                             "上市年份季节": "2025年秋季",
    //                             "服装款式细节": "纯色",
    //                             "锅具材质": "棉",
    //                             "成分含量": "29%及以下",
    //                             "适用季节": "不限季节"
    //                         }
    //                     },
    //                     "carItemIndex": 8
    //                 },
    //                 {
    //                     "itemId": 25171856183590,
    //                     "score": 0.35455158664146524,
    //                     "goodDetail": {
    //                         "itemId": 25171856183590,
    //                         "title": "6077#五色百搭打底衫",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-8f4c66c223ce4bb191cbb8fb619778f0.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "女装-内搭-打底衫",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-8f4c66c223ce4bb191cbb8fb619778f0.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "品牌": "无品牌",
    //                             "袖型": "常规",
    //                             "服饰厚度": "常规",
    //                             "衣长": "常规款",
    //                             "上市年份季节": "2025年秋季",
    //                             "面料材质": "棉",
    //                             "面料成分含量": "95%以上",
    //                             "组合形式": "单件",
    //                             "领型": "圆领",
    //                             "袖长": "长袖",
    //                             "流行元素": "纯色",
    //                             "风格": "简约风",
    //                             "服装版型": "修身"
    //                         }
    //                     },
    //                     "carItemIndex": 2
    //                 },
    //                 {
    //                     "itemId": 25030640120590,
    //                     "score": 0.3217419342638931,
    //                     "goodDetail": {
    //                         "itemId": 25030640120590,
    //                         "title": "333#韩国四色宽条袜（no退换）",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "内衣/家居服/袜子-袜子-中筒袜",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "图案": "条纹",
    //                             "厚薄": "常规",
    //                             "品牌": "无品牌",
    //                             "上市年份季节": "2025年秋季",
    //                             "服装款式细节": "纯色",
    //                             "锅具材质": "棉",
    //                             "成分含量": "29%及以下"
    //                         }
    //                     },
    //                     "carItemIndex": 7
    //                 },
    //                 {
    //                     "itemId": 25030640120590,
    //                     "score": 0.3217419342638931,
    //                     "goodDetail": {
    //                         "itemId": 25030640120590,
    //                         "title": "333#韩国四色宽条袜（no退换）",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "内衣/家居服/袜子-袜子-中筒袜",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "图案": "条纹",
    //                             "厚薄": "常规",
    //                             "品牌": "无品牌",
    //                             "上市年份季节": "2025年秋季",
    //                             "服装款式细节": "纯色",
    //                             "锅具材质": "棉",
    //                             "成分含量": "29%及以下"
    //                         }
    //                     },
    //                     "carItemIndex": 74
    //                 },
    //                 {
    //                     "itemId": 25030640120590,
    //                     "score": 0.3217419342638931,
    //                     "goodDetail": {
    //                         "itemId": 25030640120590,
    //                         "title": "333#韩国四色宽条袜（no退换）",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "内衣/家居服/袜子-袜子-中筒袜",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "图案": "条纹",
    //                             "厚薄": "常规",
    //                             "品牌": "无品牌",
    //                             "上市年份季节": "2025年秋季",
    //                             "服装款式细节": "纯色",
    //                             "锅具材质": "棉",
    //                             "成分含量": "29%及以下"
    //                         }
    //                     },
    //                     "carItemIndex": 73
    //                 },
    //                 {
    //                     "itemId": 25030640120590,
    //                     "score": 0.3217419342638931,
    //                     "goodDetail": {
    //                         "itemId": 25030640120590,
    //                         "title": "333#韩国四色宽条袜（no退换）",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "内衣/家居服/袜子-袜子-中筒袜",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "图案": "条纹",
    //                             "厚薄": "常规",
    //                             "品牌": "无品牌",
    //                             "上市年份季节": "2025年秋季",
    //                             "服装款式细节": "纯色",
    //                             "锅具材质": "棉",
    //                             "成分含量": "29%及以下"
    //                         }
    //                     },
    //                     "carItemIndex": 71
    //                 }
    //                 ,
    //                 {
    //                     "itemId": 25030640120590,
    //                     "score": 0.3217419342638931,
    //                     "goodDetail": {
    //                         "itemId": 25030640120590,
    //                         "title": "333#韩国四色宽条袜（no退换）",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "内衣/家居服/袜子-袜子-中筒袜",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "图案": "条纹",
    //                             "厚薄": "常规",
    //                             "品牌": "无品牌",
    //                             "上市年份季节": "2025年秋季",
    //                             "服装款式细节": "纯色",
    //                             "锅具材质": "棉",
    //                             "成分含量": "29%及以下"
    //                         }
    //                     },
    //                     "carItemIndex": 91
    //                 },
    //                 {
    //                     "itemId": 25030640120590,
    //                     "score": 0.3217419342638931,
    //                     "goodDetail": {
    //                         "itemId": 25030640120590,
    //                         "title": "333#韩国四色宽条袜（no退换）",
    //                         "imageUrl": "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg",
    //                         "itemDesc": null,
    //                         "itemCategoryTree": "内衣/家居服/袜子-袜子-中筒袜",
    //                         "imageUrls": [
    //                             "https://p4-ec.ecukwai.com/bs2/image-kwaishop-product/item_image-514875590-e49468f932f04837820eccc46987fbc4.jpg"
    //                         ],
    //                         "itemCategoryPropInfo": {
    //                             "图案": "条纹",
    //                             "厚薄": "常规",
    //                             "品牌": "无品牌",
    //                             "上市年份季节": "2025年秋季",
    //                             "服装款式细节": "纯色",
    //                             "锅具材质": "棉",
    //                             "成分含量": "29%及以下"
    //                         }
    //                     },
    //                     "carItemIndex": 81
    //                 }
    //             ]
    //         }
    //     ],
    //     "agentRequest": null,
    //     "agentResponse": null,
    //     "replayModelVersion": null,
    //     "replayChatModelVersion": null,
    //     "replayFineTuneModelVersion": null,
    //     "recognizeItemId": 0,
    //     "segmentStartTimeRelatedSec": 0,
    //     "segmentEndTimeRelatedSec": 0,
    //     "sellerInfo": {
    //         "id": 514875590,
    //         "name": "ALU _阿璐",
    //         "imgUrl": "http://p4.a.yximgs.com/uhead/AB/2025/05/23/21/BMjAyNTA1MjMyMTEzMDdfNTE0ODc1NTkwXzFfaGQ2NjNfMjg5_s.jpg"
    //     },
    //     "liveStreamStartTime": "2025-09-17 20:25:24",
    //     "windowCurTime": "2025-09-17 20:39:36.289",
    //     "labelResult": null
    // }];
}

export const OnSaleItemUpdate = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/label/onSaleItem",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("itemInfos：", result);
    return JSON.parse(result.data);
}

export const LiveStreamInfo = async (params: any = {}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/liveStreamId/query",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:", params);
    console.log("LiveStreamInfo：", result);
    return JSON.parse(result.data);
}

export const insertLabel = async (params: any = {}) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/live/intelligent/explanation/label/insert",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:", params);
    console.log("insertLabel：", result);
    return JSON.parse(result.data);
}

