import { jsonParse, jsonStringify } from 'safe-json-parse-and-stringify';
import type { BaseResponse, FeatMapItem, ListInfoParams, ListInfoByTagParams } from './types';

// 数据转换函数
const transformToFeatMapItem = (item: FeatMapItem): FeatMapItem => {
  return {
    id: Number(item.id) || 0,
    bizSite: item.bizSite || '',
    bizDimension: item.bizDimension || '',
    shortName: item.shortName || '',
    teamStatus: item.teamStatus || '',
    teamLink: item.teamLink || null,
    owner: Array.isArray(item.owner) ? item.owner : [],
    timeRange: item.timeRange || {},
    teamId: item.teamId || '',
    lastedTime: item.lastedTime || '',
  };
};

export const ListInfo = async (params: ListInfoParams): Promise<FeatMapItem[]> => {
  // 过滤掉 undefined 和空字符串的参数
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([key, value]) => value !== undefined && value !== ''),
  );

  const response = await fetch('/gateway/live/control/common/api/invoke', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: jsonStringify({
      api: 'api/team/feat/map/info',
      param: jsonStringify(filteredParams),
    }),
  });

  const result: BaseResponse<string> = await response.json();
  const parsedData: FeatMapItem[] = jsonParse(result.data, []);
  return parsedData.map(transformToFeatMapItem);
};

//根据标签查询
export const ListInfoByTag = async (params: ListInfoByTagParams): Promise<FeatMapItem[]> => {
  // 过滤掉 undefined 和空字符串的参数
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([key, value]) => value !== undefined && value !== ''),
  );
  const response = await fetch('/gateway/live/control/common/api/invoke', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: jsonStringify({
      api: 'api/team/feat/map/tag/query',
      param: jsonStringify(filteredParams),
    }),
  });

  const result: BaseResponse<string> = await response.json();
  const parsedData: FeatMapItem[] = jsonParse(result.data, []);
  return parsedData.map(transformToFeatMapItem);
};
