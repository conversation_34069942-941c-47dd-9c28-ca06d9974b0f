// 标注页面信息获取：视频流、商品信息
export const LabelInfo = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/label/info",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("LabelInfo：", result);
    return JSON.parse(result.data);
}

export const OnSaleItemUpdate = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/label/onSaleItem",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("itemInfos：", result);
    return JSON.parse(result.data);
}

//新增标注记录
export const LabelInsert = async (params: any) => {
    console.log("params:",params)
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/label/insert",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("---Insert:", result);
    return result;
}

// 查询标注历史记录
export const LabelHistory = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/label/history",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:",params)
    console.log("LabelHistory:", result);
    return JSON.parse(result.data);
}

// 根据视频实时返回UTC时间
export const LabelUTCTime = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/labelUTC/query",
            param: JSON.stringify(params)
        }),
    });

    const result = await response.json();
    console.log("LabelUTCTime 完整响应:", result);

    return {
        ...result,
        data: result.data ? JSON.parse(result.data) : null
    };
}


// 删除标注历史记录
export const LabelDelete = async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            api: "api/team/ai/live/record/label/delete",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("Delete:", result);
    return result.data;
}