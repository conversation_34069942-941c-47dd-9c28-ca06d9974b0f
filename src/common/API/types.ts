export interface TableRow {
  position: string;
}

// API 通用响应结构
export interface BaseResponse<T = unknown> {
  code: number;
  message: string;
  data: T;
}

// 导入现有的 FeatDetailRow 类型
import type { FeatDetailRow } from '@/components/Tables/FeatMap/FeatDetailTable';

// 使用类型别名确保一致性
export type FeatMapItem = FeatDetailRow;

// API 请求参数
export interface ListInfoParams {
  startTime?: string;
  endTime?: string;
  [key: string]: unknown;
}

export interface ListInfoByTagParams {
  [key: string]: string | number | undefined;

  productType?: string;
  activityType?: string;
  productAttr?: string;
  userType?: string;
  userRight?: string;
  channel?: string;
  position?: string;
}
