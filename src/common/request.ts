import { pc } from '@es/request/pc';
import { genTraceId } from '@es/traceid';
import { weblog } from './weblog';
import { message } from '@m-ui/react';

/**
 * HTTP状态码-成功
 *
 * @const
 * @type {number}
 */
const HTTP_STATUS_CODE_OK = 200;

/**
 * 业务请求成功码
 *
 * @const
 * @type {number}
 */
const SUCCESS_RESULT = 1;

// 网络库配置，详见：https://npm.corp.kuaishou.com/-/web/detail/@es/request
const requestInstance = pc.create({
  headers: {
    'Content-Type': 'application/json',
  },
  logConfig: {
    logger: weblog,
  },
  errorConfig: {
    showError(error: any) {
      const errorMsg = '网络请求超时，请检查您的网络';
      if (`timeout of ${requestInstance.defaults?.timeout}ms exceeded` === error?.message) {
        message.error(errorMsg);
        return Promise.reject(new Error(errorMsg));
      } else {
        message.error(
          error.errorMsg ||
            error.message ||
            error.msg ||
            error.error_msg ||
            error.errMsg ||
            '系统错误',
        );
        return Promise.reject(error);
      }
    },
  },
  formatResponse(response: any) {
    const data = typeof response.data === 'object' && response.data ? response.data : {};
    // 错误码治理 如果服务端下发了错误文案 则将Code｜result拼接在错误文案后
    const error_msg = data.error_msg || data.errorMsg;
    data.error_msg = error_msg ? `${error_msg} [${data.code || data.result || 0}]` : error_msg;
    const res = {
      ...data,
      result: data.result,
      error_msg: data.error_msg,
    };
    if (response.status === HTTP_STATUS_CODE_OK && data.result === SUCCESS_RESULT) {
      return res;
    }
    return Promise.reject(data);
  },
});

requestInstance.defaults.headers.common.Accept = 'application/json';
requestInstance.defaults.xsrfCookieName = 'ks-s-ctn';
requestInstance.defaults.xsrfHeaderName = 'ks-s-ctn';
requestInstance.defaults.withCredentials = true;
requestInstance.defaults.timeout = 30 * 1000;

requestInstance.interceptors.request.use((config: any) => {
  config.headers['Trace-Id'] = genTraceId();
  config.metadata = { startTime: +new Date() };
  return config;
});

export default requestInstance;
