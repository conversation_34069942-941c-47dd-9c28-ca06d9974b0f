const ua = navigator.userAgent.toLowerCase();

export const isChrome = navigator.userAgent.indexOf('Chrome') > -1 && window.chrome;

export const getMode: () => string | undefined = () => import.meta.env?.MODE;

//@ts-ignore
export const isDevelopmentEnv: boolean = process.env.NODE_ENV === 'development';

// 如何判断应用运行在staging、prt还是线上环境，强烈建议通过域名判断
export const isStaging = location.host.indexOf('staging') !== -1;
export const isPrt = location.host.indexOf('prt') !== -1;
export const isProd = !(isStaging || isPrt || isDevelopmentEnv);

/**
 * 是否高级浏览器，以下浏览器都是高级浏览
 * edge浏览器、opera浏览器、chrome浏览器、firefox浏览器
 */
export const isAdvancedBrowser = (() => {
  if (/applewebkit/g.test(ua)) {
    // edge浏览器
    if (/edge/g.test(ua)) {
      return true;
    } else if (/opr/g.test(ua)) {
      // opera浏览器
      return true;
    } else if (/chrome/g.test(ua)) {
      // chrome浏览器
      return true;
    } else if (/safari/g.test(ua)) {
      // safari浏览器
      return false;
    }
  } else if (/gecko/g.test(ua) && /firefox/g.test(ua)) {
    // firefox浏览器 gecko内核
    return true;
  } else if (/presto/g.test(ua)) {
    // presto内核 opera浏览器
    return true;
  }
  return false;
})();
