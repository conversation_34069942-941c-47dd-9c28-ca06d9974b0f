// src/interfaces.ts

/**
 * 顶部 FilterBar 组件需要的接口
 */

interface ScheduleListVO {
  startTime: string;
  endTime: string;
  teamGroup: string;
  strict: boolean;
}
export interface Params{
  scheduleListVO: ScheduleListVO;
  historyPeriod:number;
  intervalType:string;
}

/** 卡片组 */
export interface StatsData {
  name: string;  //卡片名称
  value: number; //数量
  history:number[] | null;
}

export interface PdStatsData{
  totalPd:number;
  history:number[] | null;
  internPd:number;
  onLoanPd:number;
  overtimePd: number;
}

// 定义单个团队成员的接口
export interface TeamMember {
  userName: string;
  name: string;
  avatar: string;
  role: string;
}

// 定义 headCountStats 的接口
export interface HeadCountStatsData {
  totalCount: number;
  history: number[] | null;
  teamMembers: TeamMember[];
}

export interface PieChartDataItem {
  name: string;
  value: number;
}

/**
 * TeamCategoriesTable 中，表格行的接口
 */
export interface TeamCategoryRow {
  /** 大类，比如 “C端用户产品需求” 或 “外部需求” 或 “技术需求”*/
  category: string;
  /** 总数量*/
  totalQuantity: number;
  /** 投入 PD，如 35、15 等 */
  totalPdCount: number;
  /** 需求来源列表 */
  sources: Array<{ category: string; sourceName: string; quantity: number; pdCount: number }>;
}

/**
 * TeamDetailsTable 中，表格行的接口
 */

export interface TimeRange{
  startDate:string;
  endDate:string;
}
export interface TeamDetailRow {
  name: string;
  teamLink: string;
  owner: string[];
  status: string;
  category: string;
  pdCount: number;
  source: string;
  priority: string;
  timeRange: TimeRange;
  teamId:string;
}




