import {MatchedGood} from "@/components/Cards/MatchedGoodsCard";

export interface Frame4 {
    index: number;
    screenSnapshotUrls: string[];
    clipVideoUrl: string;
    screenSnapshotTimeStamp: number;
    screenSnapshotRelatedTime: number;
    frameEmbeddingRequest: any;
    frameEmbeddingResponse: any;
    asrText: string;
    candidateItemList: any[];
    topSimilarItemIds: any[];
    topSimilarItems: MatchedGood[];
    frame2imageList: FrameImageItem[]; // 新增：帧图片列表
    liveStreamId?: number; // 添加liveStreamId属性
}

export interface FrameImageItem {
    imageUrl: string;
    frameIndex: number; // 1为是讲品帧，0为非讲品帧
}
