import {MatchedGood} from "@/components/Cards/MatchedGoodsCard";

export interface Frame {
    index: number;
    screenSnapshotUrls: string[];
    clipVideoUrl: string;
    screenSnapshotTimeStamp: number;
    screenSnapshotRelatedTime: number;
    frameEmbeddingRequest: any;
    frameEmbeddingResponse: any;
    asrText: string;
    candidateItemList: any[];
    topSimilarItemIds: any[];
    topSimilarItems: MatchedGood[];
    liveStreamId?: number; // 添加liveStreamId属性
}