import React, { useState, useEffect } from 'react';
import { Drawer, Typography, Spin, message } from 'antd';
import { LiveStreamInfoTable, type LiveStreamInfo } from './LiveStreamInfoTable';

interface LiveStreamInfoDrawerProps {
  visible: boolean;
  onClose: () => void;
  fetchLiveStreamInfo?: () => Promise<LiveStreamInfo[]>;
  onLiveStreamIdClick?: (liveStreamId: number) => void; // 增加了 onLiveStreamIdClick 的定义
}

export const LiveStreamInfoDrawer: React.FC<LiveStreamInfoDrawerProps> = ({
  visible,
  onClose,
  fetchLiveStreamInfo,
  onLiveStreamIdClick, // 增加了 onLiveStreamIdClick 的参数
}) => {
  const [loading, setLoading] = useState(false);
  const [liveStreamInfo, setLiveStreamInfo] = useState<LiveStreamInfo[]>([]);

  // 当抽屉打开时获取数据
  useEffect(() => {
    if (visible && fetchLiveStreamInfo) {
      setLoading(true);
      fetchLiveStreamInfo()
        .then((data) => {
          setLiveStreamInfo(data);
        })
        .catch((error) => {
          console.error('获取主播列表失败:', error);
          message.error('获取主播列表失败');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible, fetchLiveStreamInfo]);

  return (
    <Drawer
      title={
        <Typography.Text strong style={{ fontSize: 16 }}>
          主播列表 ({liveStreamInfo.length} 个)
        </Typography.Text>
      }
      placement="right"
      size="large"
      open={visible}
      onClose={onClose}
      destroyOnClose
      styles={{
        body: { padding: 0 },
      }}
    >
      <div style={{ padding: '16px' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" tip="加载中..." />
          </div>
        ) : (
          <LiveStreamInfoTable
            data={liveStreamInfo}
            loading={loading}
            onLiveStreamIdClick={onLiveStreamIdClick}
          />
        )}
      </div>
    </Drawer>
  );
};
