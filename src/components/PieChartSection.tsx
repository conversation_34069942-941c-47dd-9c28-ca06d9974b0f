import React, {useMemo, useState} from 'react';
import {Card, Tabs} from 'antd';
import {Pie} from '@ant-design/charts';
import type {PieChartDataItem} from '@/interfaces/interfaces';

interface PieChartSectionProps {
  title: string;
  demandData: PieChartDataItem[];
  pdData: PieChartDataItem[];
  priorityData: PieChartDataItem[];
}

const { TabPane } = Tabs;

export const PieChartSection: React.FC<PieChartSectionProps> = React.memo(({ title, demandData, pdData, priorityData }) => {
  const [currentTab, setCurrentTab] = useState('1');

  // 更新当前 tab 数据
  const handleTabChange = (key: string) => {
    setCurrentTab(key);
  };

  // 动态设置图表的数据
  const chartData = useMemo(() => {
    switch (currentTab) {
      case '1':
        return demandData;
      case '2':
        return pdData;
      case '3':
        return priorityData;
      default:
        return demandData;
    }
  }, [currentTab, demandData, pdData, priorityData]);

  // 图表配置
  const config = useMemo(() => {
    const total = chartData.reduce((sum, item) => sum + item.value, 0);
    return {
      appendPadding: 10,
      data: chartData.map((item) => ({
        type: item.name,
        value: item.value,
        percent: total > 0 ? item.value / total : 0,
      })),
      angleField: 'value',
      colorField: 'type',
      radius: 0.7,
      innerRadius: 0.4,
      interactions: [{ type: 'element-active' as const }],
      label: {
        text: (d: any) => {
          const percent = (d.percent * 100).toFixed(2) + '%';
          return `${d.type}\n${percent}`;
        },
        position: 'spider',
      },
      legend: false
    };
  }, [chartData]);

  return (
      <Card size="small" title={<span style={{ fontSize: 16 }}>{title}</span>} style={{ height: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Tabs defaultActiveKey="1" onChange={handleTabChange} style={{ marginLeft: 'auto' }}>
            <TabPane tab="需求数量" key="1" />
            <TabPane tab="PD数量" key="2" />
            <TabPane tab="优先级" key="3" />
          </Tabs>
        </div>
        <div style={{ height: '350px' }}> {/* 设置固定高度 */}
          <Pie {...config}  /> {/* 让饼图充满容器 */}
        </div>
      </Card>
  );
});
