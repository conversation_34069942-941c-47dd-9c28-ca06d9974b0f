import React, { useRef, useEffect, useState } from 'react';
import { Card, Typography, Space, Image, Button, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import type { ExtraOutput } from '@/types/script';

const { Text, Paragraph } = Typography;

// 从路径中提取值的函数
const extractValueFromPath = (data: any, path: string): any => {
  if (!path || !data) return null;
  
  // 移除 JSON. 前缀（如果存在）
  const cleanPath = path.replace(/^JSON\./, '');
  
  try {
    // 使用点分隔符拆分路径
    const keys = cleanPath.split('.');
    let result = data;
    
    for (const key of keys) {
      if (result === null || result === undefined) {
        return null;
      }
      result = result[key];
    }
    
    return result;
  } catch (error) {
    console.warn('提取数据失败:', error);
    return null;
  }
};

// 生成二维码URL的函数
const generateQRCodeUrl = (text: string): string => {
  // 使用在线二维码API生成二维码
  const encodedText = encodeURIComponent(text);
  return `https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=${encodedText}`;
};

// 简单的二维码组件
const SimpleQRCode: React.FC<{ value: string; size?: number }> = ({ value, size = 120 }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (canvasRef.current) {
      // 创建一个简单的二维码占位符
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        // 清除画布
        ctx.clearRect(0, 0, size, size);
        
        // 绘制边框
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.strokeRect(0, 0, size, size);
        
        // 绘制简单的格子图案
        ctx.fillStyle = '#000';
        const cellSize = size / 10;
        for (let i = 0; i < 10; i++) {
          for (let j = 0; j < 10; j++) {
            if ((i + j) % 2 === 0) {
              ctx.fillRect(i * cellSize, j * cellSize, cellSize, cellSize);
            }
          }
        }
        
        // 在中心绘制图标
        ctx.fillStyle = '#fff';
        ctx.fillRect(size/2 - 15, size/2 - 15, 30, 30);
        ctx.fillStyle = '#000';
        ctx.font = '20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('QR', size/2, size/2 + 5);
      }
    }
  }, [value, size]);

  return (
    <canvas 
      ref={canvasRef} 
      width={size} 
      height={size} 
      style={{ 
        border: '1px solid #d9d9d9', 
        borderRadius: '4px',
        backgroundColor: 'white'
      }} 
    />
  );
};

interface ExtraOutputRendererProps {
  extraOutputs: ExtraOutput[];
  resultData: any;
}

const ExtraOutputRenderer: React.FC<ExtraOutputRendererProps> = ({ extraOutputs, resultData }) => {
  
  // 从结果数据中提取值
  const extractValue = (jsonPath: string, data: any): any => {
    try {
      // 处理 JSON.xxx 格式的路径
      if (jsonPath.startsWith('JSON.')) {
        const path = jsonPath.substring(5); // 移除 'JSON.' 前缀
        return extractValueFromPath(data, path);
      }
      // 直接使用路径
      return extractValueFromPath(data, jsonPath);
    } catch (error) {
      console.error('提取值失败:', error);
      return null;
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 渲染不同类型的输出
  const renderOutputValue = (type: string, value: any) => {
    const stringValue = String(value);

    switch (type) {
      case 'qrcode':
        return (
          <QRCodeRenderer value={stringValue} />
        );

      case 'image':
        return (
          <ImageRenderer value={stringValue} />
        );

      case 'imageList':
        return (
          <ImageListRenderer value={value} />
        );

      case 'link':
        return (
          <div style={{ marginBottom: '8px' }}>
            <a 
              href={stringValue} 
              target="_blank" 
              rel="noopener noreferrer"
              style={{ 
                color: '#1890ff', 
                textDecoration: 'none',
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              <span>{stringValue}</span>
              <Button 
                size="small" 
                icon={<CopyOutlined />} 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  copyToClipboard(stringValue);
                }}
                style={{ fontSize: '10px', padding: '0 4px', height: '20px' }}
              >
                复制
              </Button>
            </a>
          </div>
        );

      case 'json':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <pre style={{
              backgroundColor: '#f5f5f5',
              padding: '8px',
              borderRadius: '4px',
              fontSize: '12px',
              maxHeight: '150px',
              overflow: 'auto',
              margin: 0,
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-all'
            }}>
              {typeof value === 'object' ? JSON.stringify(value, null, 2) : stringValue}
            </pre>
            <Button 
              size="small" 
              icon={<CopyOutlined />} 
              onClick={() => copyToClipboard(typeof value === 'object' ? JSON.stringify(value, null, 2) : stringValue)}
            >
              复制JSON
            </Button>
          </Space>
        );

      case 'text':
      default:
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Paragraph 
              copyable 
              style={{ 
                margin: 0, 
                padding: '8px', 
                backgroundColor: '#f9f9f9', 
                borderRadius: '4px',
                wordBreak: 'break-all'
              }}
            >
              {stringValue}
            </Paragraph>
          </Space>
        );
    }
  };

  if (!extraOutputs || extraOutputs.length === 0) {
    return null;
  }

  return (
    <div style={{ marginBottom: '16px' }}>
      {/*<Text strong style={{ fontSize: '16px', marginBottom: '12px', display: 'block' }}>*/}
      {/*  额外输出:*/}
      {/*</Text>*/}
      <div>
        {extraOutputs.map((output, index) => {
          const value = extractValue(output.valueJsonPath, resultData);
          
          if (value === null || value === undefined) {
            return (
              <div key={index} style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'flex-start',
                minWidth: '120px',
                marginBottom: '16px'
              }}>
                <Text strong style={{ fontSize: '14px', marginBottom: '8px' }}>
                  {output.name}:
                </Text>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  无数据
                </Text>
              </div>
            );
          }

          // imageList 类型独占一行
          if (output.type === 'imageList') {
            return (
              <div key={index} style={{ 
                display: 'block',
                width: '100%',
                marginBottom: '16px'
              }}>
                <Text strong style={{ fontSize: '14px', marginBottom: '8px', display: 'block' }}>
                  {output.name}:
                </Text>
                {renderOutputValue(output.type, value)}
              </div>
            );
          }

          // 其他类型保持原有的 flex 布局
          return (
            <div key={index} style={{ 
              display: 'inline-block',
              verticalAlign: 'top',
              marginRight: '16px',
              marginBottom: '16px'
            }}>
              <Text strong style={{ fontSize: '14px', marginBottom: '8px', display: 'block' }}>
                {output.name}:
              </Text>
              {renderOutputValue(output.type, value)}
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 二维码渲染组件
const QRCodeRenderer: React.FC<{ value: string }> = ({ value }) => {
  const [imageError, setImageError] = useState(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', marginBottom: '12px' }}>
      <div style={{ position: 'relative', marginBottom: '8px' }}>
        {imageError ? (
          <SimpleQRCode value={value} size={120} />
        ) : (
          <Image
            src={generateQRCodeUrl(value)}
            alt="二维码"
            width={120}
            height={120}
            style={{ borderRadius: '4px' }}
            placeholder={<SimpleQRCode value={value} size={120} />}
            onError={() => setImageError(true)}
          />
        )}
      </div>
      <div style={{ width: '120px' }}>
        <Text 
          type="secondary" 
          style={{ 
            fontSize: '12px', 
            display: 'block',
            width: '100%',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            cursor: 'pointer',
            lineHeight: '16px'
          }}
          onClick={() => copyToClipboard(value)}
          title={value}
        >
          {value}
        </Text>
        <Button 
          size="small" 
          icon={<CopyOutlined />} 
          onClick={() => copyToClipboard(value)}
          style={{ marginTop: '4px', fontSize: '10px', padding: '0 4px', height: '20px' }}
        >
          复制
        </Button>
      </div>
    </div>
  );
};

// 图片渲染组件
const ImageRenderer: React.FC<{ value: string }> = ({ value }) => {
  const [imageError, setImageError] = useState(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  const fallbackElement = (
    <div style={{ 
      width: '120px', 
      height: '120px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center', 
      background: '#f5f5f5',
      borderRadius: '4px',
      fontSize: '12px',
      color: '#999'
    }}>
      图片加载失败
    </div>
  );

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', marginBottom: '12px' }}>
      <div style={{ marginBottom: '8px' }}>
        {imageError ? (
          fallbackElement
        ) : (
          <Image
            src={value}
            alt="图片"
            width={120}
            height={120}
            style={{ borderRadius: '4px', objectFit: 'cover' }}
            placeholder={
              <div style={{ 
                width: '120px', 
                height: '120px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                background: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#999'
              }}>
                加载中...
              </div>
            }
            onError={() => setImageError(true)}
          />
        )}
      </div>
      <div style={{ width: '120px' }}>
        <Text 
          type="secondary" 
          style={{ 
            fontSize: '12px', 
            display: 'block',
            width: '100%',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            cursor: 'pointer',
            lineHeight: '16px'
          }}
          onClick={() => copyToClipboard(value)}
          title={value}
        >
          {value}
        </Text>
        <Button 
          size="small" 
          icon={<CopyOutlined />} 
          onClick={() => copyToClipboard(value)}
          style={{ marginTop: '4px', fontSize: '10px', padding: '0 4px', height: '20px' }}
        >
          复制
        </Button>
      </div>
    </div>
  );
};

// 图片列表渲染组件
const ImageListRenderer: React.FC<{ value: any }> = ({ value }) => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 确保 value 是数组
  const imageList = Array.isArray(value) ? value : [];

  if (imageList.length === 0) {
    return (
      <div style={{ 
        padding: '16px', 
        textAlign: 'center', 
        color: '#999',
        fontSize: '12px',
        border: '1px dashed #d9d9d9',
        borderRadius: '4px'
      }}>
        暂无图片
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'flex-start', 
      marginBottom: '12px',
      // maxWidth: '400px'
    }}>
      <div style={{ 
        display: 'flex',
        flexDirection: 'row',
        gap: '8px',
        width: '100%',
        marginBottom: '12px',
        overflowX: 'auto',
        overflowY: 'hidden',
        whiteSpace: 'nowrap',
        paddingBottom: '8px',
        scrollbarWidth: 'thin'
      }}>
        {imageList.map((imageUrl: string, index: number) => (
          <ImageListItem 
            key={index} 
            imageUrl={String(imageUrl)} 
            index={index}
            onCopy={copyToClipboard}
          />
        ))}
      </div>
      <div style={{ width: '100%' }}>
        <Button 
          size="small" 
          icon={<CopyOutlined />} 
          onClick={() => copyToClipboard(JSON.stringify(imageList, null, 2))}
          style={{ fontSize: '10px', padding: '0 8px', height: '24px' }}
        >
          复制所有链接
        </Button>
      </div>
    </div>
  );
};

// 单个图片项组件
const ImageListItem: React.FC<{ 
  imageUrl: string; 
  index: number; 
  onCopy: (text: string) => void;
}> = ({ imageUrl, index, onCopy }) => {
  const [imageError, setImageError] = useState(false);

  const fallbackElement = (
    <div style={{ 
      width: '120px',
      height: '120px',
      display: 'flex', 
      flexDirection: 'column',
      alignItems: 'center', 
      justifyContent: 'center', 
      background: '#f5f5f5',
      borderRadius: '4px',
      fontSize: '10px',
      color: '#999',
      textAlign: 'center',
      padding: '4px'
    }}>
      <div>图片{index + 1}</div>
      <div>加载失败</div>
    </div>
  );

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center',
      position: 'relative'
    }}>
      <div style={{ 
        marginBottom: '4px',
        position: 'relative',
        width: '120px',
        height: '120px'
      }}>
        {imageError ? (
          fallbackElement
        ) : (
          <Image
            src={imageUrl}
            alt={`图片${index + 1}`}
            width={120}
            height={120}
            style={{ 
              borderRadius: '4px', 
              objectFit: 'cover',
              cursor: 'pointer'
            }}
            placeholder={
              <div style={{ 
                width: '100px', 
                height: '100px', 
                display: 'flex', 
                flexDirection: 'column',
                alignItems: 'center', 
                justifyContent: 'center', 
                background: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '10px',
                color: '#999'
              }}>
                <div>图片{index + 1}</div>
                <div>加载中...</div>
              </div>
            }
            onError={() => setImageError(true)}
            preview={{
              mask: (
                <div style={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center',
                  fontSize: '10px'
                }}>
                  <div>查看</div>
                  <div>图片{index + 1}</div>
                </div>
              )
            }}
          />
        )}
      </div>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: '4px',
        width: '100px'
      }}>
        <Text 
          type="secondary" 
          style={{ 
            fontSize: '10px',
            flex: 1,
            textAlign: 'center',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}
          title={imageUrl}
        >
          图片{index + 1}
        </Text>
        <Button 
          size="small" 
          icon={<CopyOutlined />} 
          onClick={() => onCopy(imageUrl)}
          style={{ 
            fontSize: '8px', 
            padding: '0 2px', 
            height: '16px',
            minWidth: '16px'
          }}
          title="复制链接"
        />
      </div>
    </div>
  );
};

export default ExtraOutputRenderer;
