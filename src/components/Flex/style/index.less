@flex-prefix-cls: ~'@{ant-prefix}-flex';

.@{flex-prefix-cls} {
  display: flex;
  margin: 0;
  padding: 0;

  &-vertical {
    flex-direction: column;
  }

  &-rtl {
    direction: rtl;
  }

  &:empty {
    display: none;
  }

  /*==================gap===================*/
  &-gap-small {
    gap: var(--flex-gap-sm, var(--spacing-3, 8px));
  }
  &-gap-middle {
    gap: var(--flex-gap, var(--spacing-5, 16px));
  }
  &-gap-large {
    gap: var(--flex-gap-lg, var(--spacing-6, 24px));
  }

  /*==================wrap===================*/
  @flex-wrap-values: wrap, nowrap, wrap-reverse;

  .loop-flex-wrap(@i) when (@i < length(@flex-wrap-values)) {
    @value: extract(@flex-wrap-values, @i);

    &-wrap-@{value} {
      flex-wrap: @value;
    }
    .loop-flex-wrap(@i + 1);
  }

  .loop-flex-wrap(1);

  /*==================align===================*/
  @align-items-values: center, start, end, flex-start, flex-end, self-start, self-end, baseline,
    normal, stretch;

  .loop-align-items(@i) when (@i < length(@align-items-values)) {
    @value: extract(@align-items-values, @i);

    &-align-@{value} {
      align-items: @value;
    }

    .loop-align-items(@i + 1);
  }

  .loop-align-items(1);

  /*==================justify===================*/
  @justify-content-values: flex-start, flex-end, start, end, center, space-between, space-around,
    space-evenly, stretch, normal, left, right;

  .loop-justify-content(@i) when (@i < length(@justify-content-values)) {
    @value: extract(@justify-content-values, @i);

    &-justify-@{value} {
      justify-content: @value;
    }
    .loop-justify-content(@i + 1);
  }

  .loop-justify-content(1);
}
