import React, { useState, useCallback } from 'react';
import { Button, message, Space } from 'antd';
import { CopyOutlined, DownOutlined, RightOutlined, ExpandOutlined, ShrinkOutlined } from '@ant-design/icons';

// 主题类型定义
export type JsonViewerTheme = 'dark' | 'light';

// 主题配置
const themes = {
  dark: {
    background: '#1a1a1a',
    border: '#3b3a32',
    text: '#f8f8f2',
    keyColor: '#FF6B6B',
    stringColor: '#4ECDC4',
    numberColor: '#FFEAA7',
    booleanColor: '#A29BFE',
    nullColor: '#808080',
    punctuationColor: '#DDA0DD',
    commentColor: '#6C7B7F',
    hoverBg: '#1890ff20'
  },
  light: {
    background: '#ffffff',
    border: '#d9d9d9',
    text: '#262626',
    keyColor: '#d32f2f',
    stringColor: '#2e7d32',
    numberColor: '#f57c00',
    booleanColor: '#7b1fa2',
    nullColor: '#757575',
    punctuationColor: '#8e24aa',
    commentColor: '#9e9e9e',
    hoverBg: '#1890ff10'
  }
};

interface JsonViewerProps {
  data: any;
  title?: string;
  theme?: JsonViewerTheme;
}

interface JsonNodeProps {
  data: any;
  keyName?: string;
  level?: number;
  isLast?: boolean;
  globalCollapsedLevels?: Set<number>;
  onToggleLevel?: (level: number) => void;
  themeColors: typeof themes.dark;
}

const JsonNode: React.FC<JsonNodeProps> = ({
  data,
  keyName,
  level = 0,
  isLast = true,
  globalCollapsedLevels = new Set(),
  onToggleLevel,
  themeColors
}) => {
  const [localCollapsed, setLocalCollapsed] = useState(false);

  // 检查当前层级是否被全局折叠
  const isGloballyCollapsed = globalCollapsedLevels.has(level);
  const collapsed = isGloballyCollapsed || localCollapsed;
  
  const indent = level * 20;
  const isObject = typeof data === 'object' && data !== null && !Array.isArray(data);
  const isArray = Array.isArray(data);
  const isExpandable = isObject || isArray;
  
  const getValueColor = (value: any) => {
    if (value === null) return themeColors.nullColor;
    if (typeof value === 'string') return themeColors.stringColor;
    if (typeof value === 'number') return themeColors.numberColor;
    if (typeof value === 'boolean') return themeColors.booleanColor;
    return themeColors.text;
  };
  const renderValue = (value: any) => {
    if (value === null) return 'null';
    if (typeof value === 'string') return `"${value}"`;
    return String(value);
  };
  
  const copyValue = (value: any) => {
    const text = typeof value === 'string' ? value : JSON.stringify(value, null, 2);
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    });
  };

  const handleToggleCollapse = () => {
    if (isGloballyCollapsed && onToggleLevel) {
      // 如果是全局折叠状态，点击时取消该层级的全局折叠
      onToggleLevel(level);
    } else {
      // 否则切换本地折叠状态
      setLocalCollapsed(!localCollapsed);
    }
  };
  
  if (!isExpandable) {
    return (
      <div style={{
        marginLeft: indent,
        fontFamily: 'Monaco, Consolas, monospace',
        fontSize: '12px',
        wordBreak: 'break-all',
        maxWidth: '100%',
        overflow: 'hidden',
        color: themeColors.text
      }}>
        {keyName && (
          <span style={{ color: themeColors.keyColor }}>"{keyName}": </span>
        )}
        <span
          style={{
            color: getValueColor(data),
            cursor: 'pointer',
            userSelect: 'text',
            wordBreak: 'break-all'
          }}
          onClick={() => copyValue(data)}
          title="点击复制"
        >
          {renderValue(data)}
        </span>
        {!isLast && <span style={{ color: themeColors.punctuationColor }}>,</span>}
      </div>
    );
  }
  
  const entries = isArray ? data.map((item: any, index: number) => [index, item]) : Object.entries(data);
  
  return (
    <div style={{
      marginLeft: indent,
      fontFamily: 'Monaco, Consolas, monospace',
      fontSize: '12px',
      maxWidth: '100%',
      overflow: 'hidden',
      color: themeColors.text
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        maxWidth: '100%',
        overflow: 'hidden'
      }}>
        <Button
          type="text"
          size="small"
          icon={collapsed ? <RightOutlined /> : <DownOutlined />}
          onClick={handleToggleCollapse}
          style={{
            padding: '0 4px',
            minWidth: 'auto',
            height: '16px',
            backgroundColor: isGloballyCollapsed ? themeColors.hoverBg : 'transparent'
          }}
          title={isGloballyCollapsed ? '该层级已全局折叠，点击取消' : '点击折叠/展开'}
        />
        {keyName && (
          <span style={{ color: themeColors.keyColor }}>"{keyName}": </span>
        )}
        <span style={{ color: themeColors.punctuationColor }}>
          {isArray ? '[' : '{'}
          {collapsed && (
            <span style={{ color: themeColors.commentColor, fontStyle: 'italic' }}>
              {isArray ? ` ${data.length} items ` : ` ${entries.length} keys `}
            </span>
          )}
          {collapsed && (isArray ? ']' : '}')}
        </span>
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={() => copyValue(data)}
          style={{ padding: '0 4px', minWidth: 'auto', height: '16px' }}
          title="复制此对象"
        />
      </div>
      
      {!collapsed && (
        <>
          {entries.map(([key, value], index) => (
            <JsonNode
              key={key}
              data={value}
              keyName={isArray ? undefined : String(key)}
              level={level + 1}
              isLast={index === entries.length - 1}
              globalCollapsedLevels={globalCollapsedLevels}
              onToggleLevel={onToggleLevel}
              themeColors={themeColors}
            />
          ))}
          <div style={{ marginLeft: indent + 20, color: themeColors.punctuationColor }}>
            {isArray ? ']' : '}'}
            {!isLast && ','}
          </div>
        </>
      )}
    </div>
  );
};

const JsonViewer: React.FC<JsonViewerProps> = ({ data, title, theme = 'dark' }) => {
  const [collapsedLevels, setCollapsedLevels] = useState<Set<number>>(new Set());
  const themeColors = themes[theme];

  const copyAll = () => {
    const text = JSON.stringify(data, null, 2);
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制完整JSON到剪贴板');
    });
  };

  // 获取JSON的最大深度
  const getMaxDepth = useCallback((obj: any, currentDepth = 0): number => {
    if (obj === null || typeof obj !== 'object') {
      return currentDepth;
    }

    const values = Array.isArray(obj) ? obj : Object.values(obj);
    if (values.length === 0) {
      return currentDepth;
    }

    return Math.max(...values.map(value => getMaxDepth(value, currentDepth + 1)));
  }, []);

  const maxDepth = getMaxDepth(data);

  // 折叠指定层级及其以下所有层级
  const collapseFromLevel = (level: number) => {
    const newCollapsedLevels = new Set<number>();
    for (let i = level; i <= maxDepth; i++) {
      newCollapsedLevels.add(i);
    }
    setCollapsedLevels(newCollapsedLevels);
  };

  // 展开指定层级及其以下所有层级
  const expandToLevel = (level: number) => {
    const newCollapsedLevels = new Set(collapsedLevels);
    for (let i = 0; i <= level; i++) {
      newCollapsedLevels.delete(i);
    }
    setCollapsedLevels(newCollapsedLevels);
  };

  // 切换指定层级的折叠状态
  const toggleLevel = (level: number) => {
    const newCollapsedLevels = new Set(collapsedLevels);
    if (newCollapsedLevels.has(level)) {
      newCollapsedLevels.delete(level);
    } else {
      newCollapsedLevels.add(level);
    }
    setCollapsedLevels(newCollapsedLevels);
  };

  // 全部展开
  const expandAll = () => {
    setCollapsedLevels(new Set());
  };

  // 全部折叠（只显示第一层）
  const collapseAll = () => {
    const newCollapsedLevels = new Set<number>();
    for (let i = 1; i <= maxDepth; i++) {
      newCollapsedLevels.add(i);
    }
    setCollapsedLevels(newCollapsedLevels);
  };
  
  return (
    <div style={{
      background: themeColors.background,
      border: `1px solid ${themeColors.border}`,
      borderRadius: '4px',
      padding: '12px',
      height: '100%', // 改为使用100%高度而不是固定高度
      overflow: 'hidden', // 改为hidden，让内部滚动容器控制滚动
      width: '100%',
      maxWidth: '100%',
      boxSizing: 'border-box',
      display: 'flex',
      flexDirection: 'column',
      color: themeColors.text
    }}>
      {title && (
        <div style={{
          marginBottom: '12px',
          paddingBottom: '12px',
          borderBottom: `1px solid ${themeColors.border}`,
          flexShrink: 0 // 防止标题区域被压缩
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '8px'
          }}>
            <span style={{ color: themeColors.text, fontWeight: 'bold', fontSize: '14px' }}>{title}</span>
            <Button
              type="primary"
              size="small"
              icon={<CopyOutlined />}
              onClick={copyAll}
            >
              复制全部
            </Button>
          </div>

          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', alignItems: 'center' }}>
            <span style={{ color: '#999', fontSize: '12px' }}>层级控制:</span>
            <Space size="small">
              <Button
                size="small"
                icon={<ExpandOutlined />}
                onClick={expandAll}
                title="展开所有层级"
              >
                全部展开
              </Button>
              <Button
                size="small"
                icon={<ShrinkOutlined />}
                onClick={collapseAll}
                title="折叠到第一层"
              >
                全部折叠
              </Button>
            </Space>

            <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>快速展开到:</span>
            <Space size="small">
              {Array.from({ length: Math.min(maxDepth + 1, 5) }, (_, i) => (
                <Button
                  key={i}
                  size="small"
                  type={!collapsedLevels.has(i + 1) && collapsedLevels.has(i + 2) ? 'primary' : 'default'}
                  onClick={() => {
                    if (i === 0) {
                      collapseFromLevel(1);
                    } else {
                      expandToLevel(i);
                      collapseFromLevel(i + 1);
                    }
                  }}
                  title={`展开到第${i + 1}层`}
                >
                  L{i + 1}
                </Button>
              ))}
            </Space>
          </div>
        </div>
      )}
      <div style={{
        flex: 1,
        overflow: 'auto', // 只在内容区域启用滚动
        minHeight: 0 // 允许flex子项缩小
      }}>
        <JsonNode
          data={data}
          globalCollapsedLevels={collapsedLevels}
          onToggleLevel={toggleLevel}
          themeColors={themeColors}
        />
      </div>
    </div>
  );
};

export default JsonViewer;
