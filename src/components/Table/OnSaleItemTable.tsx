// 渲染“主播讲解时间”列
import {formatSecondsToHMS} from "@/pages/AgentLive/constant";
import {ConfigProvider, Image, Table, Tooltip} from "antd";
import React, {useCallback, useMemo} from "react";
import {GoodItem} from "@/pages/AgentLive/Tabs/AgentLiveInfo";
import {KwaiPlayer} from "@ks-video/kwai-player-web/react";
import zhCN from "antd/es/locale/zh_CN";
import {ITEM_LINK_PREFIX} from "@/pages/StreamLabel/constant";
import {InfoCircleOutlined} from "@ant-design/icons";
import {ColumnsType} from "antd/lib/table";

export interface OnSaleItemTableProps {
    dataSource?: GoodItem[];
    playerRef: React.RefObject<KwaiPlayer | null>;
}
const OnSaleItemTable: React.FC<OnSaleItemTableProps> =({dataSource, playerRef}) => {

    // 定义跳转函数，使用共用的 playerRef
    const handleJump = useCallback((sec: number) => {
        const inst = playerRef.current as any;
        if (inst) inst.currentTime = sec;
    }, [playerRef]);

    // 渲染“商品图片”列
    const renderImageUrlsCell = (urls: string[]) => {
        const maxVisible = 3;
        const visible = urls.slice(0, maxVisible);
        const hidden = urls.slice(maxVisible);

        return (
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', gap: 8 }}>
                {visible.map((url, idx) => (
                    <Image
                        key={idx}
                        width={150}
                        height={150}
                        src={url}
                        style={{ marginBottom: hidden.length > 0 ? 4 : 0 }}
                    />
                ))}
                {hidden.length > 0 && (
                    <Tooltip
                        title={
                            <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                                {hidden.map((url, idx) => (
                                    <Image key={idx} width={150} height={150} src={url} />
                                ))}
                            </div>
                        }
                    >
          <span style={{ cursor: 'pointer', color: '#888', marginTop: 4 }}>
            +{hidden.length}
          </span>
                    </Tooltip>
                )}
            </div>
        );
    };

    // 渲染“主播讲解时间”列
    const renderAnchorRecordTimeRange = useCallback((_text: any, record: GoodItem) => {
        const { anchorRecordStartTime, anchorRecordEndTime } = record;

        if (anchorRecordStartTime == null || anchorRecordEndTime == null) {
            return (
                <div style={{ textAlign: 'center', width: '100%' }}>
                    -
                </div>
            );
        }

        const start = formatSecondsToHMS(anchorRecordStartTime);
        const end = formatSecondsToHMS(anchorRecordEndTime);

        return (
            <div style={{ whiteSpace: 'nowrap' }}>
                <Tooltip title={`点击定位`}>
                    <a onClick={() => handleJump(anchorRecordStartTime)}>{start}</a>
                </Tooltip>
                {' - '}
                <Tooltip title={`点击定位`}>
                    <a onClick={() => handleJump(anchorRecordEndTime)}>{end}</a>
                </Tooltip>
            </div>
        );
    }, [handleJump]);

    const columns = useMemo<ColumnsType<GoodItem>>(() => [
            {
                title: '序号',
                dataIndex: 'serialNumber',
                key: 'serialNumber',
                width: 50,
                render: (text:any) => (
                    <span style={{fontWeight:'bold',fontSize:'16'}}>{text}</span>
                ),
            },
            {
                title: '商品ID',
                dataIndex: 'itemId',
                key: 'itemId',
                width: 100,
                render: (itemId: number) => (
                    <a
                        href={`${ITEM_LINK_PREFIX}${itemId}`}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        {itemId}
                    </a>
                ),
            },
            {
                title: '商品标题',
                dataIndex: 'title',
                key: 'title',
                width: 100,
                render: (text: string) => (
                    <div style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
                        {text}
                    </div>
                ),
            },
            {
                title: (
                    <span> 主播讲解时间{' '}
                        <Tooltip
                            title={
                                <div style={{ fontSize: '12px', width: '150px' }}>
                                    主播在直播讲解时手动选择的开始讲解和结束讲解时间
                                </div>
                            }
                        >
                        <InfoCircleOutlined style={{ color: 'rgba(0,0,0,0.45)', cursor: 'pointer' }} />
                        </Tooltip>
                    </span>
                ),
                dataIndex: 'anchorRecordStartTime',
                key: 'anchorRecordTimeRange',
                width: 140,
                sorter: (a: GoodItem, b: GoodItem) => {
                    const aHasTime = a.anchorRecordStartTime != null;
                    const bHasTime = b.anchorRecordStartTime != null;

                    // 都有值 → 正常比较
                    if (aHasTime && bHasTime) {
                        return a.anchorRecordStartTime! - b.anchorRecordStartTime!;
                    }

                    // 一个有值，一个没值 → 有值排前
                    if (aHasTime && !bHasTime) return -1;
                    if (!aHasTime && bHasTime) return 1;

                    // 都没有值 → 不排序
                    return 0;
                },
                render:renderAnchorRecordTimeRange,
            },
            {
                title: '商品属性',
                dataIndex: 'itemCategoryPropInfo',
                key: 'itemCategoryPropInfo',
                width: 240,
                render: (info?: Record<string, string> | null) => {
                    const entries = Object.entries(info ?? {});
                    return (
                        <div style={{ whiteSpace: 'normal', wordBreak: 'break-all' }}>
                            {entries.map(([prop, val]) => (
                                <div key={prop}>
                                    {prop}: {val}
                                </div>
                            ))}
                        </div>
                    );
                },
            },
            {
                title: '商品图片',
                dataIndex: 'imageUrls',
                key: 'imageUrls',
                width: 160,
                render: renderImageUrlsCell,
            }
    ], [handleJump]);

    return(
        <ConfigProvider locale={zhCN}>
            <Table<GoodItem>
                columns={columns}
                dataSource={dataSource??[]}
                rowKey="itemId"
                pagination={{
                    pageSize: 100,
                    showSizeChanger: false, // 不允许用户修改
                    showQuickJumper: true,  // 可选：支持跳页
                    showTotal: (total, range) => `共 ${total} 条，当前显示第 ${range?.[0]}-${range?.[1]} 条`,
                }}
                size="middle"
                locale={{ emptyText: '暂无在车商品' }}
            />
        </ConfigProvider>
    )


}

export default OnSaleItemTable;