import React, {useState} from 'react';
import {Card, Popover, Table} from 'antd';
import type {ColumnsType} from 'antd/lib/table';
import type {TeamCategoryRow, TeamDetailRow} from '@/interfaces/interfaces';
import {PopoverDetailsTable} from "@/components/PopoverDetailsTable";

interface DataTableSectionProps {
  data: TeamCategoryRow[];
  teamDetails: TeamDetailRow[];
}

interface FlatDataItem {
  category: string;
  totalQuantity: number;
  totalPdCount: number;
  sourceName: string;
  quantity: number;
  pdCount: number;
  rowIndex: number;
  sourceIndex: number;
  sourceCount: number;
}

const QuantityPopoverButton: React.FC<{ text: number, data: TeamDetailRow[], fontSize?: string }> = ({ text, data, fontSize = '16px' }) => {
  const [popoverOpen, setPopoverOpen] = useState(false);

  return (
      <Popover
          content={<PopoverDetailsTable data={data} onClose={() => setPopoverOpen(false)} />}
          trigger="click"
          placement="right"
          open={popoverOpen}
          onOpenChange={(visible) => setPopoverOpen(visible)}
      >
        <button
            style={{
              cursor: 'pointer',
              color: "#1890ff",
              background: 'none',
              border: 'none',
              padding: '0',
              fontSize: fontSize // 使用传入的 fontSize 属性
            }}
            onClick={() => setPopoverOpen(true)}
        >
          {text}
        </button>
      </Popover>
  );
};


export const TeamCategoriesTable: React.FC<DataTableSectionProps> = ({ data, teamDetails }) => {
  const flatData: FlatDataItem[] = [];
  data.forEach((row, rowIndex) => {
    const sourceCount = row.sources?.length ?? 0;
    row.sources?.forEach((s, sourceIndex) => {
      flatData.push({
        category: row.category,
        totalQuantity: row.totalQuantity,
        totalPdCount: row.totalPdCount,
        sourceName: s.sourceName,
        quantity: s.quantity,
        pdCount: s.pdCount,
        rowIndex,
        sourceIndex,
        sourceCount,
      });
    });
  });


  // 排序数据，将“其他需求”项目移到最后
  const sortedFlatData = [...flatData].sort((a, b) => {
    if (a.category === '其他需求') return 1;
    if (b.category === '其他需求') return -1;
    return 0;
  });


  const renderCategoryCell = (text: any, record: FlatDataItem) => {
    const { sourceIndex, sourceCount, category } = record;
    if (sourceIndex === 0) {
      return {
        children: <span style={{ fontSize: '16px' }}>{category}</span>,
        props: {
          rowSpan: sourceCount,
        },
      };
    }
    return {
      children: null,
      props: {
        rowSpan: 0,
      },
    };
  };

  const renderQuantityCell = (text: number, record: FlatDataItem) => {
    const { category, sourceName } = record;
    const related: TeamDetailRow[] = teamDetails.filter(
        (r) => r.category === category && r.source === sourceName
    );
    return <QuantityPopoverButton text={text} data={related} />;
  };

  const renderTotalQuantityCell = (_: any, record: FlatDataItem) => {
    const { sourceIndex, sourceCount, totalQuantity, category } = record;
    if (sourceIndex === 0) {
      const related = teamDetails.filter((r) => r.category === category);
      return {
        children: <QuantityPopoverButton text={totalQuantity} data={related} fontSize="16px"/>,
        props: { rowSpan: sourceCount },
      };
    }
    return { children: null, props: { rowSpan: 0 } };
  };

  const renderTotalPdCountCell = (text: any, record: FlatDataItem) => {
    const { sourceIndex, sourceCount, totalPdCount } = record;
    if (sourceIndex === 0) {
      return {
        children: <span style={{ fontSize: '16px' }}>{totalPdCount}</span>,
        props: {
          rowSpan: sourceCount,
        },
      };
    }
    return {
      children: null,
      props: {
        rowSpan: 0,
      },
    };
  };

  const columns: ColumnsType<(typeof flatData)[0]> = [
    {
      title: '大类',
      dataIndex: 'category',
      key: 'category',
      width: 200,
      align: 'center',
      render: renderCategoryCell,
    },
    {
      title: '数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 200,
      align: 'center',
      render: renderTotalQuantityCell,
    },
    {
      title: '投入(PD)',
      dataIndex: 'totalPdCount',
      key: 'totalPdCount',
      width: 200,
      align: 'center',
      render: renderTotalPdCountCell,
    },
    {
      title: '需求来源',
      dataIndex: 'sourceName',
      key: 'sourceName',
      align: 'center',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 200,
      align: 'center',
      render: renderQuantityCell,
    },
    {
      title: '投入(PD)',
      dataIndex: 'pdCount',
      key: 'pdCount',
      width: 200,
      align: 'center',
    },
  ];

  return (
      <Card
          size="small"
          title={<span style={{ fontSize: 16 }}>需求类型构成</span>}
          style={{ height: '100%' }}
      >
        <Table columns={columns} dataSource={sortedFlatData} pagination={false} bordered size="small" />
      </Card>
  );
};
