// src/components/StatisticCard/StatisticCard.tsx

import React from 'react';
import {Card} from 'antd';
import './StatisticCard.css';

interface StatisticCardProps {
    title: string;
    value: number;
    changeValue: number;
}


const formatChangeValue = (changeValue: number) =>
    changeValue >= 0 ? `+${changeValue}` : `${changeValue}`;

export const StatisticCard: React.FC<StatisticCardProps> = React.memo(({ title, value, changeValue }) => {

    return (
        <div className="statistics-cards">
            <Card className="statistics-card">
                <div className="card-content">
                    <div className="card-left">
                        <div className="card-title">{title}</div>
                        <div className="card-value">{value}</div>
                        <div className="card-change">
                            <div className="change-value">变化值：{formatChangeValue(changeValue)}</div>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    );
});
