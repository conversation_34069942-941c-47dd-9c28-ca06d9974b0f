import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Avatar, Button, Card, Col, Drawer, Image, message, Modal, Row, Space, Table, Tag, Typography} from "antd";
import {
    CheckCircleOutlined,
    CloseCircleOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    MinusCircleOutlined,
    PictureOutlined,
    ShoppingCartOutlined
} from '@ant-design/icons';
import GoodCard from "@/components/Cards/GoodCard";
import * as API from '@/common/API/AgentLiveV2API';
import GoodCardAlgo from "@/components/Cards/GoodCardAlgo";

// 新增：在车商品接口返回的数据结构
export interface GoodItem {
    serialNumber?: number;
    itemId: number;
    title: string;
    imageUrl?: string;
    imageUrls?: string[];
    itemCategoryTree?: string;
    itemCategoryPropInfo?: Record<string, string | undefined>;
}

export interface MatchedGood {
    itemId: number | string
    score?: number | null
    goodDetail?: GoodDetail
    carItemIndex: number | string
}

export interface GoodDetail {
    itemId?: number
    itemDesc?: any
    itemCategoryTree?: any
    title: string
    imageUrl?: string
    imageUrls?: string[]
    itemCategoryPropInfo?: Record<string, string | undefined>;
}

// 新增：标注查询结果的类型定义
export interface LabelQueryResult {
    recodId: number;
    taskId: string;
    liveStreamId: number;
    sellerId: number;
    windowTimeStamp: number;
    relatedTimeStamp: number;
    itemId: number;
    level1CateId: number;
    level1CateName: string;
    labelFlag: string; // "none"-未识别到商品, "true"-识别正确, "false"-识别错误
    extraInfo: string; // JSON字符串，包含realItemId等信息
    sourceType: string; // "0"-mmu, "1"-tm
    modelVersion: string;
    operator: string;
    createTime: number;
    updateTime: number;
}

// 新增：extraInfo解析后的类型
export interface ExtraInfo {
    realItemId: number;
    level1CateId: number;
    level1CateName: string;
}

export interface MatchedGoodsCardProps {
    title: string
    currentItem?: MatchedGood        // 正在讲解的商品，可选
    matchedAlgo?: MatchedGood             // 算法策略匹配结果，可选
    matchedAgent?: MatchedGood            // Agent策略匹配结果，可选
    /** LLM 解释文本，可选 */
    llmAnswer: string;
    /** LLM 模型名称，可选 */
    llmModelName?: string;
    /** 直播流ID */
    liveStreamId: number;
    /** 当前时间戳 */
    timestamp: number;
    /** URL参数：mmuItemId */
    mmuItemId?: number;
    /** URL参数：tmItemId */
    tmItemId?: number;
    /** URL参数：taskId */
    taskId?: string;
    /** Agent请求信息 */
    agentRequest?: any;
    /** Agent响应信息 */
    agentResponse?: any;
    mode?: string;
    replayId?: number;
    replayModelVersion?: string;
    matchedReplay?: MatchedGood;
}

export interface LLMAnswerItem {
    index: number;
    consistent: boolean;
    score: number;
    itemId: number;
    explanation: string;
    referenceCategory: string;
    detectedCategory: string;
}

const { Text } = Typography;

const MatchedGoodsCard: React.FC<MatchedGoodsCardProps> = ({
                                                               title,
                                                               currentItem,
                                                               matchedAlgo,
                                                               matchedAgent,
                                                               llmAnswer,
                                                               llmModelName,
                                                               liveStreamId,
                                                               timestamp,
                                                               mmuItemId,
                                                               tmItemId,
                                                               taskId,
                                                               agentRequest,
                                                               agentResponse,
                                                               mode,
                                                               replayId,replayModelVersion,matchedReplay

                                                           }) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDrawerVisible, setIsDrawerVisible] = useState(false);
    const [correctItemId, setCorrectItemId] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    // 新增：在车商品列表相关状态
    const [onSaleItems, setOnSaleItems] = useState<GoodItem[]>([]);
    const [selectedItemId, setSelectedItemId] = useState<number | null>(null);
    const [loading, setLoading] = useState(false);

    // 新增：标注查询相关状态
    const [labelQueryResult, setLabelQueryResult] = useState<LabelQueryResult | null>(null);
    const [labelQueryLoading, setLabelQueryLoading] = useState(false);
    const [correctProductDetail, setCorrectProductDetail] = useState<GoodItem | null>(null);
    
    // 新增：用于跟踪是否已经查询过的引用
    const hasQueriedRef = useRef<string>('');

    // 新增：判断应该显示哪些商品卡片
    const showMmu = !!currentItem?.itemId;
    const showTm = !!matchedAlgo?.itemId;
    const showReplay = !!matchedReplay?.itemId;
    const showBoth = showMmu && showTm;
    // 新增：判断是否应该禁用标注
    const disableLabeling = (!!mmuItemId && !!tmItemId) || (!showMmu && !showTm);
    // 新增：判断是否不显示标注卡片
    const showNoLabelCard = showReplay;

    const placeholderStyle: React.CSSProperties = {
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#999',
        fontSize: 18,
    };

    const highlightAlgo =
        currentItem?.itemId != null &&
        matchedAlgo?.itemId != null &&
        currentItem.itemId === matchedAlgo.itemId;

    // Helper: 从 llmAnswer 字符串中提取 explanation
    const extractLLMExplanation = useCallback((llmAnswerStr: string): string | null => {
        if (!llmAnswerStr) return null;

        try {
            const arr = JSON.parse(llmAnswerStr) as Array<{ explanation: string }>;
            return arr?.[0]?.explanation ?? null;
        } catch (err) {
            console.error('Failed to parse llmAnswer:', err, llmAnswerStr);
            return null;
        }
    }, []);

    // 获取在车商品列表
    const updateDataList = useCallback(async (currentTimeInSeconds: number): Promise<GoodItem[]> => {
        try {
            const params = {
                liveStreamId,
                specifiedTime: currentTimeInSeconds,
            }
            const result = await API.OnSaleItemUpdate(params);
            const updatedData = result.itemInfos;
            return updatedData;
        } catch (error) {
            console.error('API调用失败', error);
            message.error('获取数据失败，请重试');
            return [];
        }
    }, [liveStreamId]);

    // 新增：获取正确商品信息的辅助函数
    const getCorrectProductInfo = useCallback(async (labelData: LabelQueryResult, onSaleItems: GoodItem[]): Promise<GoodItem | null> => {
        if (labelData.labelFlag === "false" && labelData.extraInfo) {
            // 识别错误情况：从extraInfo中获取正确商品ID
            try {
                const extraInfo: ExtraInfo = JSON.parse(labelData.extraInfo);
                const correctProduct = onSaleItems.find(item => item.itemId === extraInfo.realItemId);
                if (correctProduct) {
                    console.log('找到正确商品（错误标注）:', correctProduct);
                    return correctProduct;
                }
            } catch (error) {
                console.error('解析extraInfo失败:', error);
            }
        } else if (labelData.labelFlag === "true") {
            // 识别正确情况：获取标注的商品信息
            const labeledProduct = onSaleItems.find(item => item.itemId === labelData.itemId);
            if (labeledProduct) {
                console.log('找到标注商品（正确标注）:', labeledProduct);
                return labeledProduct;
            } else {
                // 如果在车商品中没找到，尝试从当前显示的商品中获取信息
                const currentDisplayedItem = matchedAlgo?.goodDetail || matchedAgent?.goodDetail || currentItem?.goodDetail;
                if (currentDisplayedItem && (
                    currentDisplayedItem.itemId === labelData.itemId ||
                    String(currentDisplayedItem.itemId) === String(labelData.itemId)
                )) {
                    // 将当前显示的商品信息转换为 GoodItem 格式
                    const convertedItem: GoodItem = {
                        itemId: currentDisplayedItem.itemId || labelData.itemId,
                        title: currentDisplayedItem.title,
                        imageUrl: currentDisplayedItem.imageUrl,
                        imageUrls: currentDisplayedItem.imageUrls,
                        itemCategoryTree: currentDisplayedItem.itemCategoryTree,
                        itemCategoryPropInfo: currentDisplayedItem.itemCategoryPropInfo
                    };
                    console.log('使用当前显示商品信息:', convertedItem);
                    return convertedItem;
                }
            }
        }

        return null;
    }, [matchedAlgo, matchedAgent, currentItem]);

    // 新增：查询是否已有标注
    const queryExistingLabel = useCallback(async (queryLiveStreamId?: number, queryTimestamp?: number) => {
        const actualLiveStreamId = queryLiveStreamId || liveStreamId;
        const actualTimestamp = queryTimestamp || timestamp;

        console.log('queryExistingLabel 被调用');
        setLabelQueryLoading(true);
        try {
            const params = {
                liveStreamId: actualLiveStreamId,
                relatedTimestamp: actualTimestamp,
            };
            console.log('查询标注参数:', params);
            const result = await API.QueryLabel(params);
            console.log('API返回结果:', result);

            if (result) {
                try {
                    // 解析data字段中的JSON字符串
                    const labelData = JSON.parse(result) as LabelQueryResult;
                    console.log('解析后的标注数据:', labelData);
                    setLabelQueryResult(labelData);

                    // 获取在车商品列表以查找商品详细信息
                    const items = await updateDataList(actualTimestamp);

                    // 使用辅助函数获取正确商品信息
                    const correctProduct = await getCorrectProductInfo(labelData, items);
                    setCorrectProductDetail(correctProduct);

                } catch (error) {
                    console.error('解析标注数据失败:', error, result.data);
                    setLabelQueryResult(null);
                    setCorrectProductDetail(null);
                }
            } else {
                console.log('没有标注数据或数据为空');
                setLabelQueryResult(null);
                setCorrectProductDetail(null);
            }
        } catch (error) {
            console.error('查询标注失败:', error);
            setLabelQueryResult(null);
            setCorrectProductDetail(null);
        } finally {
            setLabelQueryLoading(false);
        }
    }, [updateDataList, getCorrectProductInfo]); // 减少依赖项

    const queryExistingLabelForReplay =  useCallback(async (queryReplayId: number, queryTimestamp?: number) => {
        const actualTimestamp = queryTimestamp || timestamp;
        console.log('queryExistingLabelForReplay 被调用');
        setLabelQueryLoading(true);
        try {
            const params = {
                replayId: queryReplayId,
            };
            const result = await API.QueryLabelForReplay(params);
            if (result && result !== '{}') {
                try {
                    // 解析data字段中的JSON字符串
                    const labelData = JSON.parse(result) as LabelQueryResult;
                    console.log('解析后的标注数据:', labelData);
                    setLabelQueryResult(labelData);

                    // 获取在车商品列表以查找商品详细信息
                    const items = await updateDataList(actualTimestamp);

                    // 使用辅助函数获取正确商品信息
                    const correctProduct = await getCorrectProductInfo(labelData, items);
                    setCorrectProductDetail(correctProduct);

                } catch (error) {
                    console.error('解析标注数据失败:', error, result.data);
                    setLabelQueryResult(null);
                    setCorrectProductDetail(null);
                }
            } else {
                console.log('没有标注数据或数据为空');
                setLabelQueryResult(null);
                setCorrectProductDetail(null);
            }
        } catch (error) {
            console.error('查询标注失败:', error);
            setLabelQueryResult(null);
            setCorrectProductDetail(null);
        } finally {
            setLabelQueryLoading(false);
        }
    }, [updateDataList, getCorrectProductInfo]);

    // 新增：组件挂载时查询已有标注
    useEffect(() => {
        const queryKey = `${liveStreamId}-${timestamp}`;

        console.log('useEffect 被调用, 参数:', { liveStreamId, timestamp, queryKey });

        if (liveStreamId && timestamp && hasQueriedRef.current !== queryKey) {
            console.log('条件满足，开始查询标注...', queryKey);
            hasQueriedRef.current = queryKey;
            if(mode && mode.includes('replay') && replayId!=null){
                console.log("replayId:",showReplay)
                queryExistingLabelForReplay(replayId, timestamp);
            } else{
                queryExistingLabel(liveStreamId, timestamp);
            }
        } else {
            console.log('条件不满足或已查询过，跳过查询', {
                hasLiveStreamId: !!liveStreamId,
                hasTimestamp: !!timestamp,
                alreadyQueried: hasQueriedRef.current === queryKey
            });
        }
    }, [liveStreamId, timestamp]); // 移除queryExistingLabel依赖

    // 新增：渲染已有标注结果
    const renderLabelResult = useCallback(() => {
        if (!labelQueryResult) {
            console.log('没有标注结果数据');
            return null;
        }

        const { labelFlag, operator, createTime, extraInfo } = labelQueryResult;

        let statusConfig = {
            icon: <CheckCircleOutlined />,
            color: '#52c41a',
            text: '识别正确'
        };

        if (labelFlag === "none") {
            statusConfig = {
                icon: <MinusCircleOutlined />,
                color: '#faad14',
                text: '无讲解商品'
            };
        } else if (labelFlag === "false") {
            statusConfig = {
                icon: <CloseCircleOutlined />,
                color: '#ff4d4f',
                text: '识别错误'
            };
        }

        return (
            <>
                <div style={{ textAlign: 'center', marginBottom: '12px' }}>
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: '8px'
                    }}>
                        {React.cloneElement(statusConfig.icon, {
                            style: { fontSize: '24px', color: statusConfig.color, marginRight: '8px' }
                        })}
                        <Text strong style={{ fontSize: '16px', color: statusConfig.color }}>
                            {statusConfig.text}
                        </Text>
                    </div>

                    {/* 无商品标注时展示特殊的提示框 */}
                    {labelFlag === "none" && (
                        <div style={{
                            marginTop: '12px',
                            padding: '30px 20px',
                            backgroundColor: '#fffbe6',
                            border: '1px solid #ffe58f',
                            borderRadius: '4px',
                            textAlign: 'center',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            minHeight: '120px'
                        }}>
                            <Text style={{ 
                                color: '#faad14', 
                                fontSize: '16px',
                                fontWeight: 'bold'
                            }}>
                                此时刻无商品讲解
                            </Text>
                        </div>
                    )}

                    {/* 正确商品或错误标注情况 - 显示商品信息 */}
                    {labelFlag !== "none" && (
                        <div style={{
                            marginTop: '12px',
                            padding: '10px',
                            backgroundColor: '#fff',
                            border: '1px solid #e8e8e8',
                            borderRadius: '4px',
                            textAlign: 'left'
                        }}>
                            <Text strong style={{
                                color: labelFlag === "false" ? '#52c41a' : '#52c41a',
                                marginBottom: '8px',
                                display: 'block'
                            }}>
                                {labelFlag === "false" ? '正确商品：' : '确认商品：'}
                                {correctProductDetail?.serialNumber && (
                                    <Typography.Text type="secondary" style={{ marginLeft: 10, color: '#1890ff' }}>
                                        小黄车序号: {correctProductDetail.serialNumber}
                                    </Typography.Text>
                                )}
                            </Text>

                            {correctProductDetail ? (
                                <div style={{
                                    display: 'flex',
                                    gap: '10px',
                                    alignItems: 'flex-start'
                                }}>
                                    {correctProductDetail.imageUrl && (
                                        <Avatar
                                            src={correctProductDetail.imageUrl}
                                            size={60}
                                            shape="square"
                                            style={{ flexShrink: 0 }}
                                        />
                                    )}
                                    <div style={{
                                        flex: 1,
                                        minWidth: 0
                                    }}>
                                        <Text strong style={{
                                            fontSize: '14px',
                                            display: '-webkit-box',
                                            marginBottom: '8px',
                                            lineHeight: '1.4',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical'
                                        }}>
                                            {correctProductDetail.title}
                                        </Text>
                                        <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: '8px' }}>
                                            ID: {correctProductDetail.itemId}
                                        </Text>
                                        {correctProductDetail.itemCategoryTree && (
                                            <Tag style={{
                                                fontSize: '12px',
                                                padding: '2px 6px',
                                                lineHeight: '1.4',
                                                backgroundColor: '#e6f4ff',
                                                color: '#1890ff',
                                                border: '1px solid #91caff'
                                            }}>
                                                {correctProductDetail.itemCategoryTree}
                                            </Tag>
                                        )}
                                    </div>
                                </div>
                            ) : (
                                <div style={{
                                    textAlign: 'center',
                                    color: '#999',
                                    fontSize: '14px',
                                    padding: '20px 0'
                                }}>
                                    商品信息暂时无法获取
                                </div>
                            )}
                        </div>
                    )}
                </div>

                {/* 标注信息 */}
                <div style={{
                    textAlign: 'center',
                    marginTop: 'auto',
                    paddingTop: '8px',
                    borderTop: '1px dashed #eee',
                    fontSize: '12px'
                }}>
                    <Text type="secondary">
                        标注人：{operator || '未知'} | 标注时间：{createTime ? new Date(createTime).toLocaleString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        }) : '未知'}
                    </Text>
                </div>
            </>
        );
    }, [labelQueryResult, correctProductDetail]);

    // 提交标签
    const submitLabel = useCallback(async (labelFlag: string, extraItemId?: string) => {
        // 确定要使用的商品ID和sourceType
        const sourceType = tmItemId ? 1 : 0; // tmItemId存在则sourceType为1，否则为0
        const itemId = tmItemId || mmuItemId || matchedAlgo?.itemId || matchedAgent?.itemId;

        if (!itemId) {
            message.error('找不到有效的商品ID');
            return;
        }

        setIsSubmitting(true);
        try {
            const params: any = {
                liveStreamId,
                relatedTimestamp: timestamp,
                itemId: itemId,
                labelFlag: labelFlag,
                sourceType: sourceType,
                modelVersion: ""
            };

            // 如果是错误评价，需要额外传入正确的商品ID
            if (labelFlag === "false" && extraItemId) {
                params.extraItemId = extraItemId;
            }

            // 如果存在taskId，添加到参数中
            if (taskId) {
                params.taskId = taskId;
            }

            const result = await API.InsertLabel(params);

            if (result.code === '0'){
                const labelText = labelFlag === "true" ? '正确' : labelFlag === "false" ? '错误' : '无商品';
                message.success(`标签提交成功：${labelText}`);

                // 提交成功后重新查询标注状态
                await queryExistingLabel();
            }else {
                message.error(`当前标注提交失败: ${result.error_msg}`);
            }

            if (labelFlag === "false") {
                setIsModalVisible(false);
                setIsDrawerVisible(false);
                setCorrectItemId('');
                setSelectedItemId(null);
                setOnSaleItems([]);
            }
        } catch (error) {
            console.error('标签提交失败:', error);
            message.error('标签提交失败，请重试');
        } finally {
            setIsSubmitting(false);
        }
    }, [liveStreamId, timestamp, tmItemId, mmuItemId, matchedAlgo, matchedAgent, taskId, queryExistingLabel]);

    // 处理"错误"按钮点击
    const handleIncorrectClick = useCallback(async () => {
        setLoading(true);
        try {
            // 获取当前时刻的在车商品列表
            const items = await updateDataList(timestamp);
            setOnSaleItems(items);
            setIsDrawerVisible(true);
        } catch (error) {
            message.error('获取在车商品列表失败');
        } finally {
            setLoading(false);
        }
    }, [timestamp, updateDataList]);

    // 处理抽屉确认
    const handleConfirm = useCallback(() => {
        if (!selectedItemId) {
            message.warning('请选择正确的商品');
            return;
        }
        submitLabel("false", String(selectedItemId));
    }, [submitLabel, selectedItemId]);

    // 处理抽屉关闭
    const handleDrawerClose = useCallback(() => {
        setIsDrawerVisible(false);
        setCorrectItemId('');
        setSelectedItemId(null);
        setOnSaleItems([]);
    }, []);

    // 处理"正确"按钮点击 - 添加确认弹窗
    const handleCorrectClick = useCallback(() => {
        const itemId = tmItemId || mmuItemId || matchedAlgo?.itemId || matchedAgent?.itemId;
        const itemTitle = matchedAlgo?.goodDetail?.title || matchedAgent?.goodDetail?.title || '未知商品';

        Modal.confirm({
            title: '确认商品匹配正确',
            icon: <ExclamationCircleOutlined />,
            content: (
                <div>
                    <p>您确定要标记以下商品匹配为<strong style={{ color: '#52c41a' }}>正确</strong>吗？</p>
                    <p style={{ marginTop: 8 }}>
                        <strong>商品ID：</strong>{itemId}<br/>
                        <strong>商品标题：</strong>{itemTitle}
                    </p>
                </div>
            ),
            okText: '确认正确',
            okType: 'primary',
            cancelText: '取消',
            onOk() {
                submitLabel("true");
            },
        });
    }, [submitLabel, tmItemId, mmuItemId, matchedAlgo, matchedAgent]);

    // 处理"无商品"按钮点击 - 添加确认弹窗
    const handleNoProductClick = useCallback(() => {
        Modal.confirm({
            title: '确认标记为无商品',
            icon: <ExclamationCircleOutlined />,
            content: (
                <div>
                    <p>您确定要标记当前时刻为<strong style={{ color: '#faad14' }}>无商品</strong>吗？</p>
                    <p style={{ marginTop: 8, color: '#666' }}>
                        此操作表示当前时刻主播没有在介绍任何商品
                    </p>
                </div>
            ),
            okText: '确认无商品',
            okButtonProps: {
                style: {
                    backgroundColor: '#faad14',
                    borderColor: '#faad14'
                }
            },
            cancelText: '取消',
            onOk() {
                submitLabel("none"); // 修改为正确的无商品标记值
            },
        });
    }, [submitLabel]);

    // 计算布局
    const getCardLayout = useCallback(() => {
        if (showBoth) {
            // 两个商品卡片 + 评价区域 - 缩小商品卡片，增大评价区域
            return {
                mmuSpan: 6,
                tmSpan: 6,
                evaluationSpan: 12
            };
        } else if (showMmu && !showTm) {
            // 只有MMU商品卡片 + 评价区域
            return {
                mmuSpan: 8,
                tmSpan: 0,
                evaluationSpan: 16
            };
        } else if (!showMmu && showTm) {
            // 只有TM商品卡片 + 评价区域
            return {
                mmuSpan: 0,
                tmSpan: 8,
                evaluationSpan: 16
            };
        } else if (showReplay){
            return {
                mmuSpan: 0,
                replaySpan: 8,
                evaluationSpan: 16
            };
        }else {
            // 没有商品卡片，只显示评价区域
            return {
                mmuSpan: 0,
                tmSpan: 0,
                evaluationSpan: 24
            };
        }
    }, [showBoth, showMmu, showTm,showReplay]);

    const layout = getCardLayout();

    // 表格列定义
    const columns = [
        {
            title: '序号',
            dataIndex: 'serialNumber',
            key: 'serialNumber',
            width: 100,
        },
        {
            title: '商品ID',
            dataIndex: 'itemId',
            key: 'itemId',
            width: 150,
        },
        {
            title: '商品标题',
            dataIndex: 'title',
            key: 'title',
            ellipsis: true,
            width: 300,
        },
        {
            title: '分类',
            dataIndex: 'itemCategoryTree',
            key: 'itemCategoryTree',
            width: 200,
            ellipsis: true,
        },
        {
            title: '商品图片',
            dataIndex: 'imageUrl',
            key: 'imageUrl',
            width: 280,
            render: (imageUrl: string, record: GoodItem) => {
                // 优先使用imageUrls（多图），如果没有则使用单图imageUrl
                const images = record.imageUrls?.length ? record.imageUrls : (imageUrl ? [imageUrl] : []);

                if (images.length === 0) {
                    return <span style={{ color: '#999' }}>无图片</span>;
                }

                return (
                    <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                        <Image.PreviewGroup
                            preview={{
                                toolbarRender: () => (
                                    <div style={{ color: 'white', padding: '0 16px' }}>
                                        {record.title}
                                    </div>
                                ),
                            }}
                        >
                            {images?.slice(0, 3)?.map?.((img, index) => (
                                <Image
                                    key={index}
                                    src={img}
                                    width={100}
                                    height={100}
                                    style={{ objectFit: 'cover', borderRadius: '6px' }}
                                    placeholder={
                                        <div style={{
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: '#f5f5f5'
                                        }}>
                                            <PictureOutlined style={{ fontSize: 24, color: '#ccc' }} />
                                        </div>
                                    }
                                />
                            ))}
                            {images.length > 3 && (
                                <Image
                                    src={images[3]}
                                    width={100}
                                    height={100}
                                    style={{ objectFit: 'cover', borderRadius: '6px' }}
                                    preview={{
                                        src: images[3],
                                        mask: (
                                            <div style={{
                                                display: 'flex',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                width: '100%',
                                                height: '100%',
                                                backgroundColor: 'rgba(0,0,0,0.6)',
                                                color: 'white',
                                                fontSize: '16px',
                                                fontWeight: 'bold'
                                            }}>
                                                +{images.length - 3}
                                            </div>
                                        )
                                    }}
                                />
                            )}
                            {/* 隐藏的图片，用于预览组中包含所有图片 */}
                            {images?.slice(4)?.map?.((img, index) => (
                                <Image
                                    key={`hidden-${index}`}
                                    src={img}
                                    style={{ display: 'none' }}
                                />
                            ))}
                        </Image.PreviewGroup>
                    </div>
                );
            },
        },
    ];

    return (
        <Card
            size="small"
            title={<span style={{ fontSize: 14, fontWeight: 'bold' }}>{title}</span>}
            style={{ width: '100%', marginBottom: 10, height: 330, overflow: 'hidden' }}
        >
            <Row gutter={16} style={{ height: '100%' }}>
                {/* 主播MMU匹配商品 */}
                {showMmu && (
                    <Col span={layout.mmuSpan} style={{ height: '100%' }}>
                        <div style={{ width: '100%', height: '100%' }}>
                            <GoodCard
                                ribbonText={mode === 'replay-debug' ? '标注商品' : 'MMU匹配商品'}
                                ribbonColor="orange"
                                item={currentItem}
                                style={{ width: '100%', height: '100%', overflow: 'hidden' }}
                            />
                        </div>
                    </Col>
                )}

                {/* 算法匹配商品 */}
                {showTm && (
                    <Col span={layout.tmSpan} style={{ height: '100%' }}>
                        <div style={{ width: '100%', height: '100%' }}>
                            {matchedAlgo?.itemId != null ? (
                                <GoodCardAlgo
                                    // 条件渲染：如果 score 为 0 则隐藏标签，否则显示
                                    ribbonText={matchedAlgo.score === 0 ? '' : `算法匹配分数 ${matchedAlgo.score} `}
                                    ribbonColor={highlightAlgo ? 'green' : 'red'}
                                    item={matchedAlgo}
                                    agentRequest={agentRequest}
                                    agentResponse={agentResponse}
                                    mode={mode}
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        overflow: 'hidden',
                                        border: highlightAlgo ? '2px solid green' : undefined,
                                        borderRadius: highlightAlgo ? 4 : undefined,
                                    }}
                                />
                            ) : (
                                <div style={placeholderStyle}>暂无算法策略匹配商品</div>
                            )}
                        </div>
                    </Col>
                )}

                {/* 回放匹配商品 */}
                { showReplay && (
                    <Col span={layout.replaySpan} style={{ height: '100%' }}>
                        <div style={{ width: '100%', height: '100%' }}>
                            {matchedReplay?.itemId != null ? (
                                <GoodCardAlgo
                                    ribbonText={matchedReplay.score === 0 ? `` : `回放匹配分数 ${matchedReplay.score} `}
                                    ribbonColor={highlightAlgo ? 'green' : 'red'}
                                    item={matchedReplay}
                                    mode={mode}
                                    agentRequest={agentRequest}
                                    agentResponse={agentResponse}
                                    replayModelVersion={replayModelVersion}
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        overflow: 'hidden',
                                    }}
                                />
                            ) : (
                                <div style={placeholderStyle}>回放结果无匹配商品</div>
                            )}
                        </div>
                    </Col>
                )}

                {/* 商品评价区域 */}
                <Col span={layout.evaluationSpan} style={{ height: '100%' }}>
                    {labelQueryLoading ? (
                        // 查询标注状态时显示加载
                        <div style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            padding: '20px',
                            border: '1px solid #f0f0f0',
                            borderRadius: '6px',
                            backgroundColor: '#fafafa'
                        }}>
                            <div style={{
                                fontSize: '16px',
                                fontWeight: 'bold',
                                marginBottom: '20px',
                                color: '#333'
                            }}>
                                查询标注状态中...
                            </div>
                        </div>
                    ) : (
                        // 正常显示区域 - 改为左右布局
                        <div style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            flexDirection: labelQueryResult ? 'row' : 'column',
                            border: '1px solid #f0f0f0',
                            borderRadius: '6px',
                            backgroundColor: '#fafafa',
                            overflow: 'hidden'
                        }}>
                            {/* 已有标注结果区域 - 左侧 */}
                            {labelQueryResult && (
                                <div style={{
                                    width: '50%',
                                    padding: '12px',
                                    borderRight: '1px solid #e8e8e8',
                                    backgroundColor: '#fff',
                                    display: 'flex',
                                    flexDirection: 'column'
                                }}>
                                    <div style={{
                                        padding: '8px',
                                        border: `1px solid ${(() => {
                                            const { labelFlag } = labelQueryResult;
                                            if (labelFlag === "none") return '#ffe58f';
                                            if (labelFlag === "false") return '#ffccc7';
                                            return '#b7eb8f';
                                        })()}`,
                                        borderRadius: '6px',
                                        backgroundColor: (() => {
                                            const { labelFlag } = labelQueryResult;
                                            if (labelFlag === "none") return '#fffbe6';
                                            if (labelFlag === "false") return '#fff2f0';
                                            return '#f6ffed';
                                        })(),
                                        height: '100%',
                                        overflow: 'auto'
                                    }}>
                                        {renderLabelResult()}
                                    </div>
                                </div>
                            )}

                            {/* 标注按钮区域 - 右侧或全宽 */}
                            {!showNoLabelCard && (<div style={{
                                width: labelQueryResult ? '50%' : '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                                padding: '20px'
                            }}>
                                <div style={{
                                    fontSize: '16px',
                                    fontWeight: 'bold',
                                    marginBottom: '20px',
                                    color: '#333'
                                }}>
                                    {labelQueryResult ? '重新标注' : '商品匹配标注'}
                                </div>

                                {disableLabeling ? (
                                    <div style={{
                                        fontSize: '14px',
                                        color: '#666',
                                        textAlign: 'center',
                                        padding: '20px'
                                    }}>
                                        <div style={{ marginBottom: '10px' }}>
                                            <InfoCircleOutlined style={{ fontSize: '24px', color: '#1890ff', marginBottom: '10px' }} />
                                        </div>
                                        当前模式下不支持标注功能
                                    </div>
                                ) : (
                                    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                                        <Button
                                            type="primary"
                                            size="large"
                                            style={{
                                                width: '100%',
                                                backgroundColor: '#52c41a',
                                                borderColor: '#52c41a'
                                            }}
                                            loading={isSubmitting}
                                            onClick={handleCorrectClick}
                                        >
                                            正确
                                        </Button>
                                        <Button
                                            danger
                                            size="large"
                                            style={{ width: '100%' }}
                                            loading={isSubmitting || loading}
                                            onClick={handleIncorrectClick}
                                        >
                                            错误
                                        </Button>
                                        <Button
                                            size="large"
                                            style={{
                                                width: '100%',
                                                backgroundColor: '#faad14',
                                                borderColor: '#faad14',
                                                color: '#fff'
                                            }}
                                            loading={isSubmitting}
                                            onClick={handleNoProductClick}
                                        >
                                            无商品
                                        </Button>

                                        <div style={{
                                            marginTop: '10px',
                                            fontSize: '12px',
                                            color: '#666',
                                            textAlign: 'center'
                                        }}>
                                            请根据实际情况进行评价
                                        </div>
                                    </Space>
                                )}
                            </div>)}
                        </div>
                    )}
                </Col>
            </Row>

            {/* 错误评价抽屉 */}
            <Drawer
                title={
                    <span style={{ fontWeight: 'bold' }}>
                        <ShoppingCartOutlined style={{ marginRight: 8 }} />
                        选择正确的商品
                        <InfoCircleOutlined
                            style={{ color: 'rgba(0,0,0,0.45)', marginLeft: 8 }}
                            title="从当前时刻的在车商品中选择正确匹配的商品"
                        />
                    </span>
                }
                width={1400}
                onClose={handleDrawerClose}
                open={isDrawerVisible}
                extra={
                    <Space>
                        <Button onClick={handleDrawerClose}>取消</Button>
                        <Button
                            type="primary"
                            onClick={handleConfirm}
                            loading={isSubmitting}
                            disabled={!selectedItemId}
                        >
                            确认选择
                        </Button>
                    </Space>
                }
            >
                <div style={{ marginBottom: '16px' }}>
                    当前匹配商品ID：{tmItemId || mmuItemId || matchedAlgo?.itemId || matchedAgent?.itemId}
                </div>
                <div style={{ marginBottom: '16px' }}>
                    请从下方在车商品中选择正确的商品：
                </div>
                <Table
                    columns={columns}
                    dataSource={onSaleItems}
                    rowKey="itemId"
                    pagination={{
                        pageSize: 8,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `共 ${total} 条，当前显示第 ${range?.[0]}-${range?.[1]} 条`,
                    }}
                    size="large"
                    scroll={{ y: 'calc(100vh - 300px)' }}
                    rowSelection={{
                        type: 'radio',
                        selectedRowKeys: selectedItemId ? [selectedItemId] : [],
                        onChange: (selectedRowKeys) => {
                            setSelectedItemId(selectedRowKeys?.[0] as number || null);
                        },
                    }}
                />
            </Drawer>
        </Card>
    );
};

export default MatchedGoodsCard;
