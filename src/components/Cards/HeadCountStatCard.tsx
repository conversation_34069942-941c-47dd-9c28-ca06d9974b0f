import React, {useEffect, useMemo, useRef, useState} from 'react';
import {Avatar, Card, Popover, Tooltip} from 'antd';
import {CheckCircleOutlined} from '@ant-design/icons';
import './StatisticCard.css';
import ReactECharts from 'echarts-for-react';
import {HeadCountStatsData} from "@/interfaces/interfaces";
import {MiniLineChart} from "@/components/Charts/MiniLineChart";
import PopoverGradientAreaChart from "@/components/Charts/PopoverGradientAreaChart";

interface HeadCountStatCardProps {
    data: HeadCountStatsData | null;
    historyLabels: string[];
}

export const HeadCountStatCard: React.FC<HeadCountStatCardProps> = React.memo(({data,historyLabels}) => {

    const [openPopoverKey, setOpenPopoverKey] = useState<'details' | 'chart' | null>(null);
    const chartRef = useRef<ReactECharts | null>(null);

    const totalCount = data ? data.totalCount : 0;
    const history = data ? data.history : [];
    const teamMembers = data ? data.teamMembers : [];

    // 根据角色进行过滤
    const interns = useMemo(() => teamMembers.filter(member => member.role === '实习生'), [teamMembers]);
    const secondedStaff = useMemo(() => teamMembers.filter(member => member.role === '借调人员'), [teamMembers]);

    useEffect(() => {
        const resizeChart = () => {
            chartRef.current?.getEchartsInstance().resize();
        };
        window.addEventListener('resize', resizeChart);
        return () => {
            window.removeEventListener('resize', resizeChart);
        };
    }, []);

    useEffect(() => {
        chartRef.current?.getEchartsInstance().resize();
    }, [history,historyLabels]);


    // 提前处理没有数据的情况
    if (!data) return <div>没有数据可显示</div>;

    // 通用的头像展示组件
    const renderAvatars = (members, maxCount) => (
        <Avatar.Group
            size="small"
            max={{
                count: maxCount,
                style: {color: '#f56a00', backgroundColor: '#fde3cf'},
            }}
        >
            {members.map(member => (
                <Tooltip title={member.name} placement="top" key={member.userName}>
                    <Avatar src={member.avatar}/>
                </Tooltip>
            ))}
        </Avatar.Group>
    );

    return (
        <Card style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }} >
            {/*上部分：数值*/}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div className="card-title">
                    <CheckCircleOutlined />
                    <span style={{ marginLeft: 5 }}>投入人数</span>
                </div>
            </div>
            {/*下部分：图表和数据*/}
            <div style={{ display: 'flex', flex: 1 }}>
                {/*数据*/}
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                    <div className="card-value" style={{ cursor: 'pointer' }}>
                        {totalCount}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span style={{ fontWeight: 'bold', fontSize: "12px" }}>投入总人员：</span>
                        {renderAvatars(teamMembers, 4)}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                        <span style={{ fontWeight: 'bold', fontSize: '12px' }}>实习：</span>
                        {renderAvatars(interns, 1)}
                        <span style={{ fontWeight: 'bold', fontSize: '12px', marginLeft: '8px' }}>借调：</span>
                        {renderAvatars(secondedStaff, 1)}
                    </div>
                </div>
                {/*图表*/}
                <div style={{ flex: 1, overflow: 'hidden' }}>
                    <Popover
                        content={
                            <PopoverGradientAreaChart
                                historyLabels={historyLabels}
                                history={history}
                                height={300}
                                onClose={() => setOpenPopoverKey(null)}
                            />
                        }
                        trigger="click"
                        placement="right"
                        open={openPopoverKey === 'chart'}
                        onOpenChange={visible => setOpenPopoverKey(visible ? 'chart' : null)}
                    >
                        <MiniLineChart
                            history={history}
                            containerStyle={{ flex: 1, height: '90%' }}
                            onClick={() => setOpenPopoverKey('chart')}
                        />
                    </Popover>
                </div>
            </div>
        </Card>
    );
});