import React, {useRef, useState} from "react";
import {Card, Col, Image, message, Modal, Row, Tooltip, Tree, Radio} from "antd";
import TextArea from "antd/es/input/TextArea";
import type {MatchedGood} from "@/components/Cards/MatchedGoodsCard";
import type {KwaiPlayer} from '@ks-video/kwai-player-web/react';
import {KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import Link from "antd/es/typography/Link";
import {formatMillSecTime} from "@/pages/AgentLive/constant";
import GoodCardAlgoSmall from "@/components/Cards/GoodCardAlgoSmall";
import type {Frame4, FrameImageItem} from "@/interfaces/Frame4";
import FrameImageCard from "@/components/Cards/FrameImageCard";


export interface windowTracInfo {
    windowTimestamp:number;
    windowRelatedTime:number;
    matchedGoodsAlgo: MatchedGood;
    agentRequest:any;
    agentResponse:any;
    frameTraceInfoList: Frame4[];
}

interface WindowTraceInfoListRowCardProps {
    playerRef: React.RefObject<KwaiPlayer | null>;
    windowTracInfoList: windowTracInfo[];
    liveStreamId?: number; // 添加liveStreamId属性
    labelStatus?: number; // 标注状态：0-未标注，1-已标注
    frameSelections?: Record<string, string>; // 抽帧图片选择状态
    onFrameSelectionsChange?: (selections: Record<string, string>) => void; // 抽帧图片选择状态变化回调
    isShowGoodLabel?: number; // 标注后的是否在讲品字段
    isGoodFrameLabels?: Record<string, number>; // 标注后的帧字段
}



const WindowTraceInfoListRowCard: React.FC<WindowTraceInfoListRowCardProps> = ({ 
    windowTracInfoList, 
    playerRef, 
    labelStatus = 0,
    frameSelections = {},
    onFrameSelectionsChange,
    isShowGoodLabel = 0,
    isGoodFrameLabels = {}
}) => {


    // Ref：视频播放器
    const playerRef1 = useRef<KwaiPlayer | null>(null);
    // State：是否打开 JSON Modal
    // const [openVideoModal, setOpenVideoModal] = useState<{ [key: number]: boolean }>({});
    const [openVideoModal, setOpenVideoModal] = useState<Record<string, boolean>>({});
    // State：当前正在播放的视频 URL
    const [currentVideoUrl, setCurrentVideoUrl] = useState<string>('');
    // State：哪个 frame 的 JSON Modal 正在打开
    const [jsonFrame, setJsonFrame] = useState<Frame4 | null>(null);

    const handleJump = (time: number) => {
        if (playerRef.current) {
            console.log("handleJump",time);
            playerRef.current.currentTime = time;
        }
    };

    // 处理选项选择
    const handleFrameSelection = (traceIdx: number, frameIdx: number, itemIdx: number, value: string) => {
        const key = `${traceIdx}-${frameIdx}-${itemIdx}`;
        const newSelections = {
            ...frameSelections,
            [key]: value
        };
        onFrameSelectionsChange?.(newSelections);
    };

    /**
     * 打开 抽帧视频 Modal 时，传入 index 和视频 URL
     * @param index
     * @param videoUrl
     */
    const handleOpenVideoModal = (traceIdx: number, frameIdx: number, videoUrl: string) => {
        const k = `${traceIdx}-${frameIdx}`;
        setOpenVideoModal(prev => ({ ...prev, [k]: true }));
        setCurrentVideoUrl(videoUrl.replace(/^http:/, 'https:'));
    };

    /**
     * 关闭 抽帧视频 Modal 时，传入 index
     * @param index
     */

    const handleCloseVideoModal = (traceIdx: number, frameIdx: number) => {
        const k = `${traceIdx}-${frameIdx}`;
        setOpenVideoModal(prev => ({ ...prev, [k]: false }));
        setCurrentVideoUrl('');
    };

    /**
     * 将 JSON 数据转换为 Tree 结构
     */
    const jsonToTreeData = (data: any, path = ""): any[] => {
        if (data === null || typeof data !== "object") {
            return [{title: `${path}: ${String(data)}`, key: path}];
        }
        if (Array.isArray(data)) {
            return data.map((item, idx) => ({
                title:
                    typeof item === "object"
                        ? `[${idx}]`
                        : `[${idx}]: ${String(item)}`,
                key: `${path}[${idx}]`,
                children:
                    typeof item === "object" && item !== null
                        ? jsonToTreeData(item, `${path}[${idx}]`)
                        : undefined,
            }));
        }
        return Object.entries(data).map(([k, v]) => ({
            title:
                typeof v === "object" && v !== null
                    ? k
                    : `${k}: ${String(v)}`,
            key: path ? `${path}.${k}` : k,
            children:
                typeof v === "object" && v !== null
                    ? jsonToTreeData(v, path ? `${path}.${k}` : k)
                    : undefined,
        }));
    };

    /**
     * 复制 JSON 数据到剪切板
     */
    const handleCopyJson = () => {
        if (!jsonFrame) return;
        const text = JSON.stringify(jsonFrame, null, 2);
        navigator.clipboard
            ?.writeText(text)
            .then(() => message.success("已复制 JSON 数据到剪切板"))
            .catch(() => {
                // 兼容老浏览器
                const textarea = document.createElement("textarea");
                textarea.value = text;
                textarea.style.position = "fixed";
                textarea.style.opacity = "0";
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand("copy");
                document.body.removeChild(textarea);
                message.success("已复制 JSON 数据到剪切板");
            });
    };

    return (
        <div>
            {windowTracInfoList.map((traceInfo, traceIdx) => {
                const reversedFrames = Array.isArray(traceInfo.frameTraceInfoList)
                    ? traceInfo.frameTraceInfoList.slice().reverse()
                    : [];
                return reversedFrames.map((frame) => (
                    <Row key={`${traceIdx}-${frame.index}`} gutter={[2, 2]} style={{marginBottom: 2}}>
                        {/* 左卡：图片 */}
                        {/* 中卡：ASR */}
                        <Col flex="200px">
                            <Card
                                size="small"
                                title={<span style={{fontSize: '14px', fontWeight: 'bold'}}>ASR 文本</span>}
                                style={{height: 750, overflowY: 'auto'}}
                            >
                                <TextArea
                                    style={{width: '100%',fontSize:12,height:650}}
                                    value={frame.asrText}
                                    rows={12}
                                    readOnly
                                    placeholder="ASR 文本将在此处展示"
                                />
                            </Card>
                        </Col>

                        {/* 右卡：最多五张商品图片 */}
                        <Col flex="auto">
                            <Card
                                title="抽帧图片"
                                size="small"
                                style={{height: 750, overflowY: 'auto', minWidth: 800}}
                            >
                                {/* 第一行：5张图片，每张图片下方都有选项 */}
                                <div style={{display: "flex", justifyContent: "space-between", gap: 5, marginBottom: 8}}>
                                    {(frame.frame2imageList || []).slice(0, 5).map((imageItem, idx) => (
                                        <FrameImageCard
                                            key={idx}
                                            imageItem={imageItem}
                                            value={frameSelections[`${traceIdx}-${frame.index}-${idx}`] || ''}
                                            onChange={(value) => handleFrameSelection(traceIdx, frame.index, idx, value)}
                                            style={{ flex: 1 }}
                                            labelStatus={labelStatus}
                                            isGoodFrameLabel={isGoodFrameLabels[`${traceIdx}-${frame.index}-${idx}`]}
                                        />
                                    ))}
                                </div>
                                
                                {/* 第二行：5张图片，每张图片下方都有选项 */}
                                <div style={{display: "flex", justifyContent: "space-between", gap: 8}}>
                                {(frame.frame2imageList || []).slice(5, 10).map((imageItem, idx) => (
                                    <FrameImageCard
                                        key={idx}
                                        imageItem={imageItem}
                                        value={frameSelections[`${traceIdx}-${frame.index}-${idx + 5}`] || ''}
                                        onChange={(value) => handleFrameSelection(traceIdx, frame.index, idx + 5, value)}
                                        style={{ flex: 1 }}
                                        labelStatus={labelStatus}
                                        isGoodFrameLabel={isGoodFrameLabels[`${traceIdx}-${frame.index}-${idx + 5}`]}
                                    />
                                ))}
                                </div>
                                
                            </Card>
                        </Col>
                    </Row>
                ));
            })}

            {/* 视频 Modal */}
            {windowTracInfoList.flatMap((traceInfo, traceIdx) =>
                Array.isArray(traceInfo.frameTraceInfoList)
                    ? traceInfo.frameTraceInfoList.map(frame => (
                        <Modal
                            key={`modal-${traceIdx}-${frame.index}-${frame.screenSnapshotTimeStamp}`}
                            title={`直播抽帧-${frame.index} 视频片段`}
                            open={openVideoModal[`${traceIdx}-${frame.index}`] || false}
                            onCancel={() => handleCloseVideoModal(traceIdx, frame.index)}
                            footer={null}
                            width={550}
                            styles={{ body: { padding: 10, textAlign: "center", background: "#f5f5f5" } }}
                            destroyOnClose
                        >
                            <div
                                style={{
                                    background: "#000",
                                    borderRadius: 8,
                                    width: 480,
                                    height: 640
                                }}
                            >
                                <KwaiPlayerReact
                                    ref={playerRef1}
                                    src={currentVideoUrl}
                                    controls
                                    autoPlay={true}
                                    preload="auto"
                                    style={{ width: "100%", height: "100%" }}
                                />
                            </div>
                        </Modal>
                    ))
                    : []
            )}

            {/* JSON Modal */}
            {jsonFrame && (
                <Modal
                    mask={false}
                    open={true}
                    width={800}
                    footer={null}
                    onCancel={() => setJsonFrame(null)}
                    bodyStyle={{maxHeight: 650, overflowY: "auto"}}
                    title={
                        <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                            <span>该帧原始数据</span>
                            <Link style={{marginRight: 40}} onClick={handleCopyJson}>一键复制</Link>
                        </div>
                    }
                >
                    <Tree
                        treeData={jsonToTreeData(jsonFrame)}
                        defaultExpandAll
                        showLine
                    />
                </Modal>
            )}
        </div>
    );
};
export default WindowTraceInfoListRowCard;
