// LiveSlicePreview.tsx
import React from 'react';
import {Card, Col, ConfigProvider, Image, Popover, Row} from 'antd';
import zhCN from "antd/es/locale/zh_CN";

interface LiveSlicePreviewProps {
    sliceUrls: string[];
    title?: string;
    maxVisible?: number;
    height?: number;
}

export const LiveSlicePreview: React.FC<LiveSlicePreviewProps> = ({
                                                                      sliceUrls,
                                                                      title = '直播抽帧截图',
                                                                      maxVisible = 5,
                                                                      height = 200,
                                                                  }) => {
    const visible = sliceUrls.slice(0, maxVisible);
    const hidden = sliceUrls.slice(maxVisible);

    return (
        <ConfigProvider locale={zhCN}>
        <Card
            size="small"
            title={<span style={{ fontSize: 14, fontWeight: 'bold' }}>{title}</span>}
            style={{ width: '100%', height: 262 }}
        >
            <Row gutter={[16, 16]}>
                {/* 渲染可见图片，若第5张且有隐藏则覆盖 +n 效果 */}
                {visible.map((url, idx) => {
                    // 如果是最后一个可见位置，并且有隐藏图片
                    if (idx === maxVisible - 1 && hidden.length > 0) {
                        return (
                            <Col key={url + idx} span={6} style={{ flex: '0 0 20%', position: 'relative' }}>
                                <Popover
                                    content={
                                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, maxWidth: 430 }}>
                                            {hidden.map((u, j) => (
                                                <Image
                                                    key={u + j}
                                                    width={100}
                                                    height={150}
                                                    src={u}
                                                    style={{ objectFit: 'contain' }}
                                                />
                                            ))}
                                        </div>
                                    }
                                    placement="top"
                                    trigger="hover"
                                >
                                    <Image
                                        alt={`Segment ${idx + 1}`}
                                        src={url}
                                        width="100%"
                                        height={height}
                                        style={{ objectFit: 'contain', cursor: 'pointer' }}
                                    />
                                    {/* +n 覆盖层 */}
                                    <div style={{
                                        position: 'absolute',
                                        top: 80,
                                        right: 30,
                                        background: 'rgba(0, 0, 0, 0.5)',
                                        color: '#fff',
                                        borderRadius: 12,
                                        padding: '2px 6px',
                                        fontSize: 14,
                                    }}>
                                        +{hidden.length}
                                    </div>
                                </Popover>
                            </Col>
                        );
                    }
                    // 普通展示
                    return (
                        <Col key={url + idx} span={6} style={{ flex: '0 0 20%' }}>
                            <Image
                                alt={`Segment ${idx + 1}`}
                                src={url}
                                width="100%"
                                height={height}
                                style={{ objectFit: 'contain' }}
                            />
                        </Col>
                    );
                })}
            </Row>
        </Card>
        </ConfigProvider>
    );
};

