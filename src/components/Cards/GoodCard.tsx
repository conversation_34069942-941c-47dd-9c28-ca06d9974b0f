// GoodCard.tsx
import React from 'react';
import {Badge, Card, Image, Popover, Tag, Typography} from 'antd';
import {MatchedGood} from "@/components/Cards/MatchedGoodsCard";

export interface GoodCardProps {
    ribbonText: string;
    ribbonColor?: string;
    item: MatchedGood;
    llmExplanation?:string;
    llmModelName?:string;
    /** 整个组件的外层容器样式（包括宽度） */
    style?: React.CSSProperties;
}

export const GoodCard: React.FC<GoodCardProps> = ({
                                                      ribbonText,
                                                      ribbonColor = 'red',
                                                      item,
                                                      llmExplanation,
                                                      llmModelName,
                                                      style = {},
                                                  }) => {
    const { itemId, goodDetail } = item;
    const title = goodDetail?.title ?? `商品 ${itemId}`;
    const imageUrl = goodDetail?.imageUrl;
    const propsInfo = goodDetail?.itemCategoryPropInfo ?? {};
    const imageUrls = goodDetail?.imageUrls ?? [];


    const popContent = (
        <div style={{ display: 'flex', flexDirection: 'column', maxWidth: 500 }}>
            {/* 上部分：标题、ID */}
            <div style={{ marginBottom: 6 }}>
                <Typography.Title level={5} style={{ margin: 0 }}>
                    {title}
                </Typography.Title>
                <Typography.Text type="secondary">商品ID: {itemId}</Typography.Text>
            </div>

            {/* 中部分：左属性、右图片*/}
            <div style={{ display: 'flex', gap: 16, flex: 1, borderTop: '1px dashed #eee'}}>
                {/* 左：属性*/}
                <div style={{ flex: 1 }}>
                    <div style={{ paddingTop: 6 }}>
                        {Object.entries(propsInfo).map(([prop, val]) => (
                            <div key={prop} style={{whiteSpace: 'normal',wordBreak: 'break-word', }}>
                                <Typography.Text type="secondary" style={{fontSize:12}}>
                                    <strong>{prop}:</strong> {val ?? '-'}
                                </Typography.Text>
                            </div>
                        ))}
                    </div>
                </div>
                {/* 右：图片*/}
                <div style={{   display: 'flex', flexWrap: 'wrap', gap: '6px 6px',  paddingTop: 6, maxWidth: 300, }}>
                    {imageUrls.map((url, idx) => (
                        <Image
                            key={idx}
                            width={90}
                            height={90}
                            src={url}
                            style={{ objectFit: 'contain' }}
                        />
                    ))}
                </div>
            </div>

            {/* 下部分：LLM */}
            {(llmModelName || llmExplanation) && (
                <div style={{ marginTop: 6, paddingTop: 12, borderTop: '1px dashed #eee' }}>
                    {llmModelName && (
                        <Tag color="magenta" style={{ display: 'block', marginBottom: 8 }}>
                            模型名称：{llmModelName}
                        </Tag>
                    )}
                    {llmExplanation && (
                        <Tag
                            color="green"
                            style={{
                                display: 'block',
                                whiteSpace: 'normal',
                                wordBreak: 'break-word',
                            }}
                        >
                            模型解释：{llmExplanation}
                        </Tag>
                    )}
                </div>
            )}
        </div>
    );

    return (
        <Popover content={popContent} placement="topLeft" trigger="hover">
            <div style={{ ...style, overflow: 'hidden' }}>
                <Badge.Ribbon text={ribbonText} color={ribbonColor}>
                    <Card hoverable style={{ width: '100%', height: '100%' }}>
                        {imageUrl && (
                            <div style={{ width: '100%', height: 200 }}>
                                <img
                                    alt={`product-${itemId}`}
                                    src={imageUrl}
                                    style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                                />
                            </div>
                        )}
                        <Card.Meta title={`(${item?.carItemIndex}号商品) ${itemId}` } />
                    </Card>
                </Badge.Ribbon>
            </div>
        </Popover>
    );
};

export default GoodCard;

