import React, {useMemo, useState} from 'react';
import {Badge, Card, Image, message, Modal, Popover, Tree, Typography} from 'antd';
import {MatchedGood} from '@/components/Cards/MatchedGoodsCard';
import Link from "antd/es/typography/Link";

export interface GoodCardAlgoProps {
    ribbonText: string;
    ribbonColor?: string;
    item: MatchedGood;
    agentRequest: string;       // JSON string, may contain nested JSON strings
    agentResponse: string;      // JSON string, may contain nested JSON strings
    /** 整个组件的外层容器样式（包括宽度） */
    style?: React.CSSProperties;
    mode?:string
    replayModelVersion?:string
}

/**
 * 递归解析可能嵌套的 JSON 字符串。仅当字符串看起来是 JSON（以 { 或 [ 开头）时尝试解析。
 */
const normalizeData = (data: any): any => {
    if (typeof data === 'string') {
        const str = data.trim();
        if ((str.startsWith('{') && str.endsWith('}')) || (str.startsWith('[') && str.endsWith(']'))) {
            try {
                const parsed = JSON.parse(str);
                return normalizeData(parsed);
            } catch {
                // 虽然看似 JSON，但解析失败，视为普通字符串
            }
        }
        return data;
    }
    if (Array.isArray(data)) {
        return data.map(normalizeData);
    }
    if (data && typeof data === 'object') {
        return Object.fromEntries(
            Object.entries(data).map(([k, v]) => [k, normalizeData(v)])
        );
    }
    return data;
};

/**
 * 将任意 JS 对象或数组转换为 Antd Tree 组件需要的 treeData 格式
 */
const convertToTreeData = (data: any, prefix = 'root'): any[] => {
    if (Array.isArray(data)) {
        return data.map((item, idx) => ({
            title: `[${idx}]`,
            key: `${prefix}-${idx}`,
            children: Array.isArray(item) || (item && typeof item === 'object')
                ? convertToTreeData(item, `${prefix}-${idx}`)
                : [{ title: String(item), key: `${prefix}-${idx}-leaf` }],
        }));
    }
    if (data === null || typeof data !== 'object') {
        return [{ title: String(data), key: `${prefix}` }];
    }
    return Object.entries(data).map(([key, value], idx) => {
        const nodeKey = `${prefix}-${key}-${idx}`;
        if (Array.isArray(value)) {
            return {
                title: key,
                key: nodeKey,
                children: convertToTreeData(value, nodeKey),
            };
        }
        if (value && typeof value === 'object') {
            return {
                title: key,
                key: nodeKey,
                children: convertToTreeData(value, nodeKey),
            };
        }
        return {
            title: `${key}: ${String(value)}`,
            key: nodeKey,
            isLeaf: true,
        };
    });
};

export const GoodCard: React.FC<GoodCardAlgoProps> = ({
                                                          ribbonText,
                                                          ribbonColor = 'red',
                                                          item,
                                                          agentRequest,
                                                          agentResponse,
                                                          style = {},
                                                          mode,
                                                          replayModelVersion,
                                                      }) => {
    const { itemId, goodDetail } = item;
    const title = goodDetail?.title ?? `商品 ${itemId}`;
    const imageUrl = goodDetail?.imageUrl;

    const [modalVisible, setModalVisible] = useState(false);

    // 解析并规范化数据，再转换为 treeData
    const requestTreeData = useMemo(() => {
        if (!agentRequest) return [];
        try {
            const raw = JSON.parse(agentRequest);
            return convertToTreeData(normalizeData(raw), 'request');
        } catch {
            return [];
        }
    }, [agentRequest]);

    const responseTreeData = useMemo(() => {
        if (!agentResponse) return [];
        try {
            const raw = JSON.parse(agentResponse);
            return convertToTreeData(normalizeData(raw), 'response');
        } catch {
            return [];
        }
    }, [agentResponse]);

    // 复制 JSON
    const handleCopyJson = () => {
        try {
            const combined: any = {};
            if (agentRequest) combined.request = JSON.parse(agentRequest);
            if (agentResponse) combined.response = JSON.parse(agentResponse);
            navigator.clipboard.writeText(JSON.stringify(combined, null, 2));
            message.success('已复制成功');
        } catch {
            message.error('JSON 格式错误，复制失败');
        }
    };

    const popContent = (
        <div style={{ display: 'flex', flexDirection: 'column', maxWidth: 500 }}>
            {/* 上部分：标题、ID */}
            <div style={{ marginBottom: 6 }}>
                <Typography.Title level={5} style={{ margin: 0 }}>
                    {title}
                </Typography.Title>
                <Typography.Text type="secondary">商品ID: {itemId}</Typography.Text>
            </div>

            {/* 中部分：左属性、右图片*/}
            <div style={{ display: 'flex', gap: 16, flex: 1, borderTop: '1px dashed #eee' }}>
                <div style={{ flex: 1, paddingTop: 6 }}>
                    {Object.entries(goodDetail?.itemCategoryPropInfo ?? {}).map(([prop, val]) => (
                        <div key={prop} style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
                            <Typography.Text type="secondary" style={{ fontSize: 12 }}>
                                <strong>{prop}:</strong> {val ?? '-'}
                            </Typography.Text>
                        </div>
                    ))}
                </div>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px', paddingTop: 6, maxWidth: 300 }}>
                    {(goodDetail?.imageUrls ?? []).map((url, idx) => (
                        <Image
                            key={idx}
                            width={90}
                            height={90}
                            src={url}
                            style={{ objectFit: 'contain' }}
                        />
                    ))}
                </div>
            </div>
        </div>
    );

    return (
        <>
            <Popover content={popContent} placement="topLeft" trigger="hover">
                <div style={{ ...style, overflow: 'hidden', position: 'relative' }}>
                    <Badge.Ribbon text={ribbonText} color={ribbonColor}>
                        <Card hoverable style={{ width: '100%', height: '100%', position: 'relative' }}>
                            {imageUrl && (
                                <div style={{ width: '100%', height: 200 }}>
                                    <img
                                        alt={`product-${itemId}`}
                                        src={imageUrl}
                                        style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                                    />
                                </div>
                            )}
                            {item?.carItemIndex && (
                                <Card.Meta title={`(${item.carItemIndex}号商品) ${itemId}`} />
                            )}
                            {mode?.includes('debug') && (
                                <Typography.Link
                                    style={{ position: 'absolute', bottom: 8, right: 8 }}
                                    onClick={() => setModalVisible(true)}
                                >
                                    精排数据
                                </Typography.Link>
                            )}
                            {mode?.includes('replay') && (
                                <Typography.Link
                                    style={{ position: 'absolute', bottom: 8, right: 8 }}
                                >
                                    回放向量模型版本:{replayModelVersion}
                                </Typography.Link>
                            )}
                        </Card>
                    </Badge.Ribbon>
                </div>
            </Popover>

            <Modal
                title={
                    <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                        <span>精排数据</span>
                        <Link style={{ marginRight: 40 }}  onClick={handleCopyJson}>一键复制</Link>
                    </div>
                }
                visible={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={null}
                width={1000}
                styles={{ body: { maxHeight: '80vh', overflowY: 'auto' } }}
            >
                <Typography.Title level={5}>AgentRequest</Typography.Title>
                {requestTreeData.length > 0 ? (
                    <Tree treeData={requestTreeData} defaultExpandAll />
                ) : (
                    <Typography.Text>暂无AgentRequest数据</Typography.Text>
                )}
                <Typography.Title level={5} style={{ marginTop: 16 }}>AgentResponse</Typography.Title>
                {responseTreeData.length > 0 ? (
                    <Tree treeData={responseTreeData} defaultExpandAll />
                ) : (
                    <Typography.Text>暂无AgentResponse数据</Typography.Text>
                )}
            </Modal>
        </>
    );
};

export default GoodCard;
