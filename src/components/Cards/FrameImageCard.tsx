import React from 'react';
import { Card, Radio, Typography, Image } from 'antd';
import type { FrameImageItem } from '@/interfaces/Frame4';

export interface FrameImageCardProps {
    /** 图片数据 */
    imageItem: FrameImageItem;
    /** 当前选中的值 */
    value?: string;
    /** 选择变化回调 */
    onChange?: (value: string) => void;
    /** 整个组件的外层容器样式 */
    style?: React.CSSProperties;
    /** 标注状态：0-未标注，1-已标注 */
    labelStatus?: number;
    /** 标注后的帧字段值 */
    isGoodFrameLabel?: number;
}

export const FrameImageCard: React.FC<FrameImageCardProps> = ({
    imageItem,
    value,
    onChange,
    style = {},
    labelStatus = 0,
    isGoodFrameLabel,
}) => {
    const { imageUrl, frameIndex } = imageItem;

    return (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', ...style }}>
            {/* 图片卡片 */}
            <Card
                hoverable
                style={{
                    width: '100%',
                    maxWidth: 135,
                    height: 240,
                    padding: 0,
                    borderRadius: '6px',
                    overflow: 'hidden',
                }}
                bodyStyle={{ padding: 0, height: '100%' }}
            >
                <div style={{
                    width: '100%',
                    height: '100%',
                    overflow: 'hidden',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f5f5f5',
                    cursor: 'pointer'
                }}>
                    <Image
                        src={imageUrl}
                        alt={`Frame ${frameIndex}`}
                        style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            borderRadius: '6px'
                        }}
                        preview={{
                            mask: '点击预览',
                            maskClassName: 'custom-preview-mask'
                        }}
                    />
                </div>
            </Card>

            {/* 根据labelStatus控制选项显示 */}
            <div style={{ marginTop: 8, display: 'flex', flexDirection: 'column', gap: 4 }}>
                {labelStatus === 0 ? (
                    // 未标注状态：显示可选择的选项
                    <Radio.Group 
                        value={value || ''}
                        onChange={(e) => onChange?.(e.target.value)}
                        style={{ display: 'flex', flexDirection: 'column', gap: 4 }}
                    >
                        <Radio 
                            value="yes"
                            style={{
                                fontSize: '12px',
                                fontWeight: '500',
                                margin: 0,
                                padding: 0
                            }}
                        >
                            <div style={{
                                backgroundColor: '#e6f7ff',
                                border: '1px solid #91d5ff',
                                borderRadius: '6px',
                                padding: '4px 8px',
                                color: '#1890ff',
                                fontSize: '12px',
                                fontWeight: '500',
                                textAlign: 'center',
                                minWidth: '80px',
                                cursor: 'pointer'
                            }}>
                                是讲品帧
                            </div>
                        </Radio>
                        <Radio 
                            value="no"
                            style={{
                                fontSize: '12px',
                                fontWeight: '500',
                                margin: 0,
                                padding: 0
                            }}
                        >
                            <div style={{
                                backgroundColor: '#f6ffed',
                                border: '1px solid #b7eb8f',
                                borderRadius: '6px',
                                padding: '4px 8px',
                                color: '#389e0d',
                                fontSize: '12px',
                                fontWeight: '500',
                                textAlign: 'center',
                                minWidth: '80px',
                                cursor: 'pointer'
                            }}>
                                不是讲品帧
                            </div>
                        </Radio>
                    </Radio.Group>
                ) : (
                    // 已标注状态：显示标注结果
                    <div style={{
                        backgroundColor: (isGoodFrameLabel ?? frameIndex) === 1 ? '#e6f7ff' : '#f6ffed',
                        border: `1px solid ${(isGoodFrameLabel ?? frameIndex) === 1 ? '#91d5ff' : '#b7eb8f'}`,
                        borderRadius: '6px',
                        padding: '4px 8px',
                        color: (isGoodFrameLabel ?? frameIndex) === 1 ? '#1890ff' : '#389e0d',
                        fontSize: '12px',
                        fontWeight: '500',
                        textAlign: 'center',
                        minWidth: '80px'
                    }}>
                        {(isGoodFrameLabel ?? frameIndex) === 1 ? '是讲品帧' : '不是讲品帧'}
                    </div>
                )}
            </div>
        </div>
    );
};

export default FrameImageCard;
