import React, {useEffect, useMemo, useRef, useState} from 'react';
import {Card, Popover, Typography} from 'antd';
import {StatsData, TeamDetailRow} from "@/interfaces/interfaces";
import './StatisticCard.css';
import ReactECharts from 'echarts-for-react';
import {CheckCircleOutlined} from "@ant-design/icons";
import {PopoverDetailsTable} from "@/components/PopoverDetailsTable";
import {MiniLineChart} from "@/components/Charts/MiniLineChart";
import PopoverGradientAreaChart from "@/components/Charts/PopoverGradientAreaChart";

const { Text } = Typography;

interface FinishedStatusCardProps {
  data: StatsData|null;
  teamDetails: TeamDetailRow[];
  historyLabels: string[];
}

export const CompletedDemandStatCard: React.FC<FinishedStatusCardProps> = React.memo(({ data,teamDetails,historyLabels }) => {

    const [openPopoverKey, setOpenPopoverKey] = useState<'details' | 'chart' | null>(null);
    const chartRef = useRef<ReactECharts | null>(null);

    const name = data ? data.name : '';
    const value = data ? data.value : 0;
    const history = data ? data.history : [];

    const key = name.replace(/需求数$/, '');
    const relatedDetails = useMemo(() => teamDetails.filter(row => row.status?.includes(key)), [teamDetails, key]);


    const detailsContent = (
        <PopoverDetailsTable
            data={relatedDetails}
            onClose={() => setOpenPopoverKey(null)}
        />
    );

    useEffect(() => {
        const resizeChart = () => {
            chartRef.current?.getEchartsInstance().resize();
        };
        window.addEventListener('resize', resizeChart);
        return () => {
            window.removeEventListener('resize', resizeChart);
        };
    }, []);

    useEffect(() => {
        chartRef.current?.getEchartsInstance().resize();
    }, [history,historyLabels]);

    if (!data) {
        return <div>没有数据可显示</div>;
    }
    return (
            <Card
                style={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                }}
            >
                {/* 上部分 */}
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div className="card-title">
                        <CheckCircleOutlined />
                        <span
                            style={{marginLeft: 5}}>{name}</span>
                    </div>
                </div>

                {/* 下部分 */}
                <div style={{ flex: 1, display: 'flex' }}>
                    {/* 左半区：value、明细 */}
                    <div style={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                    }}>
                        {/* value */}
                        <div className="card-value"> {value} </div>

                    {/* 查看明细 */}
                        <Popover
                            content={detailsContent}
                            trigger="click"
                            placement="right"
                            open={openPopoverKey === 'details'}
                            onOpenChange={visible => setOpenPopoverKey(visible ? 'details' : null)}
                        >
                            <Text
                                onClick={() => setOpenPopoverKey('details')}
                                style={{ color: '#888', cursor: 'pointer', marginTop: 28 }}
                            >
                                查看明细
                            </Text>
                        </Popover>
                    </div>

                    {/* 右半区：图表*/}
                    <div style={{ flex: 1 }}>
                        <Popover
                            content={
                                <PopoverGradientAreaChart
                                    historyLabels={historyLabels}
                                    history={history}
                                    height={300}
                                    onClose={() => setOpenPopoverKey(null)}
                                />
                            }
                            trigger="click"
                            placement="right"
                            open={openPopoverKey === 'chart'}
                            onOpenChange={visible => setOpenPopoverKey(visible ? 'chart' : null)}
                        >
                            <MiniLineChart
                                history={history}
                                containerStyle={{ flex: 1, height: '100%' }}
                                onClick={() => setOpenPopoverKey('chart')}
                            />
                        </Popover>
                    </div>
                </div>
            </Card>
    );
});