import React, {useEffect, useRef, useState} from 'react';
import {Card, Popover, Tag} from 'antd';
import {UserOutlined} from '@ant-design/icons';
import './StatisticCard.css';
import ReactECharts from 'echarts-for-react';
import {PdStatsData} from "@/interfaces/interfaces";
import {MiniLineChart} from "@/components/Charts/MiniLineChart";
import PopoverGradientAreaChart from "@/components/Charts/PopoverGradientAreaChart";

interface PdStatCardProps {
    data: PdStatsData | null;
    historyLabels: string[];
}

export const PdStatCard: React.FC<PdStatCardProps> = React.memo(({ data,historyLabels }) => {

    const [openPopoverKey, setOpenPopoverKey] = useState<'details' | 'chart' | null>(null);
    const chartRef = useRef<ReactECharts | null>(null);

    useEffect(() => {
        const resizeChart = () => {
            chartRef.current?.getEchartsInstance().resize();
        };
        window.addEventListener('resize', resizeChart);
        return () => {
            window.removeEventListener('resize', resizeChart);
        };
    }, []);

    useEffect(() => {
        chartRef.current?.getEchartsInstance().resize();
    }, [data,historyLabels]);

    if(!data) return <div>没有数据可显示</div>;
    const {totalPd,history,internPd,onLoanPd,overtimePd } = data;

    return (
        <Card style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }} >
            {/* 上部分 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div className="card-title">
                        <UserOutlined />
                        <span style={{ marginLeft: 5 }}>投入PD</span>
                    </div>
            </div>

            {/* 下部分 */}
            <div style={{ display: 'flex', flex: 1 }}>
                {/* 左半区：数值 */}
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                    <div className="card-value" style={{ cursor: 'pointer' }}>
                        {totalPd}
                    </div>

                    <div style={{ display: 'flex', alignItems: 'center',marginTop:"28px" }}>
                        <span style={{ display: 'flex', alignItems: 'center', marginRight: '6px' }}>
                            <Tag bordered={false} color="cyan">实习</Tag>{internPd}
                        </span>
                        <span style={{ display: 'flex', alignItems: 'center', marginRight: '6px' }}>
                            <Tag bordered={false} color="magenta">加班</Tag>{overtimePd}
                        </span>
                        <span style={{ display: 'flex', alignItems: 'center', marginRight: '6px' }}>
                            <Tag bordered={false} color="processing">借调</Tag>{onLoanPd}
                        </span>
                    </div>
                </div>

                {/* 右半区：图表*/}
                <div style={{ flex: 1, overflow: 'hidden' }}>
                    <Popover
                        content={
                            <PopoverGradientAreaChart
                                historyLabels={historyLabels}
                                history={history}
                                height={300}
                                onClose={() => setOpenPopoverKey(null)}
                            />
                        }
                        trigger="click"
                        placement="right"
                        open={openPopoverKey === 'chart'}
                        onOpenChange={visible => setOpenPopoverKey(visible ? 'chart' : null)}
                    >
                        <MiniLineChart
                            history={history}
                            containerStyle={{ flex: 1, height: '100%' }}
                            onClick={() => setOpenPopoverKey('chart')}
                        />
                    </Popover>
                </div>
            </div>

        </Card>
    );
});