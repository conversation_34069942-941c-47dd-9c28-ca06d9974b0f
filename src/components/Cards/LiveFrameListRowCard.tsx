import React, {useMemo, useRef, useState} from "react";
import {Card, Col, Image, message, Modal, Row, Tree} from "antd";
import TextArea from "antd/es/input/TextArea";
import GoodCardSmall from "@/components/Cards/GoodCardSmall";
import type {KwaiPlayer} from '@ks-video/kwai-player-web/react';
import {KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import Link from "antd/es/typography/Link";
import {Frame} from "@/interfaces/Frame";


interface FrameListProps {
    frameList: Frame[];
    liveStreamId?: number; // 添加liveStreamId属性
}

const FrameListRowCard: React.FC<FrameListProps> = ({ frameList, liveStreamId }) => {

    // Ref：视频播放器
    const playerRef1 = useRef<KwaiPlayer | null>(null);
    // State：是否打开 JSON Modal
    const [openVideoModal, setOpenVideoModal] = useState<{ [key: number]: boolean }>({});
    // State：当前正在播放的视频 URL
    const [currentVideoUrl, setCurrentVideoUrl] = useState<string>('');
    // State：哪个 frame 的 JSON Modal 正在打开
    const [jsonFrame, setJsonFrame] = useState<Frame | null>(null);
    // Memo：倒序的 frameList
    const reversedFrames = useMemo(() => frameList.slice().reverse(), [frameList]);

    /**
     * 打开 抽帧视频 Modal 时，传入 index 和视频 URL
     * @param index
     * @param videoUrl
     */
    const handleOpenVideoModal = (index: number, videoUrl: string) => {
        setOpenVideoModal(prev => ({ ...prev, [index]: true }));
        setCurrentVideoUrl(videoUrl.replace(/^http:/, 'https:'));
    };

    /**
     * 关闭 抽帧视频 Modal 时，传入 index
     * @param index
     */
    const handleCloseVideoModal = (index: number) => {
        setOpenVideoModal(prev => ({ ...prev, [index]: false }));
        setCurrentVideoUrl('');
    };

    /**
     * 将 JSON 数据转换为 Tree 结构
     */
    const jsonToTreeData = (data: any, path = ""): any[] => {
        if (data === null || typeof data !== "object") {
            return [{ title: `${path}: ${String(data)}`, key: path }];
        }
        if (Array.isArray(data)) {
            return data.map((item, idx) => ({
                title:
                    typeof item === "object"
                        ? `[${idx}]`
                        : `[${idx}]: ${String(item)}`,
                key: `${path}[${idx}]`,
                children:
                    typeof item === "object" && item !== null
                        ? jsonToTreeData(item, `${path}[${idx}]`)
                        : undefined,
            }));
        }
        return Object.entries(data).map(([k, v]) => ({
            title:
                typeof v === "object" && v !== null
                    ? k
                    : `${k}: ${String(v)}`,
            key: path ? `${path}.${k}` : k,
            children:
                typeof v === "object" && v !== null
                    ? jsonToTreeData(v, path ? `${path}.${k}` : k)
                    : undefined,
        }));
    };

    /**
     * 复制 JSON 数据到剪切板
     */
    const handleCopyJson = () => {
        if (!jsonFrame) return;
        const text = JSON.stringify(jsonFrame, null, 2);
        navigator.clipboard
            ?.writeText(text)
            .then(() => message.success("已复制 JSON 数据到剪切板"))
            .catch(() => {
                // 兼容老浏览器
                const textarea = document.createElement("textarea");
                textarea.value = text;
                textarea.style.position = "fixed";
                textarea.style.opacity = "0";
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand("copy");
                document.body.removeChild(textarea);
                message.success("已复制 JSON 数据到剪切板");
            });
    };

    return (
        <div>
            {reversedFrames.map((frame) => (
                <Row key={frame.index} gutter={[6, 6]} style={{ marginBottom: 6 }}>
                    {/* 左卡：图片 */}
                    <Col>
                        <Card
                            title={`直播抽帧-${frame.index}`}
                            size="small"
                            style={{ width: 180, height: 1000, padding: 0 }}
                            bodyStyle={{ padding: 0 }}
                        >
                            {/* 上半部分：多个抽帧图片竖着显示 */}
                            <div style={{ 
                                width: "100%", 
                                height: 900, 
                                overflow: "hidden", 
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "center",
                                alignItems: "stretch",
                                gap: 2
                            }}>
                                {frame.screenSnapshotUrls.map((url, imgIndex) => (
                                    <Image
                                        key={imgIndex}
                                        src={url}
                                        alt={`直播抽帧-${imgIndex + 1}`}
                                        width="100%"
                                        height={frame.screenSnapshotUrls.length > 1 ? Math.floor(900 / frame.screenSnapshotUrls.length) - 2 : 900}
                                        style={{ objectFit: 'contain', display: 'block' }}
                                    />
                                ))}
                            </div>

                            {/* 下半部分：左右布局 */}
                            <div
                                style={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    padding: "15px",
                                    borderTop: '1px dashed #eee'
                                }}
                            >
                                <Link
                                    onClick={() => handleOpenVideoModal(frame.index, frame.clipVideoUrl)}
                                >
                                    抽帧视频
                                </Link>
                                <Link onClick={() => setJsonFrame(frame)}>
                                    JSON 数据
                                </Link>
                            </div>
                        </Card>
                    </Col>

                    {/* 中卡：ASR */}
                    <Col>
                        <Card
                            size="small"
                            title={<span style={{ fontSize: '14px', fontWeight: 'bold' }}>ASR 文本</span>}
                            style={{ height: 310, overflowY: 'auto', width: 250 }}
                        >
                            <TextArea
                                style={{ width: '100%' }}
                                value={frame.asrText}
                                rows={10}
                                readOnly
                                placeholder="ASR 文本将在此处展示"
                            />
                        </Card>
                    </Col>

                    {/* 右卡：最多五张商品图片 */}
                    <Col >
                        <Card
                            title="TOP5 匹配相似商品"
                            size="small"
                            style={{ height: 310, overflowY: 'auto', width: 800 }}
                        >
                            <div style={{display: "flex", gap: 2}}>
                                {(frame.topSimilarItems || []).slice(0, 5).map((item, idx) => (
                                    <GoodCardSmall
                                        key={idx}
                                        ribbonText={`匹配分数 ${item?.score ? item.score.toFixed(4) : '-'}\u00A0\u00A0`}
                                        ribbonColor="orange"
                                        item={item}
                                        style={{width: 150, height: 200, overflow: 'hidden'}}
                                    />
                                ))}
                            </div>
                        </Card>
                    </Col>
                </Row>
            ))}

            {/* 统一的视频播放Modal */}
            {reversedFrames.map((frame) => (
                <Modal
                    key={`modal-${frame.index}`}
                    title={`直播抽帧-${frame.index} 视频片段`}
                    open={openVideoModal[frame.index] || false}
                    footer={null}
                    onCancel={() => handleCloseVideoModal(frame.index)}
                    width={550}
                    bodyStyle={{ padding: 10, textAlign: "center", background: "#f5f5f5" }}
                    destroyOnClose
                >
                    <div  style={{
                        background: "#000",
                        borderRadius: 8,
                        width: 480,
                        height: 640,
                    }}>
                        <KwaiPlayerReact
                            ref={playerRef1}
                            src={currentVideoUrl}
                            controls
                            autoPlay={true}
                            preload="auto"
                            style={{
                                width: '100%',
                                height: '100%',
                            }}
                        />
                    </div>
                </Modal>
            ))}

            {/* JSON Modal，只展示被点击的那个 frame */}
            {jsonFrame && (
                <Modal
                    open={true}
                    width={800}
                    footer={null}
                    onCancel={() => setJsonFrame(null)}
                    bodyStyle={{ maxHeight: 650, overflowY: "auto" }}
                    title={
                        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                            <span>该帧原始数据</span>
                            <Link style={{ marginRight: 40 }}  onClick={handleCopyJson}>一键复制</Link>
                        </div>
                    }
                >
                    <Tree
                        treeData={jsonToTreeData(jsonFrame)}
                        defaultExpandAll
                        showLine
                    />
                </Modal>
            )}
        </div>
    );
};

export default FrameListRowCard;
