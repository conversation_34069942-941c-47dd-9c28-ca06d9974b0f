import React, {useState} from 'react';
import {Card, Popover, Typography} from 'antd';
import {FormOutlined, StockOutlined} from "@ant-design/icons";
import {StatsData, TeamDetailRow} from "@/interfaces/interfaces";
import {PopoverDetailsTable} from "@/components/PopoverDetailsTable";

interface StatCardProps {
    data: StatsData | null;
    teamDetails: TeamDetailRow[];
}

const { Text } = Typography;

export const StatCard: React.FC<StatCardProps> = React.memo(({ data, teamDetails }) => {
    const [popoverOpen, setPopoverOpen] = useState(false);

    if (!data) {
        return <div>没有数据可显示</div>;
    }

    const { name, value } = data;
    const key = name.replace(/需求数$/, '');
    const relatedDetails = teamDetails.filter(row =>
        row.status?.includes(key)
    );

    return (
        <div style={{ height: '100%' /* 撑满 grid row */ }}>
            <Card
                style={{
                    height: '100%',   /* 撑满外层 div */
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    backgroundColor: data.name === '进行中需求数' ? 'rgba(228, 91, 72, 0.3)' :  'rgba(255, 147, 0, 0.2)',
                    border: 'none',
                }}
            >
                {/* 顶部title */}
                <div className="card-title">
                    {name === '进行中需求数' ?  <StockOutlined />:<FormOutlined />}
                    <span style={{ marginLeft: 5 }}>{name}</span> {/* 调整图标和文字之间的距离 */}
                </div>

                {/* 中间大数字 */}
                <div
                    style={{
                        flex: 1,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        fontSize: 60,
                        fontWeight: 'bold',
                    }}
                >
                    {value}
                </div>

                {/* 底部 “查看明细” */}
                <div style={{ padding: '0 0 12px' }}>
                    <Popover
                        content={<PopoverDetailsTable data={relatedDetails} onClose={() => setPopoverOpen(false)} />}
                        trigger="click"
                        placement="right"
                        open={popoverOpen}
                        onOpenChange={(visible) => setPopoverOpen(visible)}
                    >
                        <Text
                            onClick={() => setPopoverOpen(true)}
                            style={{ color: '#888', cursor: 'pointer' }}
                        >
                            查看明细
                        </Text>
                    </Popover>
                </div>
            </Card>
        </div>
    );
});
