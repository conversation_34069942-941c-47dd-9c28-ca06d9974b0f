import React from 'react';
import { Badge, Card, Image, Popover, Tag, Typography } from 'antd';
import { MatchedGood } from "@/components/Cards/MatchedGoodsCard";

export interface GoodCardProps {
    ribbonText: string;
    ribbonColor?: string;
    item: MatchedGood;
    llmExplanation?: string;
    llmModelName?: string;
    /** 整个组件的外层容器样式（包括宽度） */
    style?: React.CSSProperties;
}

export const GoodCard: React.FC<GoodCardProps> = ({
                                                      ribbonText,
                                                      ribbonColor = 'red',
                                                      item,
                                                      llmExplanation,
                                                      llmModelName,
                                                      style = {},
                                                  }) => {
    const { itemId, goodDetail, carItemIndex } = item;
    const title = goodDetail?.title ?? `商品 ${itemId}`;
    const imageUrl = goodDetail?.imageUrl;
    const propsInfo = goodDetail?.itemCategoryPropInfo ?? {};
    const imageUrls = goodDetail?.imageUrls ?? [];

    const popContent = (
        <div style={{ display: 'flex', flexDirection: 'column', maxWidth: 500 }}>
            {/* 上部分：标题、ID */}
            <div style={{ marginBottom: 6 }}>
                <Typography.Title level={5} style={{ margin: 0 }}>
                    {title}
                </Typography.Title>
                <Typography.Text type="secondary">商品ID: {itemId}</Typography.Text>
                {carItemIndex !== null && carItemIndex !== undefined && (
                    <Typography.Text type="secondary" style={{ marginLeft: 10, color: '#1890ff' }}>
                        小黄车序号: {carItemIndex}
                    </Typography.Text>
                )}
            </div>

            {/* 中部分：左属性、右图片*/}
            <div style={{ display: 'flex', gap: 16, flex: 1, borderTop: '1px dashed #eee' }}>
                {/* 左：属性*/}
                <div style={{ flex: 1 }}>
                    <div style={{ paddingTop: 6 }}>
                        {Object.entries(propsInfo).map(([prop, val]) => (
                            <div key={prop} style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
                                <Typography.Text type="secondary" style={{ fontSize: 12 }}>
                                    <strong>{prop}:</strong> {val ?? '-'}
                                </Typography.Text>
                            </div>
                        ))}
                    </div>
                </div>
                {/* 右：图片*/}
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px 6px', paddingTop: 6, maxWidth: 300 }}>
                    {imageUrls.map((url, idx) => (
                        <Image
                            key={idx}
                            width={90}
                            height={90}
                            src={url}
                            style={{ objectFit: 'contain' }}
                        />
                    ))}
                </div>
            </div>

            {/* 下部分：LLM */}
            {(llmModelName || llmExplanation) && (
                <div style={{ marginTop: 6, paddingTop: 12, borderTop: '1px dashed #eee' }}>
                    {llmModelName && (
                        <Tag color="magenta" style={{ display: 'block', marginBottom: 8 }}>
                            模型名称：{llmModelName}
                        </Tag>
                    )}
                    {llmExplanation && (
                        <Tag
                            color="green"
                            style={{
                                display: 'block',
                                whiteSpace: 'normal',
                                wordBreak: 'break-word',
                            }}
                        >
                            模型解释：{llmExplanation}
                        </Tag>
                    )}
                </div>
            )}
        </div>
    );

    return (
        <Popover content={popContent} placement="topLeft" trigger="hover">
            <div style={{ overflow: 'hidden'}}>
                <Badge.Ribbon text={ribbonText} color={ribbonColor}>
                    <Card
                        hoverable
                        style={{ width: '100%', height: '100%' }}
                        cover={<Image alt="商品图片" src={imageUrl} style={{
                            width: '100%',      // 撇满卡片宽度
                            height: '200px',    // 固定封面区域高度，按需调整
                            objectFit: 'cover', // 保持比例，裁剪溢出
                            display: 'block'    // 去掉 img 默认的 inline 间隙
                        }} />}
                        styles={{
                            body: {
                                height: 50,            // 留出足够高度给文本
                                padding: '8px',        // 恢复一点内边距
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'flex-start', // 左对齐
                            },
                        }}
                    >
                        <Typography>
                            {carItemIndex !== null && carItemIndex !== undefined ? (
                                <div style={{ display: 'flex', flexDirection: 'column' }}>
                                    <div style={{ 
                                        color: '#1890ff', 
                                        fontWeight: 'bold',
                                        fontSize: '14px'
                                    }}>
                                        小黄车序号: {carItemIndex}
                                    </div>
                                    <div style={{ marginTop: '4px' }}>
                                        {itemId}
                                    </div>
                                </div>
                            ) : (
                                <div>商品ID: {itemId}</div>
                            )}
                        </Typography>
                    </Card>
                </Badge.Ribbon>
            </div>
        </Popover>
    );
};

export default GoodCard;
