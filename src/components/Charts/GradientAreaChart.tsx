import React, {useMemo} from 'react';
import {Area} from '@ant-design/plots';

interface GradientAreaChartProps {
    /** 已格式化的横坐标标签数组，如 ['2025-01-01', '2025-02-01', ...] 或 ['2025-01', '2025-02', ...] */
    historyLabels: string[];
    /** 对应的数值数组 */
    history: number[] | null;
    height?: number;
}

export const GradientAreaChart: React.FC<GradientAreaChartProps> = ({historyLabels,history,height = 400,}) => {
    // 如果没有标签或数值，直接提示
    if (!historyLabels || historyLabels.length === 0 || !history) {
        return <div>没有数据</div>;
    }

    // 构建绘图数据
    const data = useMemo(
        () =>
            historyLabels.map((label, idx) => ({
                period: label,
                value: history[idx] ?? 0,
            })),
        [historyLabels, history]
    );

    const config = {
        data,
        xField: 'period',
        yField: 'value',
        height,
        paddingRight:50,
        axis: {
            x: {
                line: true, // 是否显示轴线
                lineLineWidth: 2, // 轴线宽度
                labelFontSize: 12,
                tickLength:8,
                labelTransform: 'rotate(0)',
                transform: [{
                    type: 'hide',
                    keepHeader: true, // 保留第一个刻度值
                    keepTail: true, // 保留最后一个刻度值
                }],
            },
            y: {
                line: true, // 是否显示轴线
                lineLineWidth: 1, // 轴线宽度
                labelFontSize: 12,
                tickLength:8,
                labelTransform: 'rotate(0)',
            },
        },
        interaction: {
            tooltip: {
                marker: false,
            },
        },
        // 渐变填充
        style: {
            fill: 'linear-gradient(-90deg, white 0%, #4285F4 100%)',
        },
        line: {
            style: {
                stroke: '#4285F4',
                lineWidth: 2,
            },
        },
        point: {
            sizeField: 4,
            style: {
                stroke: '#4285F4',
                fill: '#fff',
            },
        },
    };

    return <Area {...config} />;
};
