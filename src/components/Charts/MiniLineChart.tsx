import React, {useEffect, useMemo, useRef} from 'react';
import ReactECharts from 'echarts-for-react';

export interface MiniLineChartProps {
    /** 折线图的历史数据 */
    history: number[] | null;
    /** 外层容器样式 */
    containerStyle?: React.CSSProperties;
    /** 点击时回调 */
    onClick?: () => void;
}

export const MiniLineChart: React.FC<MiniLineChartProps> = ({ history, containerStyle, onClick }) => {
    const chartRef = useRef<ReactECharts>(null);

    // 根据 history 生成图表配置，仅在 history 变化时重算
    const option = useMemo(() => {
        const data = history || [0, 0, 0, 0, 0, 0];
        return {
            grid: { left: 0, right: 0, top: 0, bottom: 0 },
            xAxis: {
                type: 'category',
                show: false,
                data: data.map((_, index) => index),
            },
            yAxis: {
                type: 'value',
                show: false,
            },
            series: [
                {
                    type: 'line',
                    data,
                    smooth: true,
                    symbol: 'none',
                    lineStyle: { color: '#4285F4', width: 2 },
                    areaStyle: {
                        color: {
                            type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(66,133,244,0.3)' },
                                { offset: 1, color: 'rgba(66,133,244,0.05)' },
                            ],
                        },
                    },
                },
            ],
        };
    }, [history]);

    // 响应式调整图表大小
    useEffect(() => {
        const handleResize = () => {
            chartRef.current?.getEchartsInstance().resize();
        };
        window.addEventListener('resize', handleResize);
        handleResize();
        return () => window.removeEventListener('resize', handleResize);
    }, [option]);

    return (
        <div onClick={onClick} style={{ width: '100%', height: '100%', ...containerStyle }}>
            <ReactECharts
                ref={chartRef}
                option={option}
                style={{ width: '100%', height: '100%' }}
            />
        </div>
    );
};
export default React.memo(MiniLineChart);
