import React from 'react';
import {Button} from 'antd';
import {GradientAreaChart} from "@/components/Charts/GradientAreaChart";

interface PopoverGradientAreaChartProps {
    historyLabels: string[];
    history: number[] | null;
    /** 弹层高度，单位 px，默认为 300 */
    height?: number;
    onClose: () => void;
}

const PopoverGradientAreaChart: React.FC<PopoverGradientAreaChartProps> = ({historyLabels,history,height = 300,onClose,}) => (
    <div style={{ position: 'relative', padding: '20px 0 30px 20px' }}>
        <GradientAreaChart
            historyLabels={historyLabels}
            history={history}
            height={height}
        />
        <Button
            type="link"
            style={{ position: 'absolute', bottom: 10, right: 10 }}
            onClick={onClose}
        >
            关闭
        </Button>
    </div>
);

export default PopoverGradientAreaChart;
