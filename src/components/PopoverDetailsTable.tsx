import React from 'react';
import {CloseOutlined} from '@ant-design/icons';
import {DetailsTable} from "@/components/DetailsTable";

export const PopoverDetailsTable = ({ data, onClose }) => {
    return (
        <div style={{ position: 'relative', padding: '10px 10px 30px', height: 500 }}>
            <div style={{
                height: 'calc(100% - 40px)', // 留出底部按钮区域高度
                overflowY: 'auto',
                padding: 10,
            }}>
                <DetailsTable data={data} />
            </div>

            {/* 使用 button 元素来处理点击事件 */}
            <button
                onClick={onClose}
                style={{
                    position: 'absolute',
                    bottom: 10,
                    right: 10,
                    cursor: 'pointer',
                    display: 'inline-flex',
                    alignItems: 'center',
                    color: '#888',
                    userSelect: 'none',
                    background: 'none',
                    border: 'none',
                    padding: 0,
                }}
            >
                <span
                    style={{
                        color: '#1890ff',
                        display: 'inline-flex',
                        alignItems: 'center',
                        userSelect: 'none',
                    }}
                >
                    关闭
                    <CloseOutlined style={{ marginLeft: 4 }} />
                </span>
            </button>
        </div>
    );
}
