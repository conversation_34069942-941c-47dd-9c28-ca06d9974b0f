// src/components/DetailsTable.tsx
import React, {useMemo} from 'react';
import {Button, Table, Tag, Typography} from 'antd';
import type {ColumnsType} from 'antd/es/table';
import type {TeamDetailRow, TimeRange} from '@/interfaces/interfaces';
import dayjs from "dayjs";

const { Text } = Typography;

interface DetailsTableProps {
    data: TeamDetailRow[];
}

export const DetailsTable: React.FC<DetailsTableProps> = ({ data }) => {
    // 1) 状态按钮映射
    const STATUS_BUTTON_MAP: Record<string, { label: string; color: string }> = {
        '待开发':     { label: '待开发', color: '#FFF1B8' },
        '进行中':     { label: '进行中', color: '#AED9A6' },
        '已完成':     { label: '已完成', color: '#D1C4E9' },
    };
    const renderStatusButton = (status: string) => {
        const info = STATUS_BUTTON_MAP[status];
        if (info) {
            return (
                <Button
                    style={{ backgroundColor: info.color, color: '#333', border: 'none' }}
                    size="small"
                >
                    {info.label}
                </Button>
            );
        }
        return <Text>{status}</Text>;
    };

    // 2) 优先级映射
    const PRIORITY_MAP: Record<string, { text: string; color: string }> = {
        '最高优': { text: 'P0', color: '#e45b48' },
        '高优':   { text: 'P1', color: '#eaa43d' },
        '中等':   { text: 'P2', color: '#f0c94f' },
        '较低':   { text: 'P3', color: '#50aec6' },
        '极低':   { text: 'P4', color: '#497cf4' },
    };
    const renderPriority = (priority: string) => {
        const info = PRIORITY_MAP[priority];
        if (info) {
            return (
                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <Tag color={info.color} style={{ padding: '0 5px', color: '#fff' }}>
            {info.text}
          </Tag>
          <Text style={{ marginLeft: 4 }}>{priority}</Text>
        </span>
            );
        }
        return <Text>{priority}</Text>;
    };


    const renderTimeRangeCell = (timeRange?: TimeRange) => {
        if (!timeRange?.startDate || !timeRange?.endDate) {
            return <Text type="secondary">—</Text>;
        }
        return (
            <Text>
                {timeRange.startDate} 至 {timeRange.endDate}
            </Text>
        );
    };

    const renderNameLink = (text: string, record: TeamDetailRow)  => {
        const handleClick = () => {
            const { timeRange, teamId } = record;
            const { startDate, endDate } = timeRange || {};
            // 清理当前 URL 仅保留 layoutType=1
            const cleanURL = new URL(window.location.href);
            cleanURL.search = '?layoutType=1';
            window.history.replaceState({}, '', cleanURL.toString());

            // 构造目标参数
            const params = new URLSearchParams();
            params.append('layoutType', '1');
            if (startDate) params.append('startTime', startDate);
            if (endDate) params.append('endTime', endDate);
            if (teamId) params.append('teamId', teamId);

            // 跳转
            window.location.href = `/team/schedule?${params.toString()}`;
        };

        return (
            <button
                type="button"
                onClick={handleClick}
                style={{
                    background: 'none',
                    border: 'none',
                    padding: 0,
                    margin: 0,
                    color: '#1890ff',
                    cursor: 'pointer',
                }}
            >
                {text}
            </button>
        );
    };

    const renderTeamLink = (_: any, record: TeamDetailRow) => {
        if (!record.teamLink) return <span>{record.name}</span>;
        return (
            <a
                href={record.teamLink}
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: '#1890ff', textDecoration: 'none', display: 'inline-flex', alignItems: 'center' }}
            >
                <img
                    src="https://w2.eckwai.com/udata/pkg/eshop/team-icon.svg"
                    alt="icon"
                    style={{ width: 18, height: 18, borderRadius: '50%', marginRight: 4 }}
                />
                {record.name}
            </a>
        );
    };

    // 5) 列定义
    const columns: ColumnsType<TeamDetailRow> = useMemo(() => [
        {
            title: '需求简称',
            dataIndex: 'name',
            key: 'name',
            fixed: 'left',
            width: 140,
            render: renderNameLink
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: (s) => renderStatusButton(s),
            sorter: (a, b) => (a.status ?? '').localeCompare(b.status ?? '')

        },
        {
            title: '优先级',
            dataIndex: 'priority',
            key: 'priority',
            width: 100,
            render: (p) => renderPriority(p),
            sorter: (a, b) => (a.priority ?? '').localeCompare(b.priority ?? '')
        },
        {
            title: '时间安排',
            dataIndex: 'timeRange',
            key: 'timeRange',
            width: 250,
            render: renderTimeRangeCell,
            sorter: (a, b) => {
                const aEnd = a.timeRange?.endDate
                    ? dayjs(a.timeRange.endDate).valueOf()
                    : 0;
                const bEnd = b.timeRange?.endDate
                    ? dayjs(b.timeRange.endDate).valueOf()
                    : 0;
                return aEnd - bEnd;
            },
            sortDirections: ['ascend', 'descend'],

        },
        {
            title: '提需组织',
            dataIndex: 'category',
            key: 'category',
            width: 120,
        },
        {
            title: '需求来源',
            dataIndex: 'source',
            key: 'source',
            width: 120,
        },
        {
            title: '投入(PD)',
            dataIndex: 'pdCount',
            key: 'pdCount',
            width: 50,
            sorter: (a, b) => (Number(a.pdCount) || 0) - (Number(b.pdCount) || 0),
        },
        {
            title: 'Team 链接',
            dataIndex: 'teamLink',
            key: 'teamLink',
            width: 200,
            render: renderTeamLink,
        },
        {
            title: '跟进人',
            dataIndex: 'owner',
            key: 'owner',
            width: 160,
            render: (owners: string[]) =>
                owners && owners.length > 0 ? <Text>{owners.join('、')}</Text> : <Text type="secondary">—</Text>,
            sorter: (a, b) => {
                const aText = Array.isArray(a.owner) ? a.owner.join('') : '';
                const bText = Array.isArray(b.owner) ? b.owner.join('') : '';
                return aText.localeCompare(bText);
            },
            fixed: 'right',
        },
    ], []);

    return (
        <Table<TeamDetailRow>
            columns={columns}
            dataSource={data}
            pagination={false}
            bordered
            size="small"
            rowKey="teamId"
        />
    );
};
