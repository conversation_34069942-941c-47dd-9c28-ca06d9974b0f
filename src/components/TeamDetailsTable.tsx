// src/components/TeamDetailsTable.tsx
import React from 'react';
import {Card} from 'antd';
import type {TeamDetailRow} from '@/interfaces/interfaces';
import {DetailsTable} from './DetailsTable';

interface TeamDetailsTableProps {
    data: TeamDetailRow[];
}

export const TeamDetailsTable: React.FC<TeamDetailsTableProps> = ({ data }) => {
    return (
        <Card
            size="small"
            title={<span style={{ fontSize: 16 }}>需求明细</span>}
            style={{ height: '100%', marginTop: 24 }}
        >
            <DetailsTable data={data} />
        </Card>
    );
};
