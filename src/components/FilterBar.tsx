import React, {useState} from 'react';
import {DatePicker, Select, Space, Typography} from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import zhCNDatePicker from 'antd/es/date-picker/locale/zh_CN';
import {Params} from '@/interfaces/interfaces';

const { Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

export const FilterBar: React.FC<{ filter: Params; onChange: (newFilter: Params) => void; }> = ({ filter, onChange }) => {
  const { scheduleListVO, historyPeriod, intervalType } = filter;
  const { startTime: currStart, endTime: currEnd, teamGroup } = scheduleListVO;
  const [dateUnit, setDateUnit] = useState<'week' | 'month' | 'year'>(
      intervalType as 'week' | 'month' | 'year' || 'week' // 默认按周
  );

  const handleDateChange = (dates) => {
    if (!dates || dates.length !== 2) return;
    const [start, end] = dates;
    onChange({
      ...filter,
      scheduleListVO: {
        ...scheduleListVO,
        startTime: start.startOf(dateUnit).format('YYYY-MM-DD'),
        endTime: end.endOf(dateUnit).format('YYYY-MM-DD'),
      },
    });
  };

  const handleDateUnitChange = (value) => {
    setDateUnit(value);
    const newStart = dayjs(currStart).startOf(value).format('YYYY-MM-DD');
    const newEnd = dayjs(currEnd).endOf(value).format('YYYY-MM-DD');
    onChange({
      ...filter,
      scheduleListVO: {
        ...scheduleListVO,
        startTime: newStart,
        endTime: newEnd,
      },
      intervalType: value,
    });
  };

  /** 当选择团队时，更新团队值 */
  const onTeamChange = (value: string) => {
    onChange({
      ...filter,
      scheduleListVO: {
        ...scheduleListVO,
        teamGroup: value,
      },
    });
  };
  const onHistoryPeriodChange = (value: number) => {
    onChange({
      ...filter,
      historyPeriod: value,
    });
  };
  return (
      <Space
          size="large"
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 16,
          }}
      >
        {/* 本期日期范围 */}
        <Space>
          <Text strong>本期：</Text>
          <RangePicker
              locale={zhCNDatePicker}
              value={[dayjs(currStart, 'YYYY-MM-DD'), dayjs(currEnd, 'YYYY-MM-DD')]}
              onChange={handleDateChange}
              format="YYYY-MM-DD"
              allowClear={false}
              picker={dateUnit}  // 动态设置为选择的日期单位
          />
        </Space>
        {/* 日期单位选择 */}
        <Space>
          <Text strong>日期单位：</Text>
          <Select value={dateUnit} onChange={handleDateUnitChange} style={{ width: 60 }}>
            <Option value="week">周</Option>
            <Option value="month">月</Option>
            <Option value="year">年</Option>
          </Select>
        </Space>

        {/* 团队选择 */}
        <Space>
          <Text strong>团队：</Text>
          <Select value={teamGroup} onChange={onTeamChange} style={{ width: 120 }}>
            <Option value="live">直播间</Option>
            <Option value="xhc">小黄车</Option>
            <Option value="backend">B端</Option>
            <Option value="pendant">挂件</Option>
          </Select>
        </Space>

        {/* 历史周期展示选择 */}
        <Space>
          <Text strong>周期展示数量：</Text>
          <Select value={historyPeriod} onChange={onHistoryPeriodChange} style={{ width: 60 }}>
            <Option value={4}>4</Option>
            <Option value={6}>6</Option>
            <Option value={8}>8</Option>
            <Option value={10}>10</Option>
          </Select>
        </Space>
      </Space>
  );
};
