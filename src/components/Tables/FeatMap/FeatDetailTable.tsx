import React, { useMemo } from 'react';
import { Button, Table, Tag, Tooltip, Typography } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  TEAM_SCHEDULE_PRIORITY_MAP,
  TEAM_SCHEDULE_STATUS_BUTTON_MAP,
} from '@/constants/styleConfig';
import type { TimeRange } from '@/interfaces/interfaces';

const { Text } = Typography;

export interface FeatDetailRow {
  id: number; // 用作 rowKey
  bizSite: string;
  bizDimension: string[];
  shortName: string;
  teamStatus: string;
  teamLink?: string | null;
  owner: string[];
  timeRange: TimeRange;
  teamId: string;
  lastedTime: string;
}

interface DetailsTableProps {
  data: FeatDetailRow[];
}

export const FeatDetailTable: React.FC<DetailsTableProps> = ({ data }) => {
  const renderTeamLink = (_: any, record: FeatDetailRow) => {
    if (!record.teamLink) return <span>{record.shortName}</span>;
    return (
      <a
        href={record.teamLink}
        target="_blank"
        rel="noopener noreferrer"
        style={{
          color: '#1890ff',
          textDecoration: 'none',
          display: 'inline-flex',
          alignItems: 'center',
        }}
      >
        <img
          src="https://w2.eckwai.com/udata/pkg/eshop/team-icon.svg"
          alt="icon"
          style={{ width: 18, height: 18, borderRadius: '50%', marginRight: 4 }}
        />
        {record.shortName}
      </a>
    );
  };

  const renderStatusButton = (status: string) => {
    const info = TEAM_SCHEDULE_STATUS_BUTTON_MAP[status];
    if (info) {
      return (
        <Button style={{ backgroundColor: info.color, color: '#333', border: 'none' }} size="small">
          {info.label}
        </Button>
      );
    }
    return <Text>{status}</Text>;
  };

  const renderNameLink = (text: string, record: FeatDetailRow) => {
    const { timeRange, teamId } = record;
    const { startDate, endDate } = timeRange || {};

    // 构造目标参数
    const params = new URLSearchParams();
    params.append('layoutType', '1');
    if (startDate) params.append('startTime', startDate);
    if (endDate) params.append('endTime', endDate);
    if (teamId) params.append('teamId', teamId);

    const targetUrl = `/team/schedule?${params.toString()}`;

    return (
      <Tooltip title="点击跳转到排期页面">
        <a href={targetUrl} target="_blank" rel="noopener noreferrer" style={{ color: '#1890ff' }}>
          {text}
        </a>
      </Tooltip>
    );
  };

  // 新增：渲染数组数据，每项一行
  const renderArrayWithLineBreaks = (items: string[]) => {
    if (!items || items.length === 0) return '-';

    return (
      <div>
        {items.map((item, index) => (
          <div key={index} style={{ lineHeight: '1.5', padding: '2px 0' }}>
            {item}
          </div>
        ))}
      </div>
    );
  };

  // 渲染最新时间
  const renderLastedTime = (time: string) => {
    if (!time) return '-';
    return (
      <Tooltip title={time}>
        <span>{time}</span>
      </Tooltip>
    );
  };

  const columns: ColumnsType<FeatDetailRow> = useMemo(
    () => [
      {
        title: '位点',
        dataIndex: 'bizSite',
        key: 'bizSite',
        fixed: 'left',
        width: 140,
      },
      {
        title: '业务维度',
        dataIndex: 'bizDimension',
        key: 'bizDimension',
        width: 120,
        render: (bizDimension: string[]) => renderArrayWithLineBreaks(bizDimension),
      },
      {
        title: '需求简称',
        dataIndex: 'shortName',
        key: 'shortName',
        width: 120,
        render: renderNameLink,
      },
      {
        title: '最新时间',
        dataIndex: 'lastedTime',
        key: 'lastedTime',
        width: 120,
        render: renderLastedTime,
        sorter: (a, b) => (a.lastedTime ?? '').localeCompare(b.lastedTime ?? ''),
      },
      {
        title: '需求状态',
        dataIndex: 'teamStatus',
        key: 'teamStatus',
        width: 200,
        render: (s) => renderStatusButton(s),
        sorter: (a, b) => (a.teamStatus ?? '').localeCompare(b.teamStatus ?? ''),
      },
      {
        title: '跟进人',
        dataIndex: 'owner',
        key: 'owner',
        width: 120,
        render: (owner: string[]) => renderArrayWithLineBreaks(owner),
      },
      {
        title: 'Team 链接',
        dataIndex: 'teamLink',
        key: 'teamLink',
        width: 200,
        render: renderTeamLink,
      },
    ],
    [],
  );

  return (
    <Table<FeatDetailRow>
      columns={columns}
      dataSource={data}
      pagination={false}
      bordered
      size="small"
      rowKey="id" // 确保每条数据都有唯一 id
    />
  );
};
