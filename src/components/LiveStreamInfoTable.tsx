import React from 'react';
import { Table, Tag, Typography, Avatar } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { UserOutlined } from '@ant-design/icons';

export interface LiveStreamInfo {
    level: string;
    sellerId: number;
    name: string | null;
    imgUrl: string | null;
    latestLiveStreamId: number;
    living: boolean;
}

interface LiveStreamInfoTableProps {
    data: LiveStreamInfo[];
    loading?: boolean;
    onLiveStreamIdClick?: (liveStreamId: number) => void; // Changed callback function parameter
}

export const LiveStreamInfoTable: React.FC<LiveStreamInfoTableProps> = ({
    data,
    loading = false,
    onLiveStreamIdClick
}) => {
    const getLevelColor = (level: string) => {
        switch (level) {
            case 'L0': return 'red';
            case 'L1': return 'orange';
            case 'L2': return 'blue';
            case 'L3': return 'green';
            case 'L4': return 'purple';
            default: return 'default';
        }
    };

    const columns: ColumnsType<LiveStreamInfo> = [
        {
            title: '主播等级',
            dataIndex: 'level',
            key: 'level',
            width: 100,
            render: (level: string) => (
                <Tag color={getLevelColor(level)}>
                    {level}
                </Tag>
            ),
        },
        {
            title: '主播ID',
            dataIndex: 'sellerId',
            key: 'sellerId',
            width: 120,
            render: (sellerId: number) => (
                <Typography.Text>
                    {sellerId}
                </Typography.Text>
            ),
        },
        {
            title: '主播用户名',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (name: string | null, record: LiveStreamInfo) => (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Avatar
                        size={32}
                        src={record.imgUrl}
                        icon={<UserOutlined />}
                        style={{ flexShrink: 0 }}
                    />
                    <Typography.Text
                        style={{
                            color: name ? undefined : '#999',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                        }}
                    >
                        {name || '未设置'}
                    </Typography.Text>
                </div>
            ),
        },
        {
            title: '最新直播ID',
            dataIndex: 'latestLiveStreamId',
            key: 'latestLiveStreamId',
            width: 150,
            render: (liveStreamId: number) => (
                <Typography.Link
                    style={{
                        fontSize: '14px',
                        fontWeight: 'bold',
                        color: '#1890FF',
                        textDecoration: 'underline',
                        cursor: 'pointer'
                    }}
                    onClick={() => {
                        if (onLiveStreamIdClick) {
                            onLiveStreamIdClick(liveStreamId);
                        }
                    }}>
                    {liveStreamId}
                </Typography.Link>
            ),
        },
        {
            title: '直播状态',
            dataIndex: 'living',
            key: 'living',
            width: 100,
            render: (living: boolean) => (
                <Tag color={living ? 'success' : 'default'}>
                    {living ? '直播中' : '已结束'}
                </Tag>
            ),
        },
    ];

    return (
        <Table<LiveStreamInfo>
            columns={columns}
            dataSource={data}
            rowKey="sellerId"
            loading={loading}
            size="small"
            pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            scroll={{ y: 400 }}
        />
    );
};
