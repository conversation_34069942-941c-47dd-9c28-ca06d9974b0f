import React from 'react';
import ReactDOM from 'react-dom';
import { BaomaiWorkbenchApp } from '@es/kpro-baomai-workbench';
import { ConfigProvider } from '@m-ui/react';
import { isProd, isPrt } from '@/common/env';
import { weblog } from '@/common/weblog';
import apiInstance from '@/common/request';
import { ErrorBoundary } from '@es/kpro-tech-common-event-collector';
import { setMainJsStart } from '@ks-radar/radar-util';
import theme from './theme';
import Demo from '@/pages/demo';
import zhCN from '@m-ui/react/lib/locale/zh_CN';
import '@es/design-token/dist/esm/entry';
import ScheduleTable from '@/pages/GoodTeam/Schedule';

import './index.css';
import TeamInsight from '@/pages/GoodTeam/Insight';
import StreamLabel from '@/pages/StreamLabel';
import AgentLive from '@/pages/AgentLive';
import AgentLiveV2 from '@/pages/AgentLiveV2';
import <PERSON><PERSON>iveV3 from '@/pages/AgentLiveV3';
import VideoLabel from '@/pages/VideoLabel';
import VideoList from '@/pages/VideoList';
import ScriptEditor from '@/pages/Script/ScriptEditor';
import ScriptExecute from '@/pages/Script/ScriptExecute';
import ScriptManagement from '@/pages/Script/ScriptManagement';
import ScriptList from '@/pages/Script/ScriptList';
import TeamTag from '@/pages/GoodTeam/Tag';
import FeatMap from '@/pages/GoodTeam/FeatMap';
import AgentLiveV4 from '@/pages/AgentLiveV4';
import AgentLiveV5 from '@/pages/AgentLiveV5';
import NoPermission from '@/pages/NoPermission';

// 主JS开始执行时间采集
setMainJsStart();
performance?.mark?.('mainJsStart');

ConfigProvider.config({
  prefixCls: theme['@ant-prefix'],
});

// @ts-ignore
ReactDOM.render(
  <ConfigProvider prefixCls={theme['@ant-prefix']} locale={zhCN}>
    <ErrorBoundary project={import.meta.PROJECT_NAME} logger={weblog}>
      <BaomaiWorkbenchApp
        apiInstance={apiInstance}
        loggerInstance={weblog}
        env={isProd ? 'prod' : isPrt ? 'prt' : 'test'}
        diluKconfKey="micro_A2KkrRloH4"
        microRouterPrefix="/page"
        diluMainAppname={import.meta.PROJECT_NAME}
        permissionUrl={'/page/industryCrm/MyOrganize'}
        permissionWhiteUrls={['/page/industryCrm/MyOrganize', '/page/industryCrm/MyPermissions']}
        sopRenderKconfKey={'baomaiMainAppSopConfig'}
        // IndexComponent={()=><>站点定制首页</>}
        showDefaultHeader={{
          showPermission: true,
          isLogoClick: true,
        }}
        extraRoutes={[
          {
            element: <ScheduleTable />,
            path: '/team/schedule',
            caseSensitive: true,
          },
          {
            element: <TeamInsight />,
            path: '/team/insight',
            caseSensitive: true,
          },
          {
            element: <TeamTag />,
            path: '/team/tag',
            caseSensitive: true,
          },
          {
            element: <FeatMap />,
            path: '/team/feat-map',
            caseSensitive: true,
          },
          {
            element: <NoPermission />,
            path: '/no-permission',
            caseSensitive: true,
          },
          {
            element: <NoPermission />,
            path: '/NoPermission',
            caseSensitive: true,
          },
          {
            element: <StreamLabel />,
            path: '/stream-label',
            caseSensitive: true,
          },
          {
            element: <VideoLabel />,
            path: '/video-label',
            caseSensitive: true,
          },

          {
            element: <VideoList />,
            path: '/video-list',
            caseSensitive: true,
          },
          {
            element: <AgentLive />,
            path: '/agent-live',
            caseSensitive: true,
          },
          {
            element: <AgentLiveV2 />,
            path: '/agent-live-v2',
            caseSensitive: true,
          },
          {
            element: <AgentLiveV3 />,
            path: '/agent-live-v3',
            caseSensitive: true,
          },
          {
            element: <AgentLiveV4 />,
            path: '/agent-live-v4',
            caseSensitive: true,
          },
          {
            element: <AgentLiveV5 />,
            path: '/agent-live-v5',
            caseSensitive: true,
          },
          {
            element: <ScriptEditor />,
            path: '/script-editor',
            caseSensitive: true,
          },
          {
            element: <ScriptManagement />,
            path: '/script-management',
            caseSensitive: true,
          },
          {
            element: <ScriptList />,
            path: '/script-list',
            caseSensitive: true,
          },
          {
            element: <ScriptExecute />,
            path: '/script-execute',
            caseSensitive: true,
          },
          {
            element: <Demo />,
            path: '/demo2',
            caseSensitive: true,
          },
        ]}
      ></BaomaiWorkbenchApp>
    </ErrorBoundary>
  </ConfigProvider>,
  document.getElementById('main_root'),
);
