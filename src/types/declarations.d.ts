/* CSS MODULES */
declare module '*.module.css' {
  const classes: Record<string, string>;
  export default classes;
}
declare module '*.module.scss' {
  const classes: Record<string, string>;
  export default classes;
}
declare module '*.module.sass' {
  const classes: Record<string, string>;
  export default classes;
}

declare module '*.module.styl' {
  const classes: Record<string, string>;
  export default classes;
}

/* CSS */
declare module '*.css';
declare module '*.scss';
declare module '*.sass';
declare module '*.less';
declare module '*.styl';

declare module '*.bmp' {
  const ref: string;
  export default ref;
}
declare module '*.gif' {
  const ref: string;
  export default ref;
}
declare module '*.jpg' {
  const ref: string;
  export default ref;
}
declare module '*.jpeg' {
  const ref: string;
  export default ref;
}
declare module '*.png' {
  const ref: string;
  export default ref;
}

/* IMAGES */
declare module '*.svg' {
  export const ReactComponent: React.FunctionComponent<
    React.SVGProps<SVGSVGElement> & { title?: string }
  >;
  const content: any;
  export default content;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
  readonly RADAR_KEY: string;
  readonly PROJECT_NAME: string;
  readonly REALM: string;
  readonly SUBREALM: string;
  readonly WEB_VERSION: string;
}
interface Window {
  chrome: Record<string, any>;
  _CDN_HOST_: string;
}
interface ImportMetaEnv {
  [key: string]: string | boolean | undefined;
  MODE: string;
  WEB_ENV: string;
}
