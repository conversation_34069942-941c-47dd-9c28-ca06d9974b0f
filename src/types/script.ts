// 脚本相关类型定义

export interface Variable {
  id: string;
  name: string; // 变量名，如 userId
  displayName: string; // 显示名
  type: 'input' | 'number' | 'boolean' | 'select' | 'radio' | 'textarea' | 'string';
  defaultValue?: any;
  options?: Array<{ label: string; value: any }>; // 用于 select 和 radio 类型
  required?: boolean; // 是否必填，默认为 false
}

// 额外输出配置
export interface ExtraOutput {
  type: 'qrcode' | 'image' | 'imageList' | 'link' | 'text' | 'json'; // 输出类型
  name: string; // 显示名称
  valueJsonPath: string; // JSON 路径，用于从结果中提取值
}

export interface ScriptData {
  id?: string;
  name?: string;
  content: string; // Groovy脚本内容
  variables?: Variable[];
  extraOutput?: ExtraOutput[]; // 额外输出配置
}

export interface ExecuteResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime?: number;
}

export interface ExecuteParams {
  scriptContent: string;
  variables: Record<string, any>;
}
