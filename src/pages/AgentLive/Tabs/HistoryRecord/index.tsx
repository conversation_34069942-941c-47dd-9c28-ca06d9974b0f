// src/components/HistoryRecord.tsx
import React from 'react';
import {Col, ConfigProvider, Row, Table, Tooltip} from 'antd';
import type {ColumnsType} from 'antd/lib/table';
import {ITEM_LINK_PREFIX, timeStringToSeconds} from "@/pages/AgentLive/constant";
import zhCN from "antd/es/locale/zh_CN";
import {KwaiPlayer} from "@ks-video/kwai-player-web/react";

export interface HistoryItem {
    liveStreamId: number;
    startTime: string;
    endTime: string;
    itemId: string | number;
}

interface HistoryRecordProps {
    itemTimeRecordsAlgo?: HistoryItem[];
    itemTimeRecordsAgent?: HistoryItem[];
    playerRef: React.RefObject<KwaiPlayer | null>;
}

interface HistoryTableProps {
    title: string;
    data: HistoryItem[];
    playerRef: React.RefObject<KwaiPlayer | null>;
}

const HistoryTable: React.FC<HistoryTableProps> = ({ title, data, playerRef }) => {
    // 定义跳转函数，使用共用的 playerRef
    const handleJump = (seconds: number) => {
        const inst = playerRef.current as any;
        if (!inst) return;
        inst.currentTime = seconds;
    };
    const columns: ColumnsType<HistoryItem> = [
        // { title: '直播间ID', dataIndex: 'liveStreamId', key: 'liveStreamId', width: 140 },
        {
            title: '商品ID',
            dataIndex: 'itemId',
            key: 'itemId',
            width: 140,
            render: (itemId: string | number) => (
                <a
                    href={`${ITEM_LINK_PREFIX}${encodeURIComponent(itemId)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    {itemId}
                </a>
            ),
        },
        {
            title: '开始讲解时间',
            dataIndex: 'startTime',
            key: 'startTime',
            width: 120,
            sorter: (a, b) => timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime),
            defaultSortOrder: 'ascend',
            render: (_: any, record) => (
                <Tooltip title="点击定位">
                    <a onClick={() => handleJump(timeStringToSeconds(record.startTime))}>
                        {record.startTime}
                    </a>
                </Tooltip>
            ),
        },
        {
            title: '结束讲解时间',
            dataIndex: 'endTime',
            key: 'endTime',
            width: 120,
            sorter: (a, b) => timeStringToSeconds(a.endTime) - timeStringToSeconds(b.endTime),
            render: (_: any, record) => (
                <Tooltip title="点击定位">
                    <a onClick={() => handleJump(timeStringToSeconds(record.endTime))}>
                        {record.endTime}
                    </a>
                </Tooltip>
            ),
        },
        {
            title: '持续时间(秒)',
            key: 'duration',
            width: 120,
            sorter: (a, b) => {
                const da = timeStringToSeconds(a.endTime) - timeStringToSeconds(a.startTime);
                const db = timeStringToSeconds(b.endTime) - timeStringToSeconds(b.startTime);
                return da - db;
            },
            render: (_, record) => {
                const duration = timeStringToSeconds(record.endTime) - timeStringToSeconds(record.startTime);
                return duration >= 0 ? duration : '--';
            },
        },
    ];

    return (

        <div style={{ width: '100%', marginBottom: 24 }}>
            <h3>{title}</h3>
            <ConfigProvider locale={zhCN}>
                <Table<HistoryItem>
                    columns={columns}
                    dataSource={data}
                    rowKey={record => `${record.liveStreamId}-${record.itemId}-${record.startTime}`}
                    pagination={{
                        pageSize: 15,
                        showSizeChanger: false, // 不允许用户修改
                        showQuickJumper: true,  // 可选：支持跳页
                        showTotal: (total, range) => `共 ${total} 条，当前显示第 ${range[0]}-${range[1]} 条`,
                    }}
                    scroll={{ x: 'max-content' }}
                    size="small"
                />
            </ConfigProvider>
        </div>
    );
};

const HistoryRecord: React.FC<HistoryRecordProps> = ({ itemTimeRecordsAlgo = [], itemTimeRecordsAgent = [], playerRef }) => {
    return (
        <Row gutter={16}>
            <Col key="algo" span={12}>
                <HistoryTable title="算法策略记录" data={itemTimeRecordsAlgo} playerRef={playerRef} />
            </Col>
            <Col key="agent" span={12}>
                <HistoryTable title="Agent策略记录" data={itemTimeRecordsAgent} playerRef={playerRef} />
            </Col>
        </Row>
    );

};

export default HistoryRecord;