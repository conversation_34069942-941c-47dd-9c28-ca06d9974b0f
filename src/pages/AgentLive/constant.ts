export const TEST_LIVESTREAM_IDS = [
      13696770968,
      13784852820,
];

export const STORAGE_KEY = 'searchHistory';

export const LIVESTREAM_ID_KEY = 'liveStreamId';
// 商品链接前缀
export  const ITEM_LINK_PREFIX = "https://lego.corp.kuaishou.com/page/fangzhou/goods/360search?itemId="

// 监控链接
export const MONITOR_LINK = "https://tianwen.corp.kuaishou.com/dashboard/37128/detail?activeTab=1&currentEnv=prod"
/**
 * 将时间字符串（"HH:mm:ss"、"mm:ss" 或 秒数字符串）转换为秒数
 * @param time 时间字符串，格式可以是 "HH:mm:ss"、"mm:ss" 或 单位秒字符串
 * @returns 对应的秒数
 */
export function timeStringToSeconds(time: string): number {
      const parts = time.split(':').map(Number);
      if (parts.length === 3) {
            return parts[0] * 3600 + parts[1] * 60 + parts[2];
      }
      if (parts.length === 2) {
            return parts[0] * 60 + parts[1];
      }
      return Number(time) || 0;
}

/**
 * 将秒数格式化为 "HH:mm:ss" 字符串
 * @param seconds 要格式化的秒数
 * @returns 格式化后的字符串，始终包含两位小时、分钟、秒钟
 */
export function formatSecondsToHMS(seconds: number): string {
      const h = Math.floor(seconds / 3600).toString().padStart(2, '0');
      const m = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
      const s = Math.floor(seconds % 60).toString().padStart(2, '0');
      return `${h}:${m}:${s}`;
}
// 毫秒转 HH:mm:ss
export const formatMillSecTime = (ms: number) => {
      const totalSeconds = Math.max(0, Math.floor(ms / 1000)); // 转成秒并避免负数
      const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
      const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, '0');
      const seconds = String(totalSeconds % 60).padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
};