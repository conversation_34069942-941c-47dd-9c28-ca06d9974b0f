import React, {use<PERSON><PERSON>back, useEffect, useRef, useState} from 'react';
import Flex from '@/components/Flex';
import {Input, Tabs, Tooltip, Typography} from '@m-ui/react';
import StickyBox from 'react-sticky-box';
import {FieldTimeOutlined, QuestionCircleOutlined} from '@m-ui/icons';
import type {KwaiPlayer} from '@ks-video/kwai-player-web/react';
import {KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import cls from 'classnames/bind';
import style from './index.module.less';
import {AutoComplete, Avatar, Card, message, Space} from "antd";
import * as API from '@/common/API/AgentLiveAPI';
import type {GoodItem} from "@/pages/AgentLive/Tabs/AgentLiveInfo";
import AgentLiveInfo from "@/pages/AgentLive/Tabs/AgentLiveInfo";
import type {MatchedGood} from "@/components/Cards/MatchedGoodsCard";
import type {HistoryItem} from "@/pages/AgentLive/Tabs/HistoryRecord";
import {formatSecondsToHMS, LIVESTREAM_ID_KEY, STORAGE_KEY, TEST_LIVESTREAM_IDS} from "@/pages/AgentLive/constant";
import {LiveStreamInfoDrawer} from '@/components/LiveStreamInfoDrawer';
import type {LiveStreamInfo} from '@/components/LiveStreamInfoTable';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {Frame} from "@/interfaces/Frame";
import * as StreamLabelAPI from '@/common/API/StreamLabelAPI';

const { Meta } = Card;
const cx = cls.bind(style);

/**
 * 将字符串列表转换为 AutoComplete options，并在每项前添加图标
 */
function mapHistoryToOptions(list: string[]): { value: string; label: React.ReactNode }[] {
  return list.map(v => ({
    value: v,
    label: (
        <Space>
          <FieldTimeOutlined />
          <span>{v}</span>
        </Space>
    ),
  }));
}

export default function AgentLive() {

  const playerRef = useRef<KwaiPlayer | null>(null);

  const storedLiveStreamId = localStorage.getItem(LIVESTREAM_ID_KEY) ?? '';

  const [searchValue, setSearchValue] = useState(storedLiveStreamId);
  const [liveStreamId, setLiveStreamId] = useState<number>(() => {
    return storedLiveStreamId ? Number(storedLiveStreamId) : 0;
  });
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [activeKey, setActiveKey] = useState<'current'|'history'>('current');
  const [videoUrls, setVideoUrls] = useState<string[]>([]);
  const [srcIndex, setSrcIndex] = useState(0);
  const videoSrc = videoUrls[srcIndex] || '';

  // 商品列表和视频URL
  const [products, setProducts] = useState<GoodItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<GoodItem[]>(products);


  // 白盒化帧结果
  const[frameList,setFrameList] = useState<Frame[]>([]);

  // Agent策略匹配结果
  const [matchedGoodsAgent,setMatchedGoodsAgent] = useState<MatchedGood>();
  // 算法策略匹配结果
  const [matchedGoodsAlgo,setMatchedGoodsAlgo] = useState<MatchedGood>();

  // 主播正在讲解的商品
  const [currentItem,setCurrentItem] = useState<MatchedGood| null>(null);
  // 历史记录
  const [itemTimeRecordsAlgo, setItemTimeRecordsAlgo] = useState<HistoryItem[]>([]);
  const [itemTimeRecordsAgent, setItemTimeRecordsAgent] = useState<HistoryItem[]>([]);


  const [startTime, setStartTime] = useState<number>(0);
  const [endTime, setEndTime] = useState<number>(0);

  const timestampRef = useRef<number | null>(null);
  const [specifiedTimestamp,setSpecifiedTimestamp] = useState(0);

  const [isLiving,setIsLiving] = useState(false);
  // 新增：标记用户是否在“回看”模式
  const [isReplaying, setIsReplaying]     = useState(false);

  // 搜索历史
  const [options, setOptions] = useState<{ value: string; label: React.ReactNode }[]>([]);
  const [open, setOpen] = useState(false);
  const autoCompleteRef = useRef<any>(null);

  // 新增：主播列表抽屉状态
  const [anchorDrawerVisible, setAnchorDrawerVisible] = useState(false);


  // 主播信息、开播时间
  const [sellerInfo, setSellerInfo] = useState<any>(null);
  const [liveStreamStartTimeStr, setLiveStreamStartTimeStr] = useState('');

  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const [windowTracInfoList,setWindowTraceInfoList] = useState<any>([])

  const [liveUtcInfo, setLiveUtcInfo] = useState<{
    startUtc?: number;
    playbackTotalMs?: number;
    totalUtcMs?: number;
  } | null>(null);

  //获取 UTC 基准数据
  useEffect(() => {
    const fetchUtcData = async () => {
      if (!liveStreamId) return;
      try {
        const utcResponse = await StreamLabelAPI.LabelUTCTime({ liveStreamId });
        let parsedData = utcResponse.data;
        if (typeof parsedData === "string") {
          parsedData = JSON.parse(parsedData);
        }
        setLiveUtcInfo(parsedData);
      } catch (err) {
        console.error('获取 UTC 数据失败：', err);
      }
    };
    fetchUtcData();
  }, [liveStreamId]);

  //播放秒数转为 UTC 时间字符串
  const secondsToUtcTime = (seconds: number) => {
    if (!liveUtcInfo) return '——';
    const { startUtc, playbackTotalMs, totalUtcMs } = liveUtcInfo;
    if (!startUtc || !playbackTotalMs || !totalUtcMs) return '--';

    // 计算 UTC 时间（核心逻辑：按比例映射本地时间到 UTC 时间）
    const ratio = seconds / (playbackTotalMs / 1000);
    const utcTime = new Date(startUtc + ratio * totalUtcMs);

    // 格式化 UTC 时间为 "YYYY-MM-DD HH:MM:SS"
    return `${utcTime.getUTCFullYear()}-${
        (utcTime.getUTCMonth() + 1).toString().padStart(2, '0')
    }-${
        utcTime.getUTCDate().toString().padStart(2, '0')
    } ${
        utcTime.getUTCHours().toString().padStart(2, '0')
    }:${
        utcTime.getUTCMinutes().toString().padStart(2, '0')
    }:${
        utcTime.getUTCSeconds().toString().padStart(2, '0')
    }`;
  };
  useEffect(() => {
    setFilteredProducts(products);
  }, [products]);


  // —— 当 liveStreamId 变化时，保存到 localStorage ——
  useEffect(() => {
    if (!liveStreamId) return;
    localStorage.setItem(LIVESTREAM_ID_KEY, String(liveStreamId));
  }, [liveStreamId]);

  /**
   * 接口调用
   * @param id 直播流 ID
   * @param timestamp 可选的回放时间（单位：秒），仅回放模式下传入
   */
  const fetchInfo = useCallback(async (id: number, timestamp?: number, isByUrl?: boolean) => {
        try {
          const params: { liveStreamId: number; timestamp: number | null } = {
            liveStreamId: id,
            timestamp: timestamp ?? 60,
          };
          const info = await API.LiveInfo(params);

          if(timestamp == undefined || isByUrl){
            setVideoUrls(info?.[0].videoUrl ?? []);
            setSrcIndex(0);
            if (timestamp == undefined) {
              timestampRef.current = 60;
            }else{
              timestampRef.current = timestamp;
            }
            setSellerInfo(info?.[0].sellerInfo);
            setLiveStreamStartTimeStr(info?.[0].liveStreamStartTime ?? '');
          }

          setStartTime(info?.[0].segmentStartTimeRelatedSec ?? 0);
          setEndTime(info?.[0].segmentEndTimeRelatedSec ?? 0);

          setWindowTraceInfoList(info);
          return info;

        } catch (err) {
          message.error('接口调用失败');
          console.error(err);
          return null; // 添加返回值
        }
      },
      [] // 保持空依赖数组，因为内部使用的都是setState函数
  );

  // 新增：获取主播列表的函数
  const fetchLiveStreamInfo = useCallback(async (): Promise<LiveStreamInfo[]> => {
    try {
      const result = await API.LiveStreamInfo({});
      return result || [];
    } catch (error) {
      console.error('获取主播列表失败:', error);
      throw error;
    }
  }, []);

  const onTimeUpdate = useCallback((_: any) => {
    const inst = (playerRef.current as any)
    if (!inst) return
    let t: number;
    if (typeof inst.currentTime === 'number') {
      t = inst.currentTime;
    } else if (typeof inst.getCurrentTime === 'function') {
      t = inst.getCurrentTime();
    } else {
      t = 0;
    }
    setCurrentTime(t)
  }, [playerRef])

  const onLoadedMetadata = useCallback(() => {
    const inst = playerRef.current
    if (!inst) return
    const d = (inst as any)?.duration
    if (typeof d === 'number') setDuration(d)
    console.log('视频元数据加载 -- duration: ', d)
    // 如果有 timestamp 参数，跳转到对应时间并自动播放
    if (timestampRef.current !== null) {
      try {
        (inst as any).currentTime = timestampRef.current;
        if(isLiving) {
          setIsReplaying(true)
        }
      } catch (e) {
        console.warn('设置跳转时间失败', e);
      }
    }
    // (inst as any)?.play();
  }, [])



  const handleTabClick = (key: string) => {
    if (key === 'history') {
      // fetchHistory(); // 重新调用接口
    }
  };

  const handleTabChange = (key: string) => {
    setActiveKey(key as 'current' | 'history');
  };

  useEffect(() => {
    const urlLiveStreamId = searchParams.get("liveStreamId");
    const urlTimestamp = searchParams.get("timestamp");

    if (urlLiveStreamId) {
      saveToHistory(urlLiveStreamId);
      const idNum = Number(urlLiveStreamId);
      setLiveStreamId(idNum);
      setSearchValue(String(idNum));
      if (urlTimestamp) {
        const tsNum = Number(urlTimestamp);
        timestampRef.current = tsNum;
        setSpecifiedTimestamp(tsNum);
      }
      fetchInfo(idNum, urlTimestamp ? Number(urlTimestamp) : undefined, true);
      return; // 如果有 URL 参数，直接返回，不走 localStorage 流程
    }

    // 否则走本地记录
    if (storedLiveStreamId) {
      const id = Number(storedLiveStreamId);
      setLiveStreamId(id);
      fetchInfo(id);
    }

    const raw = localStorage.getItem(STORAGE_KEY);
    if (raw) {
      try {
        const list: string[] = JSON.parse(raw);
        setOptions(mapHistoryToOptions(list));
      } catch {}
    }
  }, [searchParams]);

  // 2. helper：把新值写入 localStorage 并去重、限长
  const saveToHistory = useCallback((val: string) => {
    if (!val) return;
    const raw = localStorage.getItem(STORAGE_KEY);
    let list: string[] = raw ? JSON.parse(raw) : [];
    list = [val, ...list.filter(x => x !== val)];
    if (list.length > 10) list = list.slice(0, 10);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(list));
    setOptions(mapHistoryToOptions(list));
  }, []);

  // 3. 处理搜索
  const onSearch = useCallback((val: string) => {
    setSearchValue(val);
    const id = Number(val);
    setLiveStreamId(id);
    fetchInfo(id);
    saveToHistory(val);
    setOpen(false);
    navigate(`/agent-live?layoutType=1`, { replace: true });
  }, [fetchInfo, setLiveStreamId, saveToHistory, navigate]);

  // 新增：处理直播ID点击的回调函数
  const handleLiveStreamIdClick = useCallback((liveStreamId: number) => {
    setSearchValue(String(liveStreamId));
    setLiveStreamId(Number(liveStreamId));
    fetchInfo(Number(liveStreamId));
    setAnchorDrawerVisible(false); // 关闭抽屉
  }, [fetchInfo]);

  return (
    <Flex vertical gap={16} className={cx('video')}>
      <Flex className={cx('columns')} gap={16} style={{ display: 'flex', flex: 1, width: '100%' ,paddingRight:"40px"}}>

        <Flex className={cx('left')} vertical gap={16} style={{ flex: '0 0 420px' }}>
          <StickyBox offsetTop={20} className={cx('sticky-box')}>

            {/* 标题 + Tooltip */}
            <Flex gap={8} align="center" style={{ padding: '0 16px', height: 30, lineHeight: '30px', fontSize: '18px',marginBottom:16 }}>
              <Typography.Text strong className={cx('title')}>
                智能直播讲解
              </Typography.Text>
              <Tooltip title="输入 liveStreamId 搜索">
                <QuestionCircleOutlined style={{ color: '#999' }} />
              </Tooltip>
              <AutoComplete
                  ref={autoCompleteRef}
                  style={{ width: '100%', maxWidth: 220 ,marginLeft:10}}
                  options={options}
                  open={open}
                  value={searchValue}
                  onFocus={() => setOpen(options.length > 0)}      // 聚焦时如果有历史就展开
                  onBlur={() => setTimeout(() => setOpen(false), 200)} // 200ms 延迟，保留点击选项时间
                  onSelect={value => onSearch(value)}               // 选中历史记录
                  onSearch={val => {
                    setSearchValue(val);
                    // 动态过滤下拉
                    const history: string[] = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                    setOptions(mapHistoryToOptions(
                        history.filter(item => item.includes(val))
                    ));
                  }}
              >
                <Input.Search
                    placeholder='请输入直播间ID'
                    onChange={e => setSearchValue(e.target.value)}
                    enterButton
                    onSearch={onSearch}
                />
              </AutoComplete>
            </Flex>

            {/* 推荐直播间*/}
            <Flex gap={8} align="center" style={{ padding: '0 16px', height: 30, lineHeight: '30px', fontSize: '18px',marginBottom:16 }}>
              <Typography.Text type="secondary" style={{ fontSize: 14  }}>
                试试这些：
              </Typography.Text>
              {TEST_LIVESTREAM_IDS.map(item => (
                  <Typography.Link
                      key={item}
                      type="secondary"
                      style={{
                        fontSize: 14,
                        marginRight: '8px', // 每个链接右侧添加空格
                      }}
                      onClick={() => {
                        setSearchValue(String(item));
                        setLiveStreamId(Number(item));
                        fetchInfo(Number(item));
                      }}
                  >
                    {item}
                  </Typography.Link>
              ))}
              <Typography.Link
                  type="secondary"
                  style={{
                    fontSize: 14,
                    marginLeft: 8,
                    color: '#1890FF', // 蓝色（可替换为任意蓝色值）
                    fontWeight: 'bold' // 加粗
                  }}
                  onClick={() => setAnchorDrawerVisible(true)}
              >
                查看更多
              </Typography.Link>
            </Flex >

            {/* 播放器 */}
            <Flex vertical gap={8} className={cx('player')} style={{ padding: '0 16px' }}>
              <div style={{ position: 'relative', width: '100%', height: '100%' }}>
              <KwaiPlayerReact
                  key={`${liveStreamId}-${srcIndex}`}
                  style={{ width: '100%', height: '100%' }}
                  id="player"
                  ref={playerRef}
                  src={videoSrc}
                  autoPlay={true}
                  showProgress={false}
                  controls={ true}
                  preload="auto"
                  onError={() => { if (srcIndex + 1 < videoUrls.length) setSrcIndex(srcIndex + 1); }}
                  onTimeUpdate={onTimeUpdate}
                  onLoadedMetadata={onLoadedMetadata}
              />
                <div
                    style={{
                      position: 'absolute',
                      bottom: 40, // 距离视频底部 40px（假设视频时间在底部 30px 位置，这里在其上方 10px）
                      left: 10,   // 距离视频左侧 10px（与视频时间左侧对齐）
                      background: 'rgba(0,0,0,0.6)',
                      color: '#fff',
                      padding: '3px 8px',
                      borderRadius: 4,
                      fontSize: 12,
                      pointerEvents: 'none',
                      zIndex: 10,
                    }}
                >
                  {secondsToUtcTime(currentTime)}
                </div>
              </div>
            </Flex>

            <Flex className={cx('info')} vertical gap={0}>
            {!!sellerInfo && (
                <Card>
                  <Meta
                      avatar={<Avatar src={sellerInfo.imgUrl} size={64}/>}
                      title={`主播ID：${sellerInfo.id}`}
                      description={
                        <>
                          <div>主播名称：{sellerInfo.name}</div>
                          <div>开播时间：{liveStreamStartTimeStr}</div>
                        </>
                      }
                  />
                </Card>)}
            </Flex>
          </StickyBox>
        </Flex>


        <Flex className={cx('right')} style={{ justifyContent: 'center', flex: '0 0 1260px' }} >
          <Tabs activeKey={activeKey} onChange={handleTabChange} onTabClick={handleTabClick}>
            <Tabs.TabPane tab="当前商品识别" key="current">
              <AgentLiveInfo
                  liveStreamId={liveStreamId??0}
                  onSelectChange={keys => console.log('select', keys)}
                  // playerRef={videoRefForChild}
                  playerRef={playerRef}
                  duration={playerRef.current?.duration ?? 0}
                  playerCurrentTime={currentTime}
                  fetchInfo={fetchInfo}
                  timestamp={specifiedTimestamp}

                  startTime={formatSecondsToHMS(startTime)}
                  endTime={formatSecondsToHMS(endTime)}
                  windowTraceInfoList = {windowTracInfoList}
                  isLiving={isLiving}
                  itemTimeRecords={itemTimeRecordsAlgo ?? []} //TODO 进度条先默认展示算法策略记录，之后再改
                  // isReplaying={isReplaying}
                  isReplaying= {true}
                  onReplayingChange={setIsReplaying}

              />
            </Tabs.TabPane>
            {/*<Tabs.TabPane tab="历史识别记录" key="history">*/}
            {/*  <HistoryRecord*/}
            {/*      itemTimeRecordsAlgo={itemTimeRecordsAlgo ?? []}*/}
            {/*      itemTimeRecordsAgent={itemTimeRecordsAgent ?? []}*/}
            {/*      playerRef={playerRef}*/}
            {/*  />*/}
            {/*</Tabs.TabPane>*/}
         </Tabs>

        </Flex>
      </Flex>

      {/* 主播列表抽屉 */}
      <LiveStreamInfoDrawer
        visible={anchorDrawerVisible}
        onClose={() => setAnchorDrawerVisible(false)}
        fetchLiveStreamInfo={fetchLiveStreamInfo}
        onLiveStreamIdClick={handleLiveStreamIdClick}
      />
    </Flex>
  );
}
