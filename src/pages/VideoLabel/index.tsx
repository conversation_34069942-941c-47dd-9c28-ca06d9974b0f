import React, { useCallback, useEffect, useRef, useState } from 'react';
import Flex from '@/components/Flex';
import { Tabs, Typography, Tag } from '@m-ui/react';
import StickyBox from 'react-sticky-box';
import type { KwaiPlayer } from '@ks-video/kwai-player-web/react';
import { KwaiPlayerReact } from '@ks-video/kwai-player-web/react';
import cls from 'classnames/bind';
import style from './index.module.less';
import type { ProductItem } from '@/pages/VideoLabel/Tabs/CurrentRecord';
import CurrentRecord from '@/pages/VideoLabel/Tabs/CurrentRecord';
import Shelf from '@/pages/VideoLabel/Tabs/Shelf';
import { message, Button, Tooltip, Modal, Input, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import * as API from '@/common/API/VideoLabelAPI';
import { jsonParse } from 'safe-json-parse-and-stringify';

const cx = cls.bind(style);

export default function VideoLabel() {
  const playerRef = useRef<KwaiPlayer | null>(null);

  const [activeKey, setActiveKey] = useState<'current' | 'history' | 'shelf'>('current');
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [history, setHistory] = useState<any[]>([]); // HistoryItem[] 替换为 any[]

  // 商品列表和视频URL
  const [products, setProducts] = useState<ProductItem[]>([]);

  // 货架商品列表
  const [shelfProducts, setShelfProducts] = useState<ProductItem[]>([]);
  const [shelfTotal, setShelfTotal] = useState(0);
  const [shelfPagination, setShelfPagination] = useState({
    pageNo: 1,
    pageSize: 50,
  });

  const [videoUrls, setVideoUrls] = useState<string[]>([]);
  const [queryHasVideo, setQueryHasVideo] = useState(false); // url 里是否携带 video_url
  const [srcIndex, setSrcIndex] = useState(0);
  const videoSrc = videoUrls[srcIndex] || '';

  // URL 参数
  type UrlParams = Record<string, string>;
  const [urlParams, setUrlParams] = useState<UrlParams>({});

  // 标注状态
  const [labelStatus, setLabelStatus] = useState<number>(0); // 0: 未完成, 1: 已完成

  // 已选择的商品ID - 为每个tab维护独立的选择状态
  const [currentSelectedRowKeys, setCurrentSelectedRowKeys] = useState<string[]>([]);
  const [shelfSelectedRowKeys, setShelfSelectedRowKeys] = useState<string[]>([]);

  // 保存 sliceInfo 信息，用于传递给子组件
  const [sliceInfo, setSliceInfo] = useState<any>(null);

  // 提交标注记录相关
  const [submitting, setSubmitting] = useState(false);

  // 搜索相关状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filteredProducts, setFilteredProducts] = useState<ProductItem[]>(products);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setFilteredProducts(products);
  }, [products]);

  // 商品搜索逻辑
  const handleSearch = useCallback(
    (keyword: string) => {
      setSearchKeyword(keyword);
      if (!keyword) {
        // 根据当前tab设置对应的数据
        if (activeKey === 'shelf') {
          setFilteredProducts(shelfProducts);
        } else {
          setFilteredProducts(products);
        }
        return;
      }
      const kw = keyword.toLowerCase();

      // 根据当前tab选择要搜索的数据源
      const dataSource = activeKey === 'shelf' ? shelfProducts : products;

      // 过滤数据
      const filteredList = dataSource.filter((item) => {
        // 标题匹配
        if (item?.title?.toLowerCase?.()?.includes?.(kw)) return true;
        // ID 匹配
        if (String(item.itemId).includes(keyword)) return true;
        // 属性匹配：key 或 value 包含
        if (
          Object.entries(item.itemCategoryPropInfo || {}).some(([prop, val]) => {
            const v = val ?? '';
            return prop?.toLowerCase()?.includes?.(kw) || v?.toLowerCase()?.includes?.(kw);
          })
        ) {
          return true;
        }
        return false;
      });
      setFilteredProducts(filteredList);
    },
    [activeKey, products, shelfProducts],
  );

  // 时间格式化（将时间戳解析为 YYYY-MM-DD HH:mm:ss）
  const formatTime = useCallback((ts?: number) => {
    if (!ts || Number.isNaN(ts)) return '-';
    const d = new Date(ts);
    const pad = (n: number) => String(n).padStart(2, '0');
    const y = d.getFullYear();
    const m = pad(d.getMonth() + 1);
    const day = pad(d.getDate());
    const h = pad(d.getHours());
    const min = pad(d.getMinutes());
    const s = pad(d.getSeconds());
    return `${y}-${m}-${day} ${h}:${min}:${s}`;
  }, []);

  // 当 products 或 shelfProducts 变化时，如果有搜索关键词，重新应用搜索过滤
  useEffect(() => {
    if (searchKeyword) {
      handleSearch(searchKeyword);
    } else {
      // 根据当前tab设置对应的数据
      if (activeKey === 'shelf') {
        setFilteredProducts(shelfProducts);
      } else {
        setFilteredProducts(products);
      }
    }
  }, [products, shelfProducts, searchKeyword, activeKey, handleSearch]);

  // 获取货架商品列表
  const fetchShelfProducts = useCallback(
    async (sliceInfoData?: any, paginationParams?: any) => {
      try {
        // 使用传入的sliceInfoData或当前的sliceInfo状态
        const currentSliceInfo = sliceInfoData || sliceInfo;

        // 检查sliceInfo是否存在
        if (!currentSliceInfo) {
          console.warn('sliceInfo不存在，无法获取货架商品');
          return;
        }

        const params = {
          pageNo: paginationParams?.pageNo || shelfPagination.pageNo,
          pageSize: paginationParams?.pageSize || shelfPagination.pageSize,
          userId: currentSliceInfo.userId, // 这里需要根据实际情况设置userId
          sliceId: currentSliceInfo.sliceId, // 新增sliceId参数
          labelType: currentSliceInfo.labelType, // 新增labelType参数
        };
        const result = await API?.OnShelfList(params);
        console.log('shelfProducts result:', result);

        // 设置总数
        setShelfTotal(result.totalCount || 0);

        setShelfProducts(result.itemList ?? []);
      } catch (error) {
        console.error('获取货架商品失败:', error);
        message.error('获取货架商品失败');
      }
    },
    [sliceInfo, shelfPagination],
  );

  const fetchInfo = useCallback(async (sliceId: number, labelType: number) => {
    // if (!liveStreamId) return;
    setLoading(true);
    try {
      const labelQueryParams = { sliceId, labelType };
      const sliceInfoData = (await API?.LabelQuery(labelQueryParams))?.data;

      // 保存 sliceInfo 到状态中
      setSliceInfo(sliceInfoData);

      if (sliceInfoData.videoCdnUri) {
        // 设置标注状态
        setLabelStatus(sliceInfoData.labelStatus || 0);

        // 设置已选择的商品ID
        if (sliceInfoData.labelItemResult) {
          try {
            const labelItemResult = jsonParse(sliceInfoData.labelItemResult, []);
            setCurrentSelectedRowKeys(labelItemResult || []);
          } catch (error) {
            console.error('解析labelItemResult失败:', error);
            setCurrentSelectedRowKeys([]);
          }
        } else {
          setCurrentSelectedRowKeys([]);
        }

        // 设置已选择的货架商品ID
        if (sliceInfoData.labelShelfResult) {
          try {
            const labelShelfResult = jsonParse(sliceInfoData.labelShelfResult, []);
            setShelfSelectedRowKeys(labelShelfResult || []);
          } catch (error) {
            console.error('解析labelShelfResult失败:', error);
            setShelfSelectedRowKeys([]);
          }
        } else {
          setShelfSelectedRowKeys([]);
        }

        setVideoUrls([sliceInfoData.videoCdnUri]);
        setSrcIndex(0);
        setQueryHasVideo(true);
        const params = {
          liveStreamId: sliceInfoData.liveStreamId,
          specifiedTime: sliceInfoData.specifiedTime,
        };

        const zeroParams = {
          liveStreamId: sliceInfoData.liveStreamId,
          authorId: sliceInfoData.userId,
          startTime: sliceInfoData.sliceStartTime,
          endTime: sliceInfoData.sliceEndTime,
        };

        // 获取指定时间的商品（恢复为仅一次调用）
        const itemInfo = await API.OnSaleItemList(params);
        const zeroItemInfo = await API.OnZeroList(zeroParams);

        // 合并：将 zeroItemInfo 映射为表格适配结构，放前面且序号置为 0
        const zeroItems = (zeroItemInfo?.zeroItemList ?? []).map((it: any) => ({
          serialNumber: 0,
          itemId: it?.itemId,
          title: it?.title ?? '',
          imageUrl:
            Array.isArray(it?.imageUrls) && it.imageUrls.length > 0 ? it.imageUrls?.[0] : '',
          imageUrls: Array.isArray(it?.imageUrls) ? it.imageUrls : [],
          itemPrice: it?.itemPrice ?? '',
          itemCategoryPropInfo: {},
          anchorRecordStartTime: null,
          anchorRecordEndTime: null,
        }));
        const baseItems = itemInfo?.itemInfos ?? [];
        const combinedItemInfos = [...zeroItems, ...baseItems];
        console.log('combinedItemInfos:', combinedItemInfos);

        // 如果有 mmuItemIdList，将匹配的商品置顶
        let sortedItemInfos = combinedItemInfos;

        const mmuItemIdList = sliceInfoData.mmuItemIdList;
        const merchantItemIdList = sliceInfoData.merchantItemIdList;

        // 第一次排序：将mmuItemIdList中的商品置顶（在merchantItemIdList之后）
        if (mmuItemIdList && mmuItemIdList.length > 0 && sortedItemInfos.length > 0) {
          sortedItemInfos = sortedItemInfos.sort((a, b) => {
            // 添加空值检查
            if (!a || !b) return 0;
            // 如果 a 的 itemId 存在于 mmuItemIdList 中，则 a 排在前面
            if (mmuItemIdList.includes(a.itemId)) return -1;
            // 如果 b 的 itemId 存在于 mmuItemIdList 中，则 b 排在前面
            if (mmuItemIdList.includes(b.itemId)) return 1;
            // 其他情况保持原有顺序
            return 0;
          });
        }
        // 第二次排序：将merchantItemIdList中的商品置顶
        if (merchantItemIdList && merchantItemIdList.length > 0 && sortedItemInfos.length > 0) {
          sortedItemInfos = sortedItemInfos.sort((a, b) => {
            // 添加空值检查
            if (!a || !b) return 0;
            // 如果 a 的 itemId 存在于 merchantItemIdList 中，则 a 排在前面
            if (merchantItemIdList.includes(a.itemId)) return -1;
            // 如果 b 的 itemId 存在于 merchantItemIdList 中，则 b 排在前面
            if (merchantItemIdList.includes(b.itemId)) return 1;
            // 其他情况保持原有顺序
            return 0;
          });
        }

        setProducts(sortedItemInfos);
        setFilteredProducts(sortedItemInfos);

        // fetchInfo完成后，获取货架商品数据
        fetchShelfProducts(sliceInfoData);
      }
    } catch {
      message.error('获取商品信息失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 解析url参数
  useEffect(() => {
    const usp = new URLSearchParams(window.location.search || '');
    const all: UrlParams = {};
    usp.forEach((v, k) => {
      all[k.trim()] = v.trim();
    });
    setUrlParams(all);

    const sliceIdStr = all['sliceId'] ?? '0';
    const sliceIdNum = Number(sliceIdStr) || 0;

    const labelTypeStr = all['labelType'] ?? '0';
    const labelTypeNum = Number(labelTypeStr) || 0;

    const videoUrl = all['videoUrl'];

    if (videoUrl) {
      setSrcIndex(0);
      setQueryHasVideo(true);
    }

    // 先获取在车商品数据，然后在回调中获取货架商品数据
    fetchInfo(sliceIdNum, labelTypeNum);
  }, [fetchInfo]);

  const onTimeUpdate = useCallback(
    (_: any) => {
      const inst = playerRef.current as any;
      if (!inst) return;
      let t: number;
      if (typeof inst.currentTime === 'number') {
        t = inst.currentTime;
      } else if (typeof inst.getCurrentTime === 'function') {
        t = inst.getCurrentTime();
      } else {
        t = 0;
      }
      setCurrentTime(t);
    },
    [playerRef],
  );

  const onLoadedMetadata = useCallback(() => {
    const inst = playerRef.current;
    if (!inst) return;
    const d = (inst as any)?.duration;
    if (typeof d === 'number') setDuration(d);
  }, []);

  const handleTabClick = (key: string) => {
    if (key === 'history') {
      // fetchHistory(); // 重新调用接口
    }
  };

  // 重新获取标注状态
  const refreshLabelStatus = useCallback(async () => {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const sliceId = urlParams.get('sliceId');
      const labelType = urlParams.get('labelType');
      if (sliceId) {
        const labelQueryParams = {
          sliceId: parseInt(sliceId),
          labelType: labelType ? parseInt(labelType) : 0,
        };
        const sliceInfo = (await API?.LabelQuery(labelQueryParams))?.data;

        // 更新标注状态
        setLabelStatus(sliceInfo.labelStatus || 0);

        // 更新已选择的商品ID
        if (sliceInfo.labelItemResult) {
          try {
            const labelItemResult = jsonParse(sliceInfo.labelItemResult, []);
            setCurrentSelectedRowKeys(labelItemResult || []);
          } catch (error) {
            console.error('解析labelItemResult失败:', error);
            setCurrentSelectedRowKeys([]);
          }
        } else {
          setCurrentSelectedRowKeys([]);
        }

        // 更新已选择的货架商品ID
        if (sliceInfo.labelShelfResult) {
          try {
            const labelShelfResult = jsonParse(sliceInfo.labelShelfResult, []);
            setShelfSelectedRowKeys(labelShelfResult || []);
          } catch (error) {
            console.error('解析labelShelfResult失败:', error);
            setShelfSelectedRowKeys([]);
          }
        } else {
          setShelfSelectedRowKeys([]);
        }
      }
    } catch (error) {
      console.error('重新获取标注状态失败:', error);
      message.error('重新获取标注状态失败');
    }
  }, []);

  const handleTabChange = (key: string) => {
    setActiveKey(key as 'current' | 'history' | 'shelf');
  };

  // 处理货架商品分页
  const handleShelfPageChange = useCallback(
    (page: number, pageSize: number) => {
      const newPagination = { pageNo: page, pageSize };
      setShelfPagination(newPagination);
      fetchShelfProducts(sliceInfo, newPagination);
    },
    [fetchShelfProducts, sliceInfo],
  );

  // 真实的提交逻辑
  const doSubmit = async () => {
    setSubmitting(true);
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const sliceId = urlParams.get('sliceId');
      const labelType = urlParams.get('labelType');

      const params = {
        sliceId: sliceId ? parseInt(sliceId) : 0,
        labelItemResult: currentSelectedRowKeys,
        labelShelfResult: shelfSelectedRowKeys,
        labelType: labelType ? parseInt(labelType) : 0,
      };
      const result = await API.LabelInsert(params);
      if (result.result === '1' || result.result === 1) {
        message.success('当前标注提交成功');
        refreshLabelStatus(); // 调用刷新状态回调
      } else {
        message.error(`当前标注提交失败: ${result.error_msg}`);
      }
    } catch (err) {
      message.error('当前标注提交异常');
    } finally {
      setSubmitting(false);
    }
  };

  // 无商品提交逻辑
  const doSubmitNoItems = async () => {
    setSubmitting(true);
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const sliceId = urlParams.get('sliceId');
      const labelType = urlParams.get('labelType');

      const params = {
        sliceId: sliceId ? parseInt(sliceId) : 0,
        labelItemResult: [], // 空数组表示无商品
        labelShelfResult: [], // 空数组表示无货架商品
        labelType: labelType ? parseInt(labelType) : 0,
      };
      const result = await API.LabelInsert(params);
      if (result.result === '1' || result.result === 1) {
        message.success('无商品标注提交成功');
        refreshLabelStatus(); // 调用刷新状态回调
      } else {
        message.error(`无商品标注提交失败: ${result.error_msg}`);
      }
    } catch (err) {
      message.error('无商品标注提交异常');
    } finally {
      setSubmitting(false);
    }
  };

  // 处理提交
  const handleSubmit = () => {
    // 如果没选商品，弹 confirm
    if (currentSelectedRowKeys.length === 0) {
      Modal.confirm({
        title: '未选择标注商品',
        content: (
          <span>
            您当前<span style={{ color: 'red' }}>未选择</span>任何标注商品，是否确认提交？
          </span>
        ),
        okText: '确认提交',
        cancelText: '取消',
        onOk: doSubmit,
      });
    } else {
      doSubmit();
    }
  };

  // 处理无商品提交
  const handleSubmitNoItems = () => {
    Modal.confirm({
      title: '确认无商品标注',
      content: '确认该视频片段中没有任何商品需要标注吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: doSubmitNoItems,
    });
  };

  return (
    <Flex vertical gap={16} className={cx('video')}>
      <Flex
        className={cx('top')}
        gap={16}
        align="center"
        style={{ flexWrap: 'nowrap', paddingTop: '20px' }}
      >
        <Flex
          gap={8}
          align="center"
          style={{
            flex: 'none',
            float: 'left',
            paddingRight: '15px',
            paddingLeft: '20px',
            height: 30,
            lineHeight: '30px',
            fontSize: '18px',
          }}
        >
          <Typography.Text strong className={cx('title')}>
            短视频标注
          </Typography.Text>
        </Flex>
      </Flex>

      <Flex
        className={cx('columns')}
        gap={16}
        style={{ display: 'flex', flex: 1, width: '100%', paddingRight: '40px' }}
      >
        <Flex className={cx('left')} vertical gap={16} style={{ flex: '0 0 420px' }}>
          <StickyBox offsetTop={20} className={cx('sticky-box')}>
            <Flex vertical gap={8} align="center" className={cx('player')}>
              <KwaiPlayerReact
                key={urlParams.videoUrl}
                style={{
                  width: '100%',
                  height: '100%',
                }}
                id="player"
                className="player"
                ref={playerRef}
                onError={() => {
                  if (srcIndex + 1 < videoUrls.length) setSrcIndex(srcIndex + 1);
                }}
                src={videoSrc}
                autoPlay={false}
                preload="auto"
                onTimeUpdate={onTimeUpdate}
                onLoadedMetadata={onLoadedMetadata}
              />
            </Flex>
          </StickyBox>
        </Flex>

        <Flex
          className={cx('right')}
          flex={1}
          vertical
          style={{ minWidth: 0, justifyContent: 'center' }}
        >
          <Spin spinning={loading} tip="加载中...">
            {/* 标注状态栏 - 移到最外层 */}
            {sliceInfo && (
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Button color={labelStatus === 1 ? 'green' : 'red'} variant="dashed">
                    标注状态：{labelStatus === 1 ? '已标注' : '未标注'}
                  </Button>
                  <Tooltip title={'请先勾选视频中讲解的商品，如果有多个，请全部选中'}>
                    <Button
                      color="orange"
                      variant="solid"
                      disabled={
                        currentSelectedRowKeys.length === 0 && shelfSelectedRowKeys.length === 0
                      }
                      loading={submitting}
                      style={{ marginLeft: 16 }}
                      onClick={handleSubmit}
                    >
                      保存
                    </Button>
                  </Tooltip>
                  <Tooltip title={'如果您确定该视频片段中没有任何商品需要标注，请点击此按钮'}>
                    <Button
                      color="orange"
                      variant="solid"
                      loading={submitting}
                      style={{ marginLeft: 16 }}
                      onClick={handleSubmitNoItems}
                    >
                      找不到对应商品
                    </Button>
                  </Tooltip>
                  {sliceInfo?.sliceStartTime && sliceInfo?.sliceEndTime && (
                    <span style={{ marginLeft: 12, color: '#666' }}>
                      切片讲解时间：{formatTime(sliceInfo.sliceStartTime)} -{' '}
                      {formatTime(sliceInfo.sliceEndTime)}
                    </span>
                  )}
                </div>

                {/* 搜索组件 - 只在非shelf tab时显示 */}
                {activeKey !== 'shelf' && (
                  <Input.Search
                    placeholder="商品信息搜索"
                    value={searchKeyword}
                    onChange={(e) => {
                      const v = e.target.value;
                      setSearchKeyword(v);
                      if (v === '') {
                        handleSearch('');
                      }
                    }}
                    onSearch={handleSearch}
                    enterButton={<SearchOutlined />}
                    style={{ width: 250, marginLeft: 'auto' }}
                  />
                )}
              </div>
            )}

            <Tabs activeKey={activeKey} onChange={handleTabChange} onTabClick={handleTabClick}>
              <Tabs.TabPane tab="在车商品" key="current">
                {sliceInfo && (
                  <CurrentRecord
                    dataList={activeKey === 'current' ? filteredProducts : products}
                    dataHistory={history}
                    liveStreamId={0}
                    sliceId={0}
                    sliceEndTime={0}
                    onSelectChange={(keys) => {
                      setCurrentSelectedRowKeys(keys);
                    }}
                    // onHistoryRefresh={fetchHistory}
                    onHistoryRefresh={null}
                    selectedRowKeys={currentSelectedRowKeys}
                    mmuItemId={sliceInfo?.mmuItemId}
                    mmuItemIdList={sliceInfo?.mmuItemIdList}
                    merchantItemIdList={sliceInfo?.merchantItemIdList}
                    candidateRecordResults={sliceInfo?.candidateRecordResults}
                  />
                )}
              </Tabs.TabPane>
              <Tabs.TabPane tab="商家货架" key="shelf">
                <Shelf
                  dataList={activeKey === 'shelf' ? filteredProducts : shelfProducts}
                  onSelectChange={(keys) => {
                    setShelfSelectedRowKeys(keys);
                  }}
                  selectedRowKeys={shelfSelectedRowKeys}
                  mmuItemId={sliceInfo?.mmuItemId}
                  mmuItemIdList={sliceInfo?.mmuItemIdList}
                  merchantItemIdList={sliceInfo?.merchantItemIdList}
                  candidateRecordResults={sliceInfo?.candidateRecordResults}
                  sliceId={sliceInfo?.sliceId}
                  labelType={sliceInfo?.labelType}
                  total={shelfTotal}
                  pagination={{
                    current: shelfPagination.pageNo,
                    pageSize: shelfPagination.pageSize,
                    total: shelfTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range?.[0]}-${range?.[1]} 条/共 ${total} 条`,
                  }}
                  onPageChange={handleShelfPageChange}
                />
              </Tabs.TabPane>
            </Tabs>
          </Spin>
        </Flex>
      </Flex>
    </Flex>
  );
}
