import React, { useEffect, useMemo, useState } from 'react';
import { Button, ConfigProvider, Image, Input, message, Modal, Table, Tooltip, Tag } from 'antd';
import { InfoCircleOutlined, SearchOutlined } from '@ant-design/icons';

import * as API from '@/common/API/VideoLabelAPI';
import * as StreamAPI from '@/common/API/StreamLabelAPI';
import { ITEM_LINK_PREFIX } from '@/pages/VideoLabel/constant';
import zhCN from 'antd/es/locale/zh_CN';
import { useSearchParams } from 'react-router-dom';

export interface ProductItem {
  serialNumber: number;
  itemId: number;
  title: string;
  imageUrl: string;
  imageUrls: string[];
  itemPrice: string;
  itemCategoryPropInfo: Record<string, string | undefined>;
  anchorRecordStartTime: number | null;
  anchorRecordEndTime: number | null;
}

interface ShelfProps {
  dataList: ProductItem[];
  onSelectChange: (selectedKeys: string[]) => void;
  selectedRowKeys?: string[];
  mmuItemId?: number;
  mmuItemIdList?: number[];
  merchantItemIdList?: number[]; // 新增merchantItemIdList参数
  candidateRecordResults?: number[]; // 候选记录结果ID列表
  sliceId?: number; // 新增sliceId参数
  labelType?: number; // 新增labelType参数
  total?: number;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger: boolean;
    showQuickJumper: boolean;
    showTotal: (total: number, range: [number, number]) => string;
  };
  onPageChange?: (page: number, pageSize: number) => void;
}

const Shelf: React.FC<ShelfProps> = ({
  dataList,
  onSelectChange,
  selectedRowKeys: propSelectedRowKeys = [],
  mmuItemId,
  mmuItemIdList = [],
  merchantItemIdList = [],
  candidateRecordResults = [],
  sliceId,
  labelType,
  total = 0,
  pagination,
  onPageChange,
}) => {
  const [showSerial, setShowSerial] = useState(false);
  const [sortedDataList, setSortedDataList] = useState(dataList);

  // 当dataList、mmuItemIdList或merchantItemIdList变化时，进行排序
  useEffect(() => {
    if (
      (mmuItemIdList && mmuItemIdList.length > 0) ||
      (merchantItemIdList && merchantItemIdList.length > 0)
    ) {
      if (dataList.length > 0) {
        let sorted = [...dataList];

        // 第一次排序：将mmuItemIdList中的商品置顶（在merchantItemIdList之后）
        if (mmuItemIdList && mmuItemIdList.length > 0) {
          sorted = sorted.sort((a, b) => {
            // 添加空值检查
            if (!a || !b) return 0;
            // 如果 a 的 itemId 存在于 mmuItemIdList 中，则 a 排在前面
            if (mmuItemIdList.includes(a.itemId)) return -1;
            // 如果 b 的 itemId 存在于 mmuItemIdList 中，则 b 排在前面
            if (mmuItemIdList.includes(b.itemId)) return 1;
            // 其他情况保持原有顺序
            return 0;
          });
        }

        // 第二次排序：将merchantItemIdList中的商品置顶
        if (merchantItemIdList && merchantItemIdList.length > 0) {
          sorted = sorted.sort((a, b) => {
            // 添加空值检查
            if (!a || !b) return 0;
            // 如果 a 的 itemId 存在于 merchantItemIdList 中，则 a 排在前面
            if (merchantItemIdList.includes(a.itemId)) return -1;
            // 如果 b 的 itemId 存在于 merchantItemIdList 中，则 b 排在前面
            if (merchantItemIdList.includes(b.itemId)) return 1;
            // 其他情况保持原有顺序
            return 0;
          });
        }

        setSortedDataList(sorted);
      }
    } else {
      setSortedDataList(dataList);
    }
  }, [dataList, mmuItemIdList, merchantItemIdList]);

  const rowSelection = {
    selectedRowKeys: propSelectedRowKeys,
    onChange: (keys: React.Key[]) => {
      onSelectChange(keys as string[]);
    },
  };

  // 渲染"商品图片"列
  const renderImageUrlsCell = (urls: string[]) => {
    const maxVisible = 3;
    const visible = urls.slice(0, maxVisible);
    const hidden = urls.slice(maxVisible);

    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        {visible.map((url, idx) => (
          <Image
            key={idx}
            width={150}
            height={150}
            src={url}
            style={{ marginRight: hidden.length > 0 ? 4 : 0 }}
            loading="lazy"
          />
        ))}
        {hidden.length > 0 && (
          <Tooltip
            title={
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                {hidden.map((url, idx) => (
                  <Image key={idx} width={150} height={150} src={url} />
                ))}
              </div>
            }
          >
            <span style={{ cursor: 'pointer', color: '#888', marginLeft: 4 }}>
              +{hidden.length}
            </span>
          </Tooltip>
        )}
      </div>
    );
  };

  const columns = useMemo(() => {
    const base = [
      {
        title: '商品ID',
        dataIndex: 'itemId',
        key: 'itemId',
        width: 200,
        render: (itemId: number) => (
          <a href={`${ITEM_LINK_PREFIX}${itemId}`} target="_blank" rel="noopener noreferrer">
            {itemId}
          </a>
        ),
      },
      {
        title: '商品标题',
        dataIndex: 'title',
        key: 'title',
        width: 350,
        render: (text: string, record: ProductItem) => (
          <div
            style={{
              whiteSpace: 'normal',
              wordBreak: 'break-word',
            }}
          >
            {candidateRecordResults.includes(record.itemId) && (
              <Tag color="green" style={{ fontSize: '12px' }}>
                讲解中
              </Tag>
            )}
            {text}
            {merchantItemIdList.includes(record.itemId) && (
              <Tag color="blue" style={{ fontSize: '12px' }}>
                新模型商品
              </Tag>
            )}
            {mmuItemIdList.includes(record.itemId) && (
              <Tag color="orange" style={{ fontSize: '12px' }}>
                MMU商品
              </Tag>
            )}
          </div>
        ),
      },
      {
        title: '价格',
        dataIndex: 'itemPrice',
        key: 'itemPrice',
        width: 170,
        render: (price: string) => {
          if (!price) return <span></span>;
          return <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>¥{price}</span>;
        },
      },

      {
        title: '商品图片',
        dataIndex: 'imageUrls',
        key: 'imageUrls',
        width: 600,
        render: renderImageUrlsCell,
      },
    ];

    if (showSerial) {
      return [...base];
    }

    return base;
  }, [showSerial, mmuItemId]);

  return (
    <div>
      {/*商品信息表格*/}
      <ConfigProvider locale={zhCN}>
        <Table<ProductItem>
          rowSelection={rowSelection}
          columns={columns}
          dataSource={sortedDataList}
          rowKey="itemId"
          pagination={
            pagination
              ? {
                  ...pagination,
                  onChange: onPageChange,
                }
              : {
                  pageSize: 50,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `共 ${total} 条，当前显示第 ${range?.[0]}-${range?.[1]} 条`,
                }
          }
          size="middle"
        />
      </ConfigProvider>
    </div>
  );
};

export default Shelf;
