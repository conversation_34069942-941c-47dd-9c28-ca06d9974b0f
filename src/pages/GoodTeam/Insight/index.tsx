import React, {useEffect, useState} from 'react';
import {Col, Layout, message, Row} from 'antd';
import type {
  HeadCountStatsData,
  Params,
  PdStatsData,
  PieChartDataItem,
  StatsData,
  TeamCategoryRow,
  TeamDetailRow,
  TimeRange,
} from '@/interfaces/interfaces';
import moment from 'moment';
import {FilterBar} from '@/components/FilterBar';
import * as API from '@/common/API/TeamInsightAPI';
import {CompletedDemandStatCard} from "@/components/Cards/CompletedDemandStatCard";
import {StatCard} from "@/components/Cards/StatCard";
import {HeadCountStatCard} from "@/components/Cards/HeadCountStatCard";
import {PdStatCard} from "@/components/Cards/PdStatCard";
import {PieChartSection} from "@/components/PieChartSection";
import {TeamCategoriesTable} from "@/components/TeamCategoriesTable";
import {TeamDetailsTable} from "@/components/TeamDetailsTable";
import dayjs from "dayjs";

const { Content } = Layout;

const TeamInsight = () => {
  /** 顶部 FilterBar 的初始状态 */

  const savedGroup = localStorage.getItem('teamGroup') ?? 'live';

  const today = moment();
  const currentStart = today.clone().startOf('week'); // 当前周的周一
  const currentEnd = today.clone().endOf('week');   // 当前周的周日

  const defaultCurrentParams: Params = {
    scheduleListVO: {
      startTime: currentStart.format('YYYY-MM-DD'),
      endTime: currentEnd.format('YYYY-MM-DD'),
      teamGroup: savedGroup,
      strict: true,
    },
    historyPeriod: 4,
    intervalType:'week'
  };

  const [currentFilter, setCurrentFilter] = useState<Params>(defaultCurrentParams);

  // ——各块 state
  const [teamCategories, setTeamCategories] = useState<TeamCategoryRow[]>([]);
  const [teamDetails, setTeamDetails] = useState<TeamDetailRow[]>([]);
  const [teamStats, setTeamStats] = useState<StatsData[]>([]);
  const [todoDemandStat, setTodoDemandStat] = useState<StatsData | null>(null);
  const [inProgressDemandStat, setInProgressDemandStat] = useState<StatsData | null>(null);
  const [completedDemandStat, setCompletedDemandStat] = useState<StatsData | null>(null);
  const [pdStat, setPdStat] = useState<PdStatsData | null>(null);
  const [headCountStat, setHeadCountStat] = useState<HeadCountStatsData | null>(null);
  const [demandData, setDemandData] = useState<PieChartDataItem[]>([]);
  const [pdData, setPdData] = useState<PieChartDataItem[]>([]);
  const [priorityData, setPriorityData] = useState<PieChartDataItem[]>([]);
  const [historyPeriods, setHistoryPeriods] = useState<TimeRange[]>([]);
  const [historyLabels, setHistoryLabels] = useState<string[]>([]);

  // 读取 URL 上的 noTechTeam 参数
  const getQueryParam = (paramName: string) => {
    const url = window.location.href;
    const urlParams = new URLSearchParams(new URL(url).search);
    return urlParams.get(paramName);
  }
  const noTechTeam = getQueryParam('noTechTeam') ?? false;

  // 格式化周期标签
  type IntervalType = 'week' | 'month' | 'year';
  const formatHistoryPeriods = (periods: TimeRange[], interval: IntervalType) =>
      periods.map(({ startDate }) => {
        const d = dayjs(startDate, 'YYYY-MM-DD');
        if (interval === 'month') return d.format('YYYY-MM');
        if (interval === 'year') return d.format('YYYY');
        return startDate;
      });

  // teamGroup 变化时，写回 localStorage
  useEffect(() => {
    localStorage.setItem('teamGroup', currentFilter.scheduleListVO.teamGroup);
  }, [currentFilter.scheduleListVO.teamGroup]);

  // 数据加载
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        console.log('获取团队洞察数据开始：');
        const data = await API.insightList({
          ...currentFilter,
          noTechTeam: noTechTeam === 'true',
        });
        // 解构并改名
        const {
          statistics: statisticsPayload,
          categories: categoriesPayload,
          details: detailsPayload,
          historyPeriods: fetchedHistoryPeriods,
        } = data;

        // 1. 团队统计卡片
        setTeamStats(
            statisticsPayload.teamStats.map((stat) => ({
              name: stat.name,
              value: stat.value,
              history: stat.history,
            }))
        );
        setTodoDemandStat(
            statisticsPayload.teamStats.find((s) => s.name === '待开发需求数') || null
        );
        setInProgressDemandStat(
            statisticsPayload.teamStats.find((s) => s.name === '进行中需求数') || null
        );
        setCompletedDemandStat(
            statisticsPayload.teamStats.find((s) => s.name === '完成需求数') || null
        );
        setPdStat(statisticsPayload.pdStats);
        setHeadCountStat(statisticsPayload.headCountStats);

        // 2. 团队分类与明细
        setTeamCategories(categoriesPayload);
        setTeamDetails(detailsPayload);

        // 3. 历史周期 & 标签
        setHistoryPeriods(fetchedHistoryPeriods);
        setHistoryLabels(
            formatHistoryPeriods(
                fetchedHistoryPeriods,
                currentFilter.intervalType as IntervalType
            )
        );

        // 4. 饼图三维度数据
        setDemandData(
            categoriesPayload.map(({ category, totalQuantity }) => ({
              name: category,
              value: totalQuantity,
            }))
        );
        setPdData(
            categoriesPayload.map(({ category, totalPdCount }) => ({
              name: category,
              value: totalPdCount,
            }))
        );
        setPriorityData(
            Object.values(
                detailsPayload.reduce((acc, { priority }) => {
                  const key = priority?.trim() ? priority : '未知';
                  acc[key] ??= { name: key, value: 0 };
                  acc[key].value += 1;
                  return acc;
                }, {} as Record<string, PieChartDataItem>)
            )
        );
        console.log('获取团队洞察数据结束：', data);
      } catch (err) {
        console.error('获取团队洞察数据失败：', err);
        message.error('获取数据失败，请稍后重试');
      }
    };
    // 6. 立即调用
    fetchAllData();
  }, [currentFilter, noTechTeam]);

  return (
    <Layout style={{ padding: 24, background: '#fff' }}>
      <Content>
        {/* 顶部筛选 */}
        <FilterBar filter={currentFilter} onChange={setCurrentFilter} />

        {/* 指标卡片区域 */}
        <div
            style={{
              display: 'grid',
              gridTemplateColumns: '12% 12% 1fr 1fr 1fr',
              gridAutoRows: '200px',
              gap: '16px',
              width: '100%',
              alignItems: 'stretch',

            }}
        >
          <StatCard data={inProgressDemandStat} teamDetails={teamDetails} />
          <StatCard data={todoDemandStat} teamDetails={teamDetails}/>
          <CompletedDemandStatCard data={completedDemandStat} teamDetails={teamDetails} historyLabels={historyLabels}/>
          <PdStatCard data={pdStat} historyLabels={historyLabels}/>
          <HeadCountStatCard data={headCountStat} historyLabels={historyLabels}/>
        </div>

         {/*下方两列：左 PieChart，右 DataTable*/}
        <Row gutter={16} style={{ marginTop: 24 }}>
          <Col xs={24} xl={10} >
            <PieChartSection
                title={'需求类型占比'}
                demandData={demandData}
                pdData={pdData}
                priorityData={priorityData}
            />
          </Col>
          <Col xs={24} xl={14}>
            <TeamCategoriesTable data={teamCategories} teamDetails={teamDetails}/>
          </Col>
        </Row>
        {/* 需求明细表格 */}
        <TeamDetailsTable data={teamDetails} />
      </Content>
    </Layout>
  );
};

export default TeamInsight;
