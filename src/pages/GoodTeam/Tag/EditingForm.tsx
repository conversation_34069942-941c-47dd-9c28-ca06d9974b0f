import React, {useEffect, useState} from 'react';
import {Form, Input, message, Modal, Select} from 'antd';
import * as API from "@/common/API/TeamTagAPI";
import type {TagInfoItem} from "@/pages/GoodTeam/Tag/index";

interface EditingFormProps {
    visible: boolean;
    onCancel: () => void;
    onSuccess: () => void;
    initialValues?: Partial<TagInfoItem>;
}

const EditingForm: React.FC<EditingFormProps> = ({ visible, onCancel, onSuccess, initialValues   }) => {
    const [form] = Form.useForm();
    const [submitting, setSubmitting] = useState(false);


    useEffect(() => {
        if (visible) {
            if (initialValues) {
                // —— 编辑：加载已有值
                form.setFieldsValue(initialValues);
            } else {
                // —— 新增：彻底清空表单
                form.resetFields();
            }
        }
    }, [visible, initialValues]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            setSubmitting(true);
            const payload = {
                ...values,
            };
            const response = initialValues?.id
                ? await API.tagInfoUpdate({ id: initialValues.id, ...payload })
                : await API.tagInfoAdd(payload);

            if (response.result === 1) {
                // 成功
                message.success(initialValues?.id ? '编辑成功' : '新增成功');
                form.resetFields();
                onSuccess();
            } else {
                // 失败
                console.error(response.error_msg);
                message.error(response.data || (initialValues?.id ? '编辑失败' : '新增失败'));
            }
        } catch (error) {
            console.error(error);
            message.error(initialValues?.id ? '编辑失败' : '新增失败');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Modal
            title={initialValues?.id ? '编辑标签信息' : '新增标签信息'}
            visible={visible}
            onCancel={onCancel}
            onOk={handleOk}
            confirmLoading={submitting}
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    name="tagName"
                    label="标签名称"
                    rules={[{ required: true, message: '请输入标签名称' }]}
                >
                    <Input />
                </Form.Item>


                <Form.Item
                    name="category"
                    label="标签类别"
                >
                    <Input />
                </Form.Item>

                <Form.Item name="tagAlias" label="标签别名">
                    <Input />
                </Form.Item>


                <Form.Item
                    name="extraInfo"
                    label="扩展字段"
                >
                    <Input.TextArea rows={3} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default EditingForm;
