import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON>Complete, Button, Form, Input, Layout, message, Popconfirm, Space, Table} from 'antd';
import type {ColumnsType} from 'antd/es/table';
import {Content} from "antd/es/layout/layout";
import * as API from "@/common/API/TeamTagAPI";
import EditingForm from "@/pages/GoodTeam/Tag/EditingForm";

// 定义表格数据类型
export interface TagInfoItem {
    id: number;
    tagName: string;
    category: string;
    tagAlias: string;
    createTime: string;
    updateTime: string;
    extraInfo: string;
    operator: string;
}

const TeamTag: React.FC = () => {
    const [form] = Form.useForm();
    const [modalVisible, setModalVisible] = useState(false);

    const [allTagInfoList, setAllTagInfoList] = useState<TagInfoItem[]>([]);
    const [tagInfoList, setTagInfoList] = useState<TagInfoItem[]>([]);
    const [editingRecord, setEditingRecord] = useState<TagInfoItem | null>(null);

    const [searchHistory, setSearchHistory] = useState<string[]>(() => {
        const stored = localStorage.getItem('tagSearchHistory');
        return stored ? JSON.parse(stored) : [];
    });


    // 表格列定义
    const columns: ColumnsType<TagInfoItem> = [
        {
            title: 'id',
            dataIndex: 'id',
            key: 'id',
        },
        { title: '标签名称', dataIndex: 'tagName', key: 'tagName',
            width: 150, },
        {
            title: '标签类别',
            dataIndex: 'category',
            key: 'category' ,
            width: 150,
        },
        { title: '标签别名', dataIndex: 'tagAlias', key: 'tagAlias' },
        { title: '扩展字段', dataIndex: 'extraInfo', key: 'extraInfo' },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime' ,
            sorter: (a, b) => Number(a.createTime) - Number(b.createTime),  // 修改：添加排序
            sortDirections: ['ascend', 'descend'],
            render: ts => new Date(Number(ts)).toLocaleString()
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            key: 'updateTime' ,
            sorter: (a, b) => Number(a.updateTime) - Number(b.updateTime),  // 修改：添加排序
            sortDirections: ['ascend', 'descend'],
            render: ts => new Date(Number(ts)).toLocaleString(),
        },
        { title: '操作人', dataIndex: 'operator', key: 'operator' },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <Space size="middle">
                    <a onClick={() => handleEdit(record)}>编辑</a>
                    <Popconfirm
                        title="确定删除这条记录吗？"
                        onConfirm={() => handleDelete(record.id)}
                        okText="是"
                        cancelText="否"
                    >
                        <a style={{ color: 'red' }}>删除</a>
                    </Popconfirm>
                </Space>
            ),
        },
    ];


    const fetchData = useCallback(async (params: any = {}) => {
        try {
            const info = await API.tagInfoList(params);
            // 解析后端传过来的 childrenIdList（字符串）为数组
            const parsedList: TagInfoItem[] = info.tagInfoList?.map?.((item: any) => ({
                ...item,
            }));
            setAllTagInfoList(parsedList);
            setTagInfoList(parsedList);
        } catch {
            message.error('获取标签列表失败');
        }
    },[]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    useEffect(() => {
        localStorage.setItem('tagSearchHistory', JSON.stringify(searchHistory));
    }, [searchHistory]);


    const handleNew = () => {
        setEditingRecord(null);
        setModalVisible(true);
    };

    const handleEdit = (record: TagInfoItem) => {
        setEditingRecord(record);
        setModalVisible(true);
    };

    const handleDelete = async (id: number) => {
        try {
            await API.tagInfoDel({ id });
            message.success('删除成功');
            fetchData();
        } catch {
            message.error('删除失败');
        }
    };

    const handleCancel = () => {
        setModalVisible(false);
    };

    const handleSuccess = () => {
        setModalVisible(false);
        fetchData();
    };

    // 查询数据
    const handleSearch = (value: string) => {
        const v = value ?? '';
        const keyword = v?.trim()?.toLowerCase?.();
        if (keyword) {
            // 更新历史：去重、截断到 10 条
            setSearchHistory(prev => {
                const next = [keyword, ...prev.filter(item => item !== keyword)];
                return next.slice(0, 10);
            });
            // 过滤逻辑
            setTagInfoList(allTagInfoList.filter(item => {
                const name     = (item.tagName   ?? '').toLowerCase();
                const category = (item.category  ?? '').toLowerCase();
                const alias    = (item.tagAlias  ?? '').toLowerCase();
                const operator    = (item.operator  ?? '').toLowerCase();

                return (
                    name.includes(keyword) ||
                    category.includes(keyword) ||
                    alias.includes(keyword) ||
                    operator.includes(keyword)
                );
            }));
        } else {
            setTagInfoList(allTagInfoList);
        }
    };


    // 重置表单
    const handleReset = () => {
        form.resetFields();
    };



    return (
        <Layout style={{padding: 24, background: '#fff'}}>
            <Content>
                <Form
                    form={form}
                    layout="inline"
                    style={{ display: 'flex', justifyContent: 'center', marginBottom: 24 }} // 修改: 居中对齐
                >
                    {/* 搜索栏 */}
                    <Form.Item name="search" style={{ width: 500, marginRight: 16 }}>
                        <AutoComplete
                            options={searchHistory.map(h => ({ value: h }))}
                            onSelect={handleSearch}
                        >
                            <Input.Search
                                placeholder="请输入搜索内容: 标签名称/标签别名/标签类别/操作人"
                                allowClear
                                onSearch={handleSearch}
                                onChange={e=>{
                                    const v = e.target.value;
                                    if(v==''){
                                        handleSearch('');
                                    }
                                }}
                            />
                        </AutoComplete>
                    </Form.Item>
                    {/* 新增按钮 */}
                    <Form.Item>
                        <Button type="primary" onClick={handleNew}>
                            新增标签
                        </Button>
                    </Form.Item>
                </Form>


            <Table<TagInfoItem>
                columns={columns}
                dataSource={tagInfoList}
                rowKey="id"
                pagination={{
                    pageSize: 30,
                    showSizeChanger: false, // 不允许用户修改
                    showQuickJumper: true,  // 可选：支持跳页
                    showTotal: (total, range) => `共 ${total} 条，当前显示第 ${range?.[0]}-${range?.[1]} 条`,
                }}
                locale={{ emptyText: '暂无数据' }}
            />
            <EditingForm
                visible={modalVisible}
                onCancel={handleCancel}
                onSuccess={handleSuccess}
                initialValues={editingRecord || undefined}
            />
            </Content>
        </Layout>
    );
};

export default TeamTag;
