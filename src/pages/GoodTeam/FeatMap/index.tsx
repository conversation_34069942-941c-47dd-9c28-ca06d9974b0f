import React, { useEffect, useState } from 'react';
import { Button, Form, Select, Tooltip } from 'antd';
import imgSrc from './img/img.png';
import * as API from '@/common/API/FeatMapAPI';
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import { FeatDetailTable } from '@/components/Tables/FeatMap/FeatDetailTable';
import type { FeatMapItem, TableRow } from '@/common/API/types';

// 扩展 dayjs
dayjs.extend(isoWeek);

// 定义每个筛选器的选项数据
const FILTER_OPTIONS_DATA = {
  productType: [
    { label: '自建', value: '自建' },
    { label: '闪电购', value: '闪电购' },
    { label: '本地生活', value: '本地生活' },
    { label: '房产', value: '房产' },
    { label: '拍卖', value: '拍卖' },
    { label: '快手课堂', value: '快手课堂' },
    { label: '跨境商品', value: '跨境商品' },
  ],
  activityType: [
    { label: '秒杀', value: '秒杀' },
    { label: '福利购', value: '福利购' },
    { label: '万人团', value: '万人团' },
    { label: '多人团', value: '多人团' },
    { label: '大牌大补', value: '大牌大补' },
    { label: '凑单优惠', value: '凑单优惠' },
    { label: '天降券包', value: '天降券包' },
    { label: '券优惠', value: '券优惠' },
    { label: '满减满折', value: '满减满折' },
    { label: '大场满减', value: '大场满减' },
    { label: '大场满返', value: '大场满返' },
    { label: '单单返', value: '单单返' },
    { label: '下单有礼', value: '下单有礼' },
    { label: '大促预售', value: '大促预售' },
    { label: '现货开抢', value: '现货开抢' },
    { label: '低价好物', value: '低价好物' },
    { label: '新品预约', value: '新品预约' },
    { label: '抽奖', value: '抽奖' },
    { label: '待开价', value: '待开价' },
    { label: '0元试穿', value: '0元试穿' },
    { label: '首选之子', value: '首选之子' },
    { label: '省钱月卡', value: '省钱月卡' },
  ],
  productAttr: [
    { label: '品牌', value: '品牌' },
    { label: '排行榜', value: '排行榜' },
    { label: '直播热卖', value: '直播热卖' },
    { label: '库存', value: '库存' },
    { label: '商品复购率', value: '商品复购率' },
    { label: '评价', value: '评价' },
    { label: '价格力', value: '价格力' },
    { label: '商品属性卖点', value: '商品属性卖点' },
    { label: '真抢光', value: '真抢光' },
    { label: '假抢光', value: '假抢光' },
  ],
  userType: [
    { label: '卖家', value: '卖家' },
    { label: '快手旗舰', value: '快手旗舰' },
    { label: '主播(劣质/剧本)', value: '主播(劣质/剧本)' },
    { label: '大V', value: '大V' },
    { label: '新人', value: '新人' },
    { label: '高价值会员', value: '高价值会员' },
    { label: '黑卡/黑钻', value: '黑卡/黑钻' },
    { label: '店铺会员', value: '店铺会员' },
    { label: '粉丝', value: '粉丝' },
    { label: '本场热卖', value: '本场热卖' },
  ],
  userRight: [
    { label: '支付权益', value: '支付权益' },
    { label: '物流权益', value: '物流权益' },
    { label: '售后权益', value: '售后权益' },
  ],
  channel: [
    { label: '直播间', value: '直播间' },
    { label: '简易直播间', value: '简易直播间' },
    { label: '短视频', value: '短视频' },
    { label: '搜索', value: '搜索' },
    { label: '买首', value: '买首' },
    { label: '商城', value: '商城' },
  ],
};

// 表格列名配置
const filterOptions = [
  { label: '商品类型', key: 'productType' },
  { label: '活动类型', key: 'activityType' },
  { label: '商品属性', key: 'productAttr' },
  { label: '用户类型', key: 'userType' },
  { label: '用户权益', key: 'userRight' },
  { label: '渠道', key: 'channel' },
];

/* ------------ 工具：根据 labelPos 生成样式 ------------ */
const LABEL_TEXT_STYLE: React.CSSProperties = {
  fontSize: 14,
  color: '#0000FF',
};

const buildLabelStyle = (pos?: LabelPos): React.CSSProperties => {
  const p = { x: 'left', y: 'bottom', offsetX: 4, offsetY: 4, ...(pos || {}) };
  const style: React.CSSProperties = {
    ...LABEL_TEXT_STYLE,
    position: 'absolute',
    padding: '2px 4px',
    background: 'rgba(255,255,255,0.6)',
    borderRadius: 2,
    lineHeight: 1.2,
    pointerEvents: 'none',
    transform: '',
  };

  // 水平方向
  if (p.x === 'left') style.left = p.offsetX;
  else if (p.x === 'right') style.right = p.offsetX;
  else {
    style.left = '50%';
    style.transform += ' translateX(-50%)';
  }

  // 垂直方向
  if (p.y === 'top') style.top = p.offsetY;
  else if (p.y === 'bottom') style.bottom = p.offsetY;
  else {
    style.top = '50%';
    style.transform += ' translateY(-50%)';
  }

  return style;
};

/** 热点区域结构：rect = [left, top, width, height]，单位 px（因为容器宽高固定） */
/* ------------ 热点定义 ------------ */
type LabelPos = {
  x: 'left' | 'center' | 'right';
  y: 'top' | 'center' | 'bottom';
  offsetX?: number;
  offsetY?: number;
};

// 热点数据：楼层，买点...
type Hotspot = {
  id: number;
  label: string;
  rectPct: [number, number, number, number];
  color?: string; // 蒙层色
  labelPos?: LabelPos; // label 位置
  filter: Partial<TableRow>; // 点击后筛选条件
};

const HOTSPOTS: Hotspot[] = [
  {
    id: 30,
    label: '营销楼层',
    rectPct: [0.01, 0.31, 0.98, 0.1],
    filter: { position: '营销楼层' },
    color: 'rgba(255,77,79,0.18)',
    labelPos: { x: 'left', y: 'bottom', offsetX: 5, offsetY: 7 },
  },
  {
    id: 31,
    label: '卖点',
    rectPct: [0.36, 0.485, 0.58, 0.06],
    filter: { position: '卖点' },
    color: 'rgba(255,77,79,0.18)',
    labelPos: { x: 'right', y: 'top', offsetX: 5, offsetY: 7 },
  },
  {
    id: 32,
    label: '序号',
    rectPct: [0.02, 0.63, 0.35, 0.03],
    filter: { position: '序号' },
    color: 'rgba(255,77,79,0.18)',
    labelPos: { x: 'right', y: 'top', offsetX: 0, offsetY: 0 },
  },
  {
    id: 33,
    label: '价格',
    rectPct: [0.36, 0.72, 0.32, 0.07],
    filter: { position: '价格' },
    color: 'rgba(255,77,79,0.18)',
    labelPos: { x: 'right', y: 'top', offsetX: 1, offsetY: 3 },
  },
  {
    id: 34,
    label: '行动按钮',
    rectPct: [0.66, 0.9, 0.32, 0.07],
    filter: { position: '行动按钮' },
    color: 'rgba(255,77,79,0.18)',
    labelPos: { x: 'left', y: 'bottom', offsetX: 1, offsetY: 3 },
  },
];

const FeatMap: React.FC = () => {
  const [tableDataSource, setTableDataSource] = useState<FeatMapItem[]>([]);
  const [activeHotspot, setActiveHotspot] = useState<Hotspot | null>(null);
  const [selectedBizMap, setSelectedBizMap] = useState<Record<string, string | undefined>>({});

  // 根据类型，得到对应的筛选项
  const getFilterOptions = (key: string) => {
    return FILTER_OPTIONS_DATA[key as keyof typeof FILTER_OPTIONS_DATA] || [];
  };

  // 初始数据加载
  const fetchInitialData = async () => {
    try {
      const startOfMonth = dayjs()?.startOf?.('month')?.format?.('YYYY-MM-DD');
      const endOfMonth = dayjs()?.endOf?.('month')?.format?.('YYYY-MM-DD');
      const params = { startTime: startOfMonth, endTime: endOfMonth };
      const result = await API.ListInfo(params);
      setTableDataSource(result);
    } catch (error) {
      console.error('初始数据加载失败:', error);
    }
  };

  const clearHotspot = () => setActiveHotspot(null);

  const clearAll = () => {
    setActiveHotspot(null);
    setSelectedBizMap({});
    // 清空后重新加载初始数据
    fetchInitialData();
  };

  // 下拉框选择处理
  const handleBizChange = (key: string, value?: string) => {
    setSelectedBizMap((prev) => ({ ...prev, [key]: value }));
  };

  // 构建筛选参数
  const buildFilterParams = () => {
    const params: Record<string, string> = {};
    Object.entries(selectedBizMap).forEach(([key, value]) => {
      if (value) {
        params[key] = value;
      }
    });
    return params;
  };

  // 获取表格数据（不使用热点ID）
  const getTableData = async (additionalFilters?: Record<string, string>) => {
    try {
      const params = {
        // 合并用户筛选条件和热点筛选条件
        ...buildFilterParams(),
        ...additionalFilters,
      };
      console.log('API请求参数:', params);
      const result = await API.ListInfoByTag(params);
      setTableDataSource(result);
    } catch (error) {
      console.error('数据加载失败:', error);
    }
  };

  // 热点点击处理
  const handleHotspotClick = (hotspot: Hotspot) => {
    setActiveHotspot(hotspot);

    // 构建热点特定的筛选条件
    const hotspotFilters: Record<string, string> = {};
    Object.entries(hotspot.filter).forEach(([key, value]) => {
      if (value) {
        hotspotFilters[key] = value.toString();
      }
    });

    // 调用API，只传递筛选条件
    getTableData(hotspotFilters);
  };

  // 当筛选条件变化时，如果当前有激活的热点，重新加载数据
  useEffect(() => {
    if (activeHotspot) {
      // 重新构建热点筛选条件
      const hotspotFilters: Record<string, string> = {};
      Object.entries(activeHotspot.filter).forEach(([key, value]) => {
        if (value) {
          hotspotFilters[key] = value.toString();
        }
      });

      // 合并热点筛选条件和用户筛选条件
      const params = {
        ...buildFilterParams(),
        ...hotspotFilters,
      };

      // 直接调用 API
      const fetchData = async () => {
        try {
          console.log('API请求参数:', params);
          const result = await API.ListInfoByTag(params);
          setTableDataSource(result);
        } catch (error) {
          console.error('数据加载失败:', error);
        }
      };

      fetchData();
    }
  }, [activeHotspot, selectedBizMap]); // 直接依赖 selectedBizMap

  // 组件挂载时加载初始数据
  useEffect(() => {
    fetchInitialData();
  }, []);

  return (
    <div style={{ padding: 24, background: '#f7f8fa', minHeight: '100vh' }}>
      {/* 顶部筛选栏 */}
      <Form layout="inline" style={{ marginBottom: 20 }}>
        {filterOptions.map((opt) => (
          <Form.Item label={opt.label} key={opt.key}>
            <Select
              allowClear
              style={{ width: 160 }}
              placeholder={`请选择${opt.label}`}
              value={selectedBizMap[opt.key]}
              onChange={(val) => handleBizChange(opt.key, val)}
              options={getFilterOptions(opt.key)}
            />
          </Form.Item>
        ))}

        {activeHotspot && (
          <Form.Item>
            <Button onClick={clearHotspot}>清空热点</Button>
          </Form.Item>
        )}
        <Form.Item>
          <Button onClick={clearAll}>重置全部</Button>
        </Form.Item>
      </Form>

      {/* 显示当前筛选条件 */}
      {Object.keys(selectedBizMap).some((key) => selectedBizMap[key]) && (
        <div style={{ marginBottom: 16, padding: 8, background: '#e6f7ff', borderRadius: 6 }}>
          <strong>当前筛选条件：</strong>
          {filterOptions
            .map((opt) => {
              const value = selectedBizMap[opt.key];
              return value ? (
                <span key={opt.key} style={{ margin: '0 8px' }}>
                  {opt.label}: {value}
                </span>
              ) : null;
            })
            .filter(Boolean)}
        </div>
      )}

      {/* 主体：左图右表 */}
      <div style={{ display: 'flex', alignItems: 'flex-start' }}>
        {/* 左图容器 */}
        <div
          style={{
            width: 350,
            height: 750,
            background: '#fff',
            borderRadius: 10,
            boxShadow: '0 2px 8px #eee',
            overflow: 'hidden',
            marginRight: 32,
            flexShrink: 0,
            position: 'relative',
          }}
        >
          <img
            src={imgSrc}
            alt="左侧图片"
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />

          {/* 热点区域叠加 */}
          {HOTSPOTS.map((hs) => {
            const [l, t, w, h] = hs.rectPct;
            const isActive = activeHotspot?.id === hs.id;
            return (
              <Tooltip key={hs.id} title={hs.label}>
                <div
                  style={{
                    position: 'absolute',
                    left: `${l * 100}%`,
                    top: `${t * 100}%`,
                    width: `${w * 100}%`,
                    height: `${h * 100}%`,
                    background: hs.color || 'rgba(255,255,255,0.45)',
                    border: isActive ? '2px solid #1890ff' : '1px solid rgba(0,0,0,0.08)',
                    cursor: 'pointer',
                    boxSizing: 'border-box',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#000',
                    fontSize: 12,
                    fontWeight: 500,
                    userSelect: 'none',
                  }}
                  onClick={() => handleHotspotClick(hs)}
                >
                  <span style={buildLabelStyle(hs.labelPos)}>{hs.label}</span>
                </div>
              </Tooltip>
            );
          })}
        </div>

        {/* 右表 */}
        <div style={{ flex: 1 }}>
          <FeatDetailTable data={tableDataSource} />
        </div>
      </div>
    </div>
  );
};

export default FeatMap;
