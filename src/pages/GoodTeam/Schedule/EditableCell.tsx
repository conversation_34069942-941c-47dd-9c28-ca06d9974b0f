// @ts-nocheck
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Form, Input, Select} from "antd";

const {Option} = Select;

const EditableCell = ({
                          editing,
                          dataIndex,
                          title,
                          inputType,
                          record,
                          index,
                          children,
                          onEditChange,
                          options, // 接收传入的选项数据
                          ...restProps
                      }) => {


    const inputNode = inputType === 'select' ? (
        <Select
            size="small"
            // className={restProps.className}
            defaultValue={record[dataIndex]}
            onChange={(value) => {
                record[dataIndex] = value;
                onEditChange(value,record);
            }}
        >
            {options.map((option) => (
                <Option key={option.value} value={option.value} >
                    {option.label}
                </Option>
            ))}
        </Select>
    ) : (
        <Input/>
    );

    return (
        <td {...restProps} >
            {editing ? (
                <Form.Item
                    name={dataIndex}
                    style={{margin: 0}}
                    rules={[
                        {
                            required: true,
                            message: `请输入 ${title}!`,
                        },
                    ]}
                >
                    {inputNode}
                </Form.Item>
            ) : (
                children
            )}
        </td>
    );
};

export default EditableCell;