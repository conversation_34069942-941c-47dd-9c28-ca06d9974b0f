// @ts-nocheck
// 图例组件
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Tag} from "antd";

const Legend = (props) => {
    return (
        <div style={{...props.style, marginBottom: 16}}>
            {/*<span style={{fontWeight: 'bold'}}>图例：</span>*/}
            <span style={{ fontSize: "12px"}}>图例：</span>
            {Object.entries(props?.statusColorMap).map(([status, color]) => (
                <span key={status} style={{marginLeft: 10}}>
                    <Tag color={color} style={{color: 'black'}}> {status}</Tag>
        </span>
            ))}
        </div>
    );
};

export default Legend;