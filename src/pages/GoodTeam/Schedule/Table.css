.ant-table-cell {
    white-space: nowrap; /* 防止换行 */
}

.ant-table-body {
    overflow-x: auto !important; /* 支持横向滚动 */
}

th.schedule-status, td.schedule-status {
    padding-left: 3px !important;
    padding-right: 3px !important;
}

.schedule-status .ant-select-selector {
    padding: 0 3px;
    border-color: hsla(0,0%,100%,0) !important;;
}

.editable-row .ant-table-cell {
    padding-left: 4px ;
    padding-right: 4px ;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.dateColumn.ant-table-cell {
    padding-left: 4px !important;
    padding-right: 4px !important;
}

.schedule-status .ant-select-selection-item {
    padding-inline-end: 10px !important;
}

.schedule-status .ant-select-arrow {
    inset-inline-start: 51px;
}

.ant-select-item.ant-select-item-option {
    padding: 5px 3px !important;
}

.schedule-status .ant-select-selection-item {
    color: blue
}