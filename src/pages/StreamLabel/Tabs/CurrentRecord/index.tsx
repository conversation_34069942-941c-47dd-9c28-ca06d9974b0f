import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Button, ConfigProvider, Dropdown, Image, Input, Menu, message, Modal, Slider, Table, Tooltip} from 'antd';
import {BackwardOutlined, ForwardOutlined, InfoCircleOutlined, SearchOutlined} from '@ant-design/icons';

import type {HistoryItem} from "@/pages/StreamLabel/Tabs/HistoryRecord";
import {PauseCircleOutlined, PlayCircleOutlined} from "@m-ui/icons";
import * as API from '@/common/API/StreamLabelAPI';
import {ITEM_LINK_PREFIX} from "@/pages/StreamLabel/constant";
import zhCN from 'antd/es/locale/zh_CN';

export interface ProductItem {
    serialNumber:number;
    itemId: number;
    title: string;
    imageUrl: string;
    imageUrls: string[];
    itemCategoryPropInfo: Record<string, string | undefined>;
    anchorRecordStartTime: number | null;
    anchorRecordEndTime: number | null;
}

interface CurrentRecordProps {
    dataList: ProductItem[];
    dataHistory: HistoryItem[];
    liveStreamId?: number;
    onSelectChange: (selectedKeys: string[]) => void;
    playerRef: React.RefObject<HTMLVideoElement>;
    duration: number;
    playerCurrentTime: number
    onHistoryRefresh,
}

// 转换 HH:mm:ss 到秒数
function timeStringToSeconds(time: string): number {
    const parts = time?.split(':')?.map?.(Number);
    if (parts.length === 3) return parts?.[0] * 3600 + parts?.[1] * 60 + parts?.[2];
    if (parts.length === 2) return parts?.[0] * 60 + parts?.[1];
    return Number(time) || 0;
}

// 秒数转 HH:mm:ss
function formatSecondsToHMS(seconds: number): string {
    const h = Math.floor(seconds / 3600)?.toString()?.padStart?.(2, '0');
    const m = Math.floor((seconds % 3600) / 60)?.toString()?.padStart?.(2, '0');
    const s = Math.floor(seconds % 60)?.toString()?.padStart?.(2, '0');
    return `${h}:${m}:${s}`;
}

const CurrentRecord: React.FC<CurrentRecordProps> = ({liveStreamId, playerCurrentTime,dataList,dataHistory,  onSelectChange, onHistoryRefresh,playerRef, duration }) => {
    const [searchKeyword, setSearchKeyword] = useState('');
    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
    const [marker, setMarker] = useState<[string, string]>(['', '']); // start, end

    const [isPlaying, setIsPlaying] = useState(false);
    const [playbackRate, setPlaybackRate] = useState(1);
    const [sliderValue, setSliderValue] = useState(0);
    const initializedRef = useRef(false);

    // 全量商品数据（该场直播所有商品）
    const [allDataList, setAllDataList] = useState<ProductItem[]>(dataList);
    // 原始数据（从API获取的更新数据 or 全量商品数据 即搜索过滤前的数据）
    const [originalDataList, setOriginalDataList] = useState<ProductItem[]>(dataList);
    // 展示数据（可能是原始数据 or 全量商品数据 or 搜索过滤后的数据）
    const [displayDataList, setDisplayDataList] = useState<ProductItem[]>(dataList);

    //商品快照捕获时间
    const [captureTime,setCaptureTime] = useState('');
    const [showSerial, setShowSerial] = useState(false);

    // 提交标注记录相关
    const [submitting, setSubmitting] = useState(false);
    const canSubmit = Boolean(marker[0] && marker[1]);

    // 鼠标悬浮slider滑轨时显示值
    const [hoverSliderValue, setHoverSliderValue] = useState<number | null>(null);

    // 渲染“主播讲解时间”列
    const renderAnchorRecordTimeRange = (text: any, record: ProductItem) => {
        const { anchorRecordStartTime, anchorRecordEndTime } = record;

        if (anchorRecordStartTime == null || anchorRecordEndTime == null) {
            return (
                <div style={{ textAlign: 'center', width: '100%' }}>
                    -
                </div>
            );
        }

        const start = formatSecondsToHMS(anchorRecordStartTime);
        const end = formatSecondsToHMS(anchorRecordEndTime);

        const handleJump = (time: number) => {
            if (playerRef.current) {
                playerRef.current.currentTime = time;
                setSliderValue?.(time);
                setIsPlaying(true);
                playerRef.current?.play();
            }
        };

        return (
            <div style={{ whiteSpace: 'nowrap' }}>
                <Tooltip title={`点击定位`}>
                    <a onClick={() => handleJump(anchorRecordStartTime)}>{start}</a>
                </Tooltip>
                {' - '}
                <Tooltip title={`点击定位`}>
                    <a onClick={() => handleJump(anchorRecordEndTime)}>{end}</a>
                </Tooltip>
            </div>
        );
    };

    // 渲染“商品图片”列
    const renderImageUrlsCell = (urls: string[]) => {
        const maxVisible = 3;
        const visible = urls.slice(0, maxVisible);
        const hidden = urls.slice(maxVisible);

        return (
            <div style={{ display: 'flex', alignItems: 'center',gap: 8 }}>
                {visible.map((url, idx) => (
                    <Image
                        key={idx}
                        width={150}
                        height={150}
                        src={url}
                        style={{ marginRight: hidden.length > 0 ? 4 : 0 }}
                        loading="lazy"
                    />
                ))}
                {hidden.length > 0 && (
                    <Tooltip
                        title={
                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                                {hidden.map((url, idx) => (
                                    <Image key={idx} width={150} height={150} src={url} />
                                ))}
                            </div>
                        }
                    >
          <span style={{ cursor: 'pointer', color: '#888',marginLeft: 4 }}>
            +{hidden.length}
          </span>
                    </Tooltip>
                )}
            </div>
        );
    }


    const columns= useMemo(() => {
        const base = [
            {
                title: '商品ID',
                dataIndex: 'itemId',
                key: 'itemId',
                width: 150,
                render: (itemId: number) => (
                    <a
                        href={`${ITEM_LINK_PREFIX}${itemId}`}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        {itemId}
                    </a>
                ),
            },
            {
                title: '商品标题',
                dataIndex: 'title',
                key: 'title',
                width: 200,
                render: (text: string) => (
                    <div
                        style={{
                            whiteSpace: 'normal',
                            wordBreak: 'break-word',
                        }}
                    >
                        {text}
                    </div>
                ),
            },
            {
                title: (
                    <span> 主播讲解时间{' '}
                        <Tooltip
                            title={
                                <div style={{ fontSize: '12px', width: '150px' }}>
                                    主播在直播讲解时手动选择的开始讲解和结束讲解时间
                                </div>
                            }
                        >
                        <InfoCircleOutlined style={{ color: 'rgba(0,0,0,0.45)', cursor: 'pointer' }} />
                        </Tooltip>
                    </span>
                ),
                dataIndex: 'anchorRecordStartTime',
                key: 'anchorRecordTimeRange',
                width: 170,
                sorter: (a: ProductItem, b: ProductItem) => {
                    const aHasTime = a.anchorRecordStartTime != null;
                    const bHasTime = b.anchorRecordStartTime != null;

                    // 都有值 → 正常比较
                    if (aHasTime && bHasTime) {
                        return a.anchorRecordStartTime! - b.anchorRecordStartTime!;
                    }

                    // 一个有值，一个没值 → 有值排前
                    if (aHasTime && !bHasTime) return -1;
                    if (!aHasTime && bHasTime) return 1;

                    // 都没有值 → 不排序
                    return 0;
                },
                render:renderAnchorRecordTimeRange,
            },
            {
                title: '商品属性',
                dataIndex: 'itemCategoryPropInfo',
                key: 'itemCategoryPropInfo',
                width: 260,
                render: (info?: Record<string, string> | null) => {
                    const entries = Object.entries(info ?? {});
                    return (
                        <div style={{ whiteSpace: 'normal', wordBreak: 'break-all' }}>
                            {entries.map(([prop, val]) => (
                                <div key={prop}>
                                    {prop}: {val}
                                </div>
                            ))}
                        </div>
                    );
                },
            },
            {
                title: '商品图片',
                dataIndex: 'imageUrls',
                key: 'imageUrls',
                width: 500,
                render: renderImageUrlsCell,
            }
        ];

        if(showSerial){
            return [
                {
                    title: '序号',
                    dataIndex: 'serialNumber',
                    key: 'serialNumber',
                    width: 100,
                },
                ...base,
            ];
        }

        return base;
    }, [showSerial]);


    // 当父组件传入的dataList变化时（即请求新的直播间），重置全量数据、原始数据、展示数据
    useEffect(() => {
        setAllDataList(dataList);
        setOriginalDataList(dataList);
        setDisplayDataList(dataList);
    }, [dataList]);



    const rowSelection = {
        selectedRowKeys,
        onChange: (keys: React.Key[]) => {
            setSelectedRowKeys(keys as string[]);
            onSelectChange(keys as string[]);
        }
    };


    // 播放/暂停
    const handleTogglePlay = () => {
        const player = playerRef.current;
        if (!player) return;
        if (isPlaying) {
            player.pause?.(); // 调用 pause 方法（如果存在）
        } else {
            player.play?.();  // 调用 play 方法（如果存在）
        }

        setIsPlaying(!isPlaying);
    };

    // 快退 10s
    const handleRewind = () => {
        const newTime = Math.max(sliderValue - 10, 0);
        setSliderValue(newTime);
        if (playerRef.current) playerRef.current.currentTime = newTime;
    };

    // 快进 10s
    const handleForward = () => {
        if (duration === undefined) return; // 无效时不前进
        const maxTime = duration;
        const newTime = Math.min(sliderValue + 10, maxTime);
        setSliderValue(newTime);
        if (playerRef.current) playerRef.current.currentTime = newTime;
    };

    // 设置播放速度
    const speedMenu = (
        <Menu onClick={({ key }) => {
            const rate = Number(key);
            setPlaybackRate(rate);
            if (playerRef.current) playerRef.current.playbackRate = rate;
        }}>
            {[0.5, 1, 1.5, 2, 2.5, 3].map(rate => (
                <Menu.Item key={rate}>{rate}×</Menu.Item>
            ))}
        </Menu>
    );


    const [hoveredMark, setHoveredMark] = useState<number | null>(null);
    const marks = dataHistory.reduce((acc, item) => {
        const start = timeStringToSeconds(item.startTime);
        const end = timeStringToSeconds(item.endTime);

        acc[start] = {
            label: (
                <span
                    onMouseEnter={() => setHoveredMark(start)}
                    onMouseLeave={() => setHoveredMark(null)}
                    style={{
                        fontSize: 12,
                        opacity: hoveredMark === start ? 1 : 0,
                        transition: 'opacity 0.3s',
                    }}
                >
        {item.startTime}
      </span>
            ),
        };

        acc[end] = {
            label: (
                <span
                    onMouseEnter={() => setHoveredMark(end)}
                    onMouseLeave={() => setHoveredMark(null)}
                    style={{
                        fontSize: 12,
                        opacity: hoveredMark === end ? 1 : 0,
                        transition: 'opacity 0.3s',
                    }}
                >
        {item.endTime}
      </span>
            ),
        };
        return acc;
    }, {} as Record<number, { label: React.ReactNode }>);

    const getSliderTrackStyle = useCallback((): string => {
        if (!duration || duration <= 0) {
            return 'transparent';
        }
        const segments: string[] = [];
        const total = duration;
        let lastEnd = 0;
        let maxend = 0;
        const sortedHistory = [...dataHistory].sort(
            (a, b) =>
                timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime)
        );
        for (const item of sortedHistory) {
            const start = timeStringToSeconds(item.startTime);
            const end = timeStringToSeconds(item.endTime);
            if (sliderValue >= end) {
                maxend = end
                // 蓝色段（非标注区域）
                if (start > lastEnd) {
                    segments.push(`#1677ff ${(lastEnd / total) * 100}% ${(start / total) * 100}%`);
                }
                // 红色段（标注区域）
                segments.push(`#fd7f07  ${(start / total) * 100}% ${(end / total) * 100}%`);
                lastEnd = end;
            }
            else if(sliderValue<end)
                segments.push(`#1677ff ${(lastEnd / total) * 100}% ${(sliderValue / total) * 100}%`);
        }
        if(maxend==lastEnd){
            segments.push(`#1677ff ${(lastEnd / total) * 100}% ${(sliderValue / total) * 100}%`);
        }
        if (lastEnd < total) {
            segments.push(`rgba(0,0,0,0.04) ${(sliderValue / total) * 100}% 100%`);
        }
        return `linear-gradient(to right, ${segments.join(', ')})`;
    }, [dataHistory, duration,sliderValue]);
   // 当鼠标悬浮在 Slider 的轨道时，显示该位置对应的时间
    const handleSliderMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
        const { left, width } = e.currentTarget.getBoundingClientRect();
        const percent = (e.clientX - left) / width;
        const clampedPercent = Math.max(0, Math.min(1, percent));
        const rawValue = duration * clampedPercent;
        const steppedValue = Math.round(rawValue);
        setHoverSliderValue(steppedValue);
    };
    const handleSliderMouseLeave = () => {
        setHoverSliderValue(null);
    };


    // 接口调用，捕获当前在车商品列表
    const updateDataList = async (currentTimeInSeconds: number): Promise<ProductItem[]> => {
        try {
            const params ={
                liveStreamId,
                specifiedTime: currentTimeInSeconds,}
            const result = await API.OnSaleItemUpdate(params);
            const updatedData = result.itemInfos;
            return updatedData;
        } catch (error) {
            console.error('API调用失败', error);
            message.error('获取数据失败，请重试');
            return [];
        }
    };

    // 设置定时器，每10秒调用一次 updateDataList
    // useEffect(() => {
    //     let intervalId: NodeJS.Timeout | null = null;
    //     if (isPlaying) {
    //         intervalId = setInterval(() => {
    //             if (playerRef.current) {
    //                 const currentTimeInSeconds = playerRef.current?.currentTime;
    //                 updateDataList(currentTimeInSeconds);
    //             }
    //         }, 10000); // 10000ms = 10s
    //     } else {
    //         if (intervalId) {
    //             clearInterval(intervalId);
    //         }
    //     }
    //
    //     return () => {
    //         if (intervalId) {
    //             clearInterval(intervalId);
    //         }
    //     };
    // }, [isPlaying, playerRef, liveStreamId]);
    // 控制拖拽和播放
    const [isDragging, setIsDragging] = useState(false);
    useEffect(() => {
        if (!isDragging) {
            setSliderValue(playerCurrentTime);
        }
    }, [playerCurrentTime, isDragging]);


    // 只要滑块的值改变，就立即触发一次
    const handleSliderChange = (val: number) => {
        setIsDragging(true);
        setSliderValue(val);
    };

    // 只有当用户“松开”滑块（或者键盘操作结束）时才触发一次。
    const handleSliderChangeComplete = (val: number) => {
        setIsDragging(false);
        setSliderValue(val); // 最终值同步
        if (playerRef.current) {
            playerRef.current.currentTime = val;
            // if (val > 0) {
            //     updateDataList(val);
            // }
        }
    };

    /** 进度条读取 - 组件挂载或 liveStreamId 变更时，从 storage 读取进度，获取当前在车商品列表 */
    useEffect(() => {
        if (!liveStreamId) return;
        initializedRef.current = false;

        const key = `sliderValue-${liveStreamId}`;
        const saved = localStorage.getItem(key);
        const val = saved !== null ? Number(saved) : 0;
        setSliderValue(val);

        if (playerRef.current) {
            playerRef.current.currentTime = val;
        }
        // 标记“已完成第一次读取”
        initializedRef.current = true;
    }, [liveStreamId]);


    /** 进度条保存 -  任何时候 sliderValue 变化，都存一次 */
    useEffect(() => {
        if (!liveStreamId || !initializedRef.current) return;
        localStorage.setItem(`sliderValue-${liveStreamId}`, String(sliderValue));
    }, [sliderValue, liveStreamId]);

    // 标注开始时间
    const handleMarkStart = () => {
        if(marker[1]){
            const endSec = timeStringToSeconds(marker?.[1]);
            if (sliderValue >= endSec) {
                message.warning('开始时间必须早于结束时间');
                return;
            }
        }
        setMarker([formatSecondsToHMS(sliderValue), marker?.[1]]);
    };

    // 标注结束时间
    const handleMarkEnd = () => {
        const startSec = timeStringToSeconds(marker?.[0]);
        if (sliderValue <= startSec) {
            message.warning('结束时间必须晚于开始时间');
            return;
        }
        setMarker([marker?.[0], formatSecondsToHMS(sliderValue)]);
        setIsPlaying(false);
        playerRef.current?.pause();
    };

    // 标注提交后重置
    const handleReset = () => {
        setMarker(['', '']);
        setSelectedRowKeys([]);
    };

    // 捕获在车商品快照
    const handleCapture = async () => {
        setCaptureTime(formatSecondsToHMS(sliderValue));
        try {
            const capturedDataList = await updateDataList(sliderValue);
            setOriginalDataList(capturedDataList);
            setDisplayDataList(capturedDataList);
            setSelectedRowKeys([]);
            setShowSerial(true);
        } catch (err) {
            message.error('获取在车商品失败');
        }
    };

    //清空捕获，返回全量商品
    const handleCaptureClear = () => {
        setCaptureTime('');
        setShowSerial(false);
        setDisplayDataList(allDataList);
    };

    // 商品搜索
    const handleSearch = (keyword: string) => {
        setSearchKeyword(keyword);
        if (!keyword) {
            setDisplayDataList(originalDataList);
            return;
        }
        const kw = keyword.toLowerCase();
        // a. 过滤数据
        const filteredList = displayDataList.filter(item => {
            // a.1. 标题匹配
            if (item?.title?.toLowerCase?.()?.includes?.(kw)) return true;
            // a.2. ID 匹配
            if (String(item.itemId).includes(keyword)) return true;
            // a.3. 属性匹配：key 或 value 包含
            if (Object.entries(item.itemCategoryPropInfo || {}).some(
                    ([prop, val]) => {
                        const v = val ?? '';
                        return (
                            prop?.toLowerCase()?.includes?.(kw) ||
                            v?.toLowerCase()?.includes?.(kw)
                        );
                    }
                )
            ) {
                return true;
            }
            // a.4. 序号匹配 (如果 showSerial 为 true)
            if (showSerial && item.serialNumber != null && String(item.serialNumber).includes(keyword)) {
                return true;
            }
            return false;
        });
        // b.再排序：把“正好匹配序号”的条目排到最前
        if (showSerial) {
            filteredList.sort((a, b) => {
                const aMatch = a.serialNumber != null && String(a.serialNumber) === keyword;
                const bMatch = b.serialNumber != null && String(b.serialNumber) === keyword;
                if (aMatch && !bMatch) return -1;
                if (!aMatch && bMatch) return 1;
                return 0;
            });
        }
        setDisplayDataList(filteredList);
    };


    // 处理提交
    const handleSubmit = () => {
        if (!canSubmit) return; // 如果连开始/结束都没选，不会进来
        // 如果没选商品，弹 confirm
        if (selectedRowKeys.length === 0) {
            Modal.confirm({
                title: '未选择标注商品',
                content: (
                    <span>
                    您当前<span style={{ color: 'red' }}>未选择</span>任何标注商品，是否确认提交？
                    </span>
                ),
                okText: '确认提交',
                cancelText: '取消',
                onOk: doSubmit,
            });
        } else {
            doSubmit();
        }
    };
    // 真实的提交逻辑
    const doSubmit = async () => {
        setSubmitting(true);
        try {
            // 校验过了，marker[0]、marker[1] 一定存在
            const startSeconds = timeStringToSeconds(marker[0]);
            const endSeconds = timeStringToSeconds(marker[1]);
            const labelItemInfoList = selectedRowKeys.map(key => {
                const product = displayDataList.find(p => String(p.itemId) === key);
                return {
                    itemId: product?.itemId ?? Number(key),
                    itemCategoryPropInfo: product?.itemCategoryPropInfo ?? {},
                    imageUrls: product?.imageUrls ?? '',
                };
            });
            const params = {
                liveStreamId,
                startTime: startSeconds,
                endTime: endSeconds,
                labelItemIdList: selectedRowKeys.map(id => Number(id)),
                labelItemInfoList,
            };
            const result = await API.LabelInsert(params);
            if (result.code === '0' || result.result === 0) {
                message.success('当前标注提交成功');
                handleReset();
                onHistoryRefresh();
            } else {
                message.error(`当前标注提交失败: ${result.error_msg}`);
            }
        } catch (err) {
            message.error('当前标注提交异常');
        } finally {
            setSubmitting(false);
        }
    };

    const speedMenuProps = {
        items: [0.5, 1, 1.5, 2, 2.5, 3,3.5,4].map(rate => ({
            key: rate.toString(),
            label: `${rate}×`,
        })),
        onClick: ({ key }: { key: string }) => {
            const rate = Number(key);
            setPlaybackRate(rate);
            if (playerRef.current) {
                playerRef.current.playbackRate = rate;
            }
        },
    };

    return (
        <div>
            {/* —— 视频进度 & 标注条 —— */}
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 5 }}>
                <div style={{display: 'flex', justifyContent: 'space-between', marginRight: 8}}>
                    <span>{formatSecondsToHMS(sliderValue)} </span>
                    <span> / {formatSecondsToHMS(duration)}</span>
                </div>
                <Tooltip title="后退 10s">
                    <Button
                        onClick={handleRewind}
                        type="text"                      // 文字按钮，无边框
                        icon={<BackwardOutlined style={{fontSize: 26}}/>}     // 只渲染图标
                        style={{marginRight: 5}}
                    />
                </Tooltip>

                <Tooltip title="播放/暂停">
                    <Button
                        onClick={handleTogglePlay}
                        icon={isPlaying ? <PauseCircleOutlined style={{fontSize: 26}}/> : <PlayCircleOutlined style={{fontSize: 26}}/>}
                        style={{marginRight: 5}}
                        type={"text"}
                    />
                </Tooltip>

                <Tooltip title="前进 10s">
                    <Button
                        onClick={handleForward}
                        type="text"
                        icon={<ForwardOutlined style={{fontSize: 26}}/>}
                    />
                </Tooltip>

                <Dropdown menu={speedMenuProps} trigger={['click']}>
                    <Button
                        type="text"
                        size="large"
                        style={{width: 60, height: 30, marginRight: 2, fontSize: 14}}
                    >
                        倍速 {playbackRate}×
                    </Button>
                </Dropdown>
                <div
                    style={{  height: 30, flex: 1, margin: '0 20px 0 2px ' ,position: 'relative',overflow: 'visible' }}
                    onMouseMove={handleSliderMouseMove}
                    onMouseLeave={handleSliderMouseLeave}
                >
                    {hoverSliderValue !== null && (
                        <div
                            style={{
                                position: 'absolute',
                                top: -25,
                                left: `${(hoverSliderValue / duration) * 100}%`,
                                transform: 'translateX(-50%)',
                                background: '#fff',
                                padding: '2px 6px',
                                borderRadius: 4,
                                boxShadow: '0 0 4px rgba(0,0,0,0.15)',
                                fontSize: 13,
                                whiteSpace: 'nowrap',
                                pointerEvents: 'none',
                                zIndex: 1000,
                            }}
                        >
                            {formatSecondsToHMS(hoverSliderValue)}
                        </div>
                    )}
                    <Slider
                        min={0}
                        max={duration}
                        step={1}
                        value={sliderValue}
                        onChange={handleSliderChange}
                        onChangeComplete={handleSliderChangeComplete}
                        // marks={marks}
                        tooltip={{
                            formatter: val => (val !== undefined ? formatSecondsToHMS(val): ''),
                            getPopupContainer: () => document.body
                        }}
                        defaultValue={0}
                        style={{
                            marginTop: 4
                        }}
                        styles={{
                            handle: {
                                display: 'none',
                            },

                            track: {
                                background: 'transparent',
                            },
                            rail: {
                                height: 20,
                                marginTop: -4,
                                background: getSliderTrackStyle(),
                            },
                        }}
                    />
                </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Button type="primary" onClick={handleMarkStart}>标注开始</Button>
                    <Button type="primary" onClick={handleMarkEnd} style={{ marginLeft: 8 }}>标注结束</Button>
                    <Input
                        value={marker?.[0]}
                        readOnly
                        style={{ width: 90, margin: '0 8px' }}
                        placeholder="开始时间"
                    />
                    <Input
                        value={marker?.[1]}
                        readOnly
                        style={{ width: 90, marginRight: 8 }}
                        placeholder="结束时间"
                    />
                    <Button style={{width:70}}  onClick={() => { setMarker(['', '']); }}>重置
                        <Tooltip
                            title={
                                <div style={{ fontSize: '12px', width: '90px' }}>
                                    重置标注开始和标注结束时间
                                </div>
                            }
                        >
                            <InfoCircleOutlined
                                style={{  color: 'rgba(0,0,0,0.45)' }}
                            />
                        </Tooltip>
                    </Button>

                    <Tooltip title={!marker[0] || !marker[1] ? '请先标注开始和结束时间' : ''} >
                        <Button
                            color='orange' variant="solid"
                            disabled={!canSubmit || submitting}
                            loading={submitting}
                            style={{ width: 80, marginLeft: 20 }}
                            onClick={handleSubmit}
                        >
                            标注完成
                        </Button>
                    </Tooltip>

                    {/*在车商品快照*/}
                    <Button type="primary" style={{marginLeft:70}} onClick={handleCapture}>在车商品快照
                        <Tooltip
                            title={
                                    <div style={{ fontSize: '12px', width: '150px' }}>
                                        点击获取当前时刻小黄车商品列表，以便快速找到口播中提到的链接序号
                                    </div>
                                }
                        >
                            <InfoCircleOutlined style={{  color: 'rgba(0,0,0,0.45)' }} />
                        </Tooltip>
                    </Button>
                    <Input
                        value={captureTime}
                        readOnly
                        style={{ width: 90, margin: '0 8px' }}
                        placeholder="快照时间"
                    />
                    <Button onClick={handleCaptureClear} style={{width:70}}>恢复
                        <Tooltip
                            title={
                                <div style={{ fontSize: '12px', width: '100px' }}>
                                    清空快照，恢复为整场直播商品
                                </div>
                            }
                        >
                            <InfoCircleOutlined style={{  color: 'rgba(0,0,0,0.45)' }} />
                        </Tooltip>
                    </Button>

                </div>

                <Input.Search
                    placeholder="商品信息搜索"
                    value={searchKeyword}
                    onChange={e => {
                        const v = e.target.value;
                        setSearchKeyword(v);
                        if (v === '') {
                            handleSearch('');
                        }
                    }}
                    onSearch={handleSearch}
                    enterButton={<SearchOutlined />}
                    style={{ width: 250, marginLeft: 'auto' }}
                />
            </div>
            
            {/*商品信息表格*/}
            <ConfigProvider locale={zhCN}>
                <Table<ProductItem>
                    rowSelection={rowSelection}
                    columns={columns}
                    dataSource={displayDataList}
                    rowKey="itemId"
                    pagination={{
                        pageSize: 100,
                        showSizeChanger: false, // 不允许用户修改
                        showQuickJumper: true,  // 可选：支持跳页
                        showTotal: (total, range) => `共 ${total} 条，当前显示第 ${range[0]}-${range[1]} 条`,
                    }}
                    size="middle"
                />
            </ConfigProvider>
        </div>
    );
};

export default CurrentRecord;