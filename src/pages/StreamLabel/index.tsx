import React, {useCallback, useEffect, useRef, useState} from 'react';
import Flex from '@/components/Flex';
import {Input, Tabs, Tooltip, Typography} from '@m-ui/react';
import StickyBox from 'react-sticky-box';
import {QuestionCircleOutlined,} from '@m-ui/icons';
import type {KwaiPlayer} from '@ks-video/kwai-player-web/react';
import { KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import cls from 'classnames/bind';
import style from './index.module.less';
import {SOP_LINK, TEST_VIDEO_IDS} from './constant';
import type {ProductItem} from "@/pages/StreamLabel/Tabs/CurrentRecord";
import CurrentRecord from "@/pages/StreamLabel/Tabs/CurrentRecord";
import type {HistoryItem} from "@/pages/StreamLabel/Tabs/HistoryRecord";
import HistoryRecord from "@/pages/StreamLabel/Tabs/HistoryRecord";
import {Button, message} from "antd";
import * as API from '@/common/API/StreamLabelAPI';

const cx = cls.bind(style);

export default function StreamLabel() {
  /** --------------------------------------- 状态变量定义 START------------------------------------------------------- */
  const playerRef = useRef<KwaiPlayer | null>(null);
  const videoRefForChild = (playerRef as unknown) as React.RefObject<HTMLVideoElement>;

  const stored = localStorage.getItem('liveStreamId') ?? '';
  const [searchValue, setSearchValue] = useState(stored);
  const [liveStreamId, setLiveStreamId] = useState<number>(() => {
    return stored ? Number(stored) : 0;
  });
  const [activeKey, setActiveKey] = useState<'current'|'history'>('current');
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [history, setHistory] = useState<HistoryItem[]>([]);

  // 商品列表和视频URL
  const [products, setProducts] = useState<ProductItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductItem[]>(products);

  const [videoUrls, setVideoUrls] = useState<string[]>([]);
  const [srcIndex, setSrcIndex] = useState(0);
  const videoSrc = videoUrls[srcIndex] || '';
  /** --------------------------------------- 状态变量定义 END------------------------------------------------------- */


  /** --------------------------------------- 函数定义 START------------------------------------------------------- */
  // 存储接口返回的 UTC 时间相关数据（playbackTotalMs、totalUtcMs、startUtc）
  const [liveUtcInfo, setLiveUtcInfo] = useState<{
    playbackTotalMs?: number;
    totalUtcMs?: number;
    startUtc?: number;
  } | null>(null);

  // 存储实时计算出的视频当前 UTC 时间
  const [currentVideoUtc, setCurrentVideoUtc] = useState<string>('加载中...');
  useEffect(() => {
    setFilteredProducts(products);
  }, [products]);
  // 新增：计算视频当前 UTC 时间
  const calculateUtcTime = (currentLocalTime: number, utcInfo: typeof liveUtcInfo) => {
    if (!utcInfo || !utcInfo.playbackTotalMs || !utcInfo.totalUtcMs || !utcInfo.startUtc) {
      return '数据加载中...';
    }

    // 计算本地时间占回放总时长的比例（转换单位为秒）
    const localRatio = currentLocalTime / (utcInfo.playbackTotalMs / 1000);
    // 计算时间偏移量
    const timeOffsetMs = localRatio * utcInfo.totalUtcMs;
    // 计算最终本地时间并格式化（YYYY-MM-DD HH:mm:ss）
    const localDate = new Date(utcInfo.startUtc + timeOffsetMs);
    return `${localDate.getFullYear()}-${
        (localDate.getMonth() + 1).toString().padStart(2, '0')
    }-${
        localDate.getDate().toString().padStart(2, '0')
    } ${
        localDate.getHours().toString().padStart(2, '0')
    }:${
        localDate.getMinutes().toString().padStart(2, '0')
    }:${
        localDate.getSeconds().toString().padStart(2, '0')
    }`;
  };
  // 接口调用函数：获取视频和商品信息
  // 调用 LabelUTCTime 接口获取 UTC 时间信息
  const fetchLiveUtcInfo = useCallback(async (id: number) => {
    const maxRetries = 2;
    let retries = 0;

    const attemptFetch = async () => {
      try {
        const utcResponse = await API.LabelUTCTime({ liveStreamId: id });

        // 1. 修复code校验逻辑：兼容字符串和数字类型的"0"，并处理可能的undefined
        const code = utcResponse.code;
        const isSuccess = code === "0" || code === 0; // 同时支持字符串和数字类型的0
        if (!isSuccess) {
          const errorMsg = `接口失败：code=${code ?? 'undefined'}，msg=${utcResponse.errorMsg || '无信息'}`;
          console.error("[UTC] 接口失败：", errorMsg);
          throw new Error(errorMsg);
        }

        // 2. 增强data格式处理：增加异常捕获
        let parsedData: { playbackTotalMs?: number; totalUtcMs?: number; startUtc?: number } | null = null;
        if (typeof utcResponse.data === "string") {
          try {
            parsedData = JSON.parse(utcResponse.data);
          } catch (parseErr) {
            throw new Error(`data解析失败：${(parseErr as Error).message}，原始数据：${utcResponse.data}`);
          }
        } else if (typeof utcResponse.data === "object" && utcResponse.data !== null) {
          parsedData = utcResponse.data;
        } else {
          throw new Error(`data格式异常：${typeof utcResponse.data}，值：${utcResponse.data}`);
        }

        // 3. 数据合法性校验：保持不变
        const requiredFields = ['playbackTotalMs', 'totalUtcMs', 'startUtc'];
        const invalidFields = requiredFields.filter(field => {
          const value = parsedData![field];
          return value === undefined || value === null || typeof value !== "number";
        });
        if (invalidFields.length > 0) {
          throw new Error(`data缺少/无效字段：${invalidFields.join(', ')}，数据：${JSON.stringify(parsedData)}`);
        }

        // 4. 成功处理：保持不变
        setLiveUtcInfo(parsedData);
        const initialUtc = calculateUtcTime(0, parsedData);
        setCurrentVideoUtc(initialUtc);
        console.log("[UTC] 数据获取成功：", parsedData, "初始 UTC 时间：", initialUtc);

      } catch (err) {
        if (retries < maxRetries) {
          retries++;
          console.warn("[UTC] 重试请求，剩余次数：", maxRetries - retries, "错误：", err);
          setTimeout(attemptFetch, 1000);
        } else {
          console.error("[UTC] 所有重试失败：", err);
          setLiveUtcInfo(null);
          setCurrentVideoUtc('获取UTC失败');
        }
      }
    };

    attemptFetch();
  }, [calculateUtcTime]);


  const fetchInfo = useCallback(async (id: number) => {
    try {
      const info = await API.LabelInfo({ liveStreamId: id });
      setVideoUrls(info.videoUrl ?? []);
      setSrcIndex(0);
      setProducts(info.itemInfos ?? []);
      setFilteredProducts(info.itemInfos ?? []);
    } catch {
      message.error('获取视频信息失败');
    }
  }, []);

  // 页面入口：如果localStorage中存在liveStreamId，进入页面时就自动调用一次，获取视频和商品信息
  useEffect(() => {
    if (searchValue) {
      const id = Number(searchValue);
      setLiveStreamId(id);
      fetchInfo(id);
      fetchLiveUtcInfo(id);
    }
  }, []);

  // 接口调用函数：获取历史标注记录
  const fetchHistory = useCallback(async () => {
    if (!liveStreamId) return;
    try {
      const historyData = await API.LabelHistory({ liveStreamId: Number(liveStreamId) });
      setHistory(historyData ?? []);
    } catch (err) {
      console.error(err);
      message.error('获取历史评测记录失败');
    }
  }, [liveStreamId]);

  // 页面入口：如果localStorage中存在liveStreamId，进入页面时就自动调用一次，获取历史标注记录
  useEffect(() => {
    setHistory([]);    // 切 ID 先清空
    fetchHistory();
    if (liveStreamId) {
      localStorage.setItem('liveStreamId', String(liveStreamId));
    }
  }, [liveStreamId]);

  // 视频播放器相关函数
  const onTimeUpdate = useCallback((_: any) => {
    console.log('onTimeUpdate 触发了！');
    const inst = (playerRef.current as any)
    if (!inst) return
    let t: number;
    if (typeof inst.currentTime === 'number') {
      t = inst.currentTime;
    } else if (typeof inst.getCurrentTime === 'function') {
      t = inst.getCurrentTime();
    } else {
      t = 0;
    }
    setCurrentTime(t);
    if (liveUtcInfo) {
      setCurrentVideoUtc(calculateUtcTime(t, liveUtcInfo));
    }
  }, [playerRef, liveUtcInfo])

  const onLoadedMetadata = useCallback(() => {
    const inst = playerRef.current
    if (!inst) return
    const d = (inst as any)?.duration
    if (typeof d === 'number') setDuration(d)
  }, [])
  const handleTabClick = (key: string) => {
    if (key === 'history') {
      fetchHistory(); // 重新调用接口
    }
  };

  const handleTabChange = (key: string) => {
    setActiveKey(key as 'current' | 'history');
  };


  return (
    <Flex vertical gap={16} className={cx('video')}>
      <Flex
          className={cx('top')}
          gap={16}
          align="center"
          style={{ flexWrap: 'nowrap' , paddingTop:"20px"}}
      >
        {/* 标题 + Tooltip */}
        <Flex gap={8} align="center" style={{ flex: 'none', float:"left", paddingRight:"15px", paddingLeft:"20px", height:30, lineHeight:'30px', fontSize:'18px'}}>
          <Typography.Text strong className={cx('title')} >
            直播视频标注
          </Typography.Text>
          <Tooltip title="输入 liveStreamId 搜索">
            <QuestionCircleOutlined style={{ color: '#999' ,paddingLeft:'5px'}} />
          </Tooltip>
        </Flex>

        {/* Search 输入框 */}
        <Flex style={{ flex: 1, minWidth: 200, float:"left"}}>
          <Input.Search
              placeholder={`liveStreamId ${liveStreamId}`}
              value={searchValue}
              onChange={e => setSearchValue(e.target.value)}
              enterButton
              style={{ width: '100%' }}
              onSearch={value => {
                // 值同步到 liveStreamId，触发 localStorage & 数据拉取
                const id = Number(value);
                setSearchValue(value);
                setLiveStreamId(id);
                fetchInfo(id);
                fetchLiveUtcInfo(id)
              }
          }
          />
        </Flex>

        {/* 推荐链接 和 SOP文档 */}
        <Flex gap={8} align="center" style={{ flex: 'none', whiteSpace: 'nowrap', height:30, lineHeight:'30px', fontSize:'18px'}}>
          <Typography.Text type="secondary" style={{ fontSize: 14,  paddingLeft:"10px" }}>
            试试这些：
          </Typography.Text>
          {TEST_VIDEO_IDS.map(item => (
              <Typography.Link
                  key={item}
                  type="secondary"
                  style={{ fontSize: 14 }}
                  onClick={() => {
                    setSearchValue(String(item));  // 更新搜索框的值
                    setLiveStreamId(Number(item)); // 更新 liveStreamId
                    fetchInfo(Number(item));       // 拉取数据
                    fetchLiveUtcInfo(Number(item));
                  }}
              >
                {item}
              </Typography.Link>
          ))}

          <a
              href={SOP_LINK}
              target="_blank"
              rel="noopener noreferrer"
          >
            <Button
                type="link"
                style={{ paddingLeft: 0, fontSize: 16, color: '#1975fa', display: 'flex', alignItems: 'center',float:"right",marginRight:"50px" }}
            >
              <QuestionCircleOutlined style={{ marginRight: 1 }} /> 标注SOP文档
            </Button>
          </a>

        </Flex >

      </Flex>

      <Flex className={cx('columns')} gap={16} style={{ display: 'flex', flex: 1, width: '100%' ,paddingRight:"40px"}}>
        <Flex className={cx('left')} vertical gap={16}  style={{ flex: '0 0 420px' }}  >
          <StickyBox offsetTop={20} className={cx('sticky-box')}>
            <Flex vertical gap={8} align="center" className={cx('player')}>
              {/* 显示实时 UTC 时间 */}
              <div style={{
                position: 'absolute',
                bottom: '50px',
                left: '10px',
                fontSize: 14,
                color: '#fff',
                backgroundColor: 'rgba(0,0,0,0.5)',
                padding: '2px 8px',
                borderRadius: 4,
                zIndex: 10,
              }}>
                {currentVideoUtc}
              </div>
              <KwaiPlayerReact
                key={liveStreamId}
                style={{
                  width: '100%',
                  height: '100%',
                }}
                id="player"
                className="player"
                ref={playerRef}
                onError={() => { if (srcIndex + 1 < videoUrls.length) setSrcIndex(srcIndex + 1); }}
                src={videoSrc}
                autoPlay={false}
                preload="auto"
                onTimeUpdate={onTimeUpdate}
                onLoadedMetadata={onLoadedMetadata}
              />
            </Flex>
          </StickyBox>
        </Flex>

        <Flex className={cx('right')} flex={1} style={{ minWidth: 0, justifyContent: 'center' }} >
          <Tabs activeKey={activeKey} onChange={handleTabChange} onTabClick={handleTabClick}>
            <Tabs.TabPane tab="当前标注" key="current">
              <CurrentRecord
                  dataList={filteredProducts}
                  dataHistory={history}
                  liveStreamId={liveStreamId}
                  onSelectChange={keys => console.log('select', keys)}
                  playerRef={videoRefForChild}
                  duration={playerRef.current?.duration ?? 0}
                  playerCurrentTime={currentTime}
                  onHistoryRefresh={fetchHistory}
              />
            </Tabs.TabPane>
            <Tabs.TabPane tab="历史标注记录" key="history">
              <HistoryRecord data={history}
                             onDelete={async (liveStreamId, liveLabelRecordId) => {
                               try {
                                 await API.LabelDelete({ liveStreamId, liveLabelRecordId });
                                 message.success('删除成功');
                                 fetchHistory();
                               } catch (err) {
                                 console.error(err);
                                 message.error('删除失败，请重试');
                               }
              }}
                               onJumpToTime={(seconds) => {
                               const player = playerRef.current;
                               if (player) {
                               // 调用播放器的跳转方法
                               player.currentTime = seconds;

                             }
                             }
              }/>
            </Tabs.TabPane>
          </Tabs>
        </Flex>
      </Flex>
    </Flex>
  );
  /** --------------------------------------- 布局 END------------------------------------------------------- */
}
