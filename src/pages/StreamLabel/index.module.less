.sticky-box {
  z-index: 200;
  width: 100%;
}

.video {
  /* 如果想让视频区域和右侧区域平分高度，给整个 .video 一个高度 */
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;

  .top {
    height: 40px;
  }
  .left {
    width: 280px;
    padding: 16px;
    background-color: #f8f8f8;
    border-radius: 8px;
    display: flex;
    flex: 0 0 280px;
    flex-direction: column; /* 让 player + info 一个在上一个在下 */

    .player {
      position: relative;
      width: 372px;  //248 400
      height: 600px;
      margin: 0 auto 2px auto;
      background-color: #000;
      border-radius: 8px;
    }
    .info {
      background-color: #fff;
      padding: 12px;
      border-radius: 8px;

      .author {
        padding: 6px 0;
        border-bottom: 1px solid #f0f0f0;
      }
      .line {
        display: flex;
        padding: 8px 4px;
        justify-content: space-between;
        align-items: flex-start;
        border-bottom: 1px solid #f0f0f0;

        &:last-of-type {
          border-bottom: none;
        }

        &.stats {
          padding: 12px;
        }

        .cnt {
          font-size: 12px;
          font-weight: 300;
        }
      }
    }

    .key {
      width: 70px;
      color: #999;
      font-size: 12px;
    }
    .value {
      flex: 1;
      justify-content: flex-end;
      font-size: 12px;
    }
  }

  .right {
    flex: 1;             /* 占据剩余宽度 */
    min-width: 0;        /* 允许它比内容窄，表格才会按百分比缩 */
    border-radius: 8px;
  }
}
