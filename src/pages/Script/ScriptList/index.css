.script-card {
  transition: all 0.3s ease;
  border: 1px solid #e8e8e8;
}

.script-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15) !important;
  border-color: #1890ff;
}

.script-card .ant-card-body {
  padding: 16px;
}

.script-card h5 {
  font-weight: 600;
  color: #262626;
}

.script-card:hover h5 {
  color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .script-card {
    height: 140px;
  }
  
  .script-card .ant-card-body {
    padding: 12px;
  }
}

@media (max-width: 576px) {
  .script-card {
    height: 120px;
  }
  
  .script-card h5 {
    font-size: 14px;
  }
  
  .script-card p {
    font-size: 11px;
  }
}
