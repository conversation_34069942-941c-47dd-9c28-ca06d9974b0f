import React, { useCallback, useEffect, useState } from 'react';
import { Card, Layout, message, Input, AutoComplete, Form, Typography, Row, Col, Spin } from 'antd';
import { Content } from "antd/es/layout/layout";
import * as API from "@/common/API/ScriptAPI";
import { ToolScript } from "../ScriptManagement";
import './index.css';

const { Title } = Typography;
const { Search } = Input;

const ScriptList: React.FC = () => {
  const [form] = Form.useForm();
  const [allScriptList, setAllScriptList] = useState<ToolScript[]>([]);
  const [scriptList, setScriptList] = useState<ToolScript[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>(() => {
    const stored = localStorage.getItem('scriptListSearchHistory');
    return stored ? JSON.parse(stored) : [];
  });

  // 按scene分组的脚本数据
  const [groupedScripts, setGroupedScripts] = useState<Record<string, ToolScript[]>>({});

  const fetchData = useCallback(async (params: any = {}) => {
    try {
      setLoading(true);
      const info = await API.scriptList(params);
      // 解析后端传过来的脚本列表
      const parsedList: ToolScript[] = info?.map?.((item: any) => ({
        ...item,
      }));
      setAllScriptList(parsedList);
      setScriptList(parsedList);
      
      // 按scene分组
      const grouped = parsedList.reduce((acc: Record<string, ToolScript[]>, script) => {
        const scene = script.scene || '其他工具';
        if (!acc[scene]) {
          acc[scene] = [];
        }
        acc[scene].push(script);
        return acc;
      }, {});
      
      setGroupedScripts(grouped);
    } catch {
      message.error('获取脚本列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    localStorage.setItem('scriptListSearchHistory', JSON.stringify(searchHistory));
  }, [searchHistory]);

  // 处理脚本卡片点击
  const handleScriptClick = (script: ToolScript) => {
    const url = `/script-execute?scriptId=${script.id}`;
    window.open(url, '_blank');
  };

  // 查询数据
  const handleSearch = (value: string) => {
    const v = value ?? '';
    const keyword = v?.trim()?.toLowerCase?.();
    if (keyword) {
      // 更新历史：去重、截断到 10 条
      setSearchHistory(prev => {
        const next = [keyword, ...prev.filter(item => item !== keyword)];
        return next.slice(0, 10);
      });
      // 过滤逻辑
      const filteredList = allScriptList.filter(item => {
        const name = (item.name ?? '').toLowerCase();
        const content = (item.content ?? '').toLowerCase();
        const params = (item.params ?? '').toLowerCase();
        const owner = (item.owner ?? '').toLowerCase();
        const scene = (item.scene ?? '').toLowerCase();
        const tags = (item.tags ?? '').toLowerCase();
        const ksn = (item.ksn ?? '').toLowerCase();
        const desc = (item.desc ?? '').toLowerCase();

        return (
          name.includes(keyword) ||
          content.includes(keyword) ||
          params.includes(keyword) ||
          owner.includes(keyword) ||
          scene.includes(keyword) ||
          tags.includes(keyword) ||
          ksn.includes(keyword) ||
          desc.includes(keyword)
        );
      });
      
      setScriptList(filteredList);
      
      // 重新按scene分组
      const grouped = filteredList.reduce((acc: Record<string, ToolScript[]>, script) => {
        const scene = script.scene || '其他工具';
        if (!acc[scene]) {
          acc[scene] = [];
        }
        acc[scene].push(script);
        return acc;
      }, {});
      
      setGroupedScripts(grouped);
    } else {
      setScriptList(allScriptList);
      // 重新按scene分组
      const grouped = allScriptList.reduce((acc: Record<string, ToolScript[]>, script) => {
        const scene = script.scene || '其他工具';
        if (!acc[scene]) {
          acc[scene] = [];
        }
        acc[scene].push(script);
        return acc;
      }, {});
      
      setGroupedScripts(grouped);
    }
  };

  return (
    <Layout style={{ padding: 24, background: '#fff', minHeight: '100vh' }}>
      <Content>
        {/* 搜索栏 */}
        <Form
          form={form}
          layout="inline"
          style={{ display: 'flex', justifyContent: 'center', marginBottom: 32 }}
        >
          <Form.Item name="search" style={{ width: 500 }}>
            <AutoComplete
              options={searchHistory.map(h => ({ value: h }))}
              onSelect={handleSearch}
            >
              <Search
                placeholder="请输入搜索内容: 脚本名称/内容/参数/拥有者/场景/标签/KSN/描述"
                allowClear
                onSearch={handleSearch}
                onChange={e => {
                  const v = e.target.value;
                  if (v === '') {
                    handleSearch('');
                  }
                }}
                size="large"
              />
            </AutoComplete>
          </Form.Item>
        </Form>

        <Spin spinning={loading}>
          {/* 按场景分组展示脚本 */}
          {Object.entries(groupedScripts).map(([scene, scripts]) => (
            <div key={scene} style={{ marginBottom: 40 }}>
              <Title level={3} style={{ marginBottom: 16, color: '#1890ff' }}>
                {scene}
              </Title>
              <Row gutter={[16, 16]}>
                {scripts.map((script) => (
                  <Col key={script.id} xs={24} sm={12} md={8} lg={6} xl={4}>
                    <Card
                      hoverable
                      className="script-card"
                      onClick={() => handleScriptClick(script)}
                      style={{
                        height: 160,
                        cursor: 'pointer',
                        borderRadius: 8,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      }}
                      bodyStyle={{
                        padding: 16,
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                      }}
                    >
                      <div>
                        <Title level={5} style={{ margin: 0, marginBottom: 8, fontSize: 16 }}>
                          {script.name}
                        </Title>
                        <p style={{
                          margin: 0,
                          color: '#666',
                          fontSize: 12,
                          lineHeight: 1.4,
                          overflow: 'hidden',
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                        }}>
                          {script.desc || '暂无描述'}
                        </p>
                      </div>
                      <div style={{ fontSize: 12, color: '#999', marginTop: 8 }}>
                        <div>拥有者: {script.owner}</div>
                        {script.ksn && <div>KSN: {script.ksn}</div>}
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          ))}
          
          {Object.keys(groupedScripts).length === 0 && !loading && (
            <div style={{ textAlign: 'center', padding: 60, color: '#999' }}>
              暂无脚本数据
            </div>
          )}
        </Spin>
      </Content>
    </Layout>
  );
};

export default ScriptList;
