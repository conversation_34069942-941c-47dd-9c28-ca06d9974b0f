import React from 'react';
import { Input, Select, Card, Typography, Space, Tag, Button } from 'antd';
import { PlusOutlined, DeleteOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import type { ExtraOutput } from '@/types/script';

const { Option } = Select;
const { Title, Text } = Typography;

interface ExtraOutputEditorProps {
  extraOutputs: ExtraOutput[];
  onChange: (extraOutputs: ExtraOutput[]) => void;
}

const ExtraOutputEditor: React.FC<ExtraOutputEditorProps> = ({ extraOutputs, onChange }) => {
  
  const addExtraOutput = () => {
    const newExtraOutput: ExtraOutput = {
      type: 'qrcode',
      name: '输出项',
      valueJsonPath: 'JSON.'
    };
    onChange([...extraOutputs, newExtraOutput]);
  };

  const updateExtraOutput = (index: number, field: keyof ExtraOutput, value: string) => {
    const newExtraOutputs = [...extraOutputs];
    newExtraOutputs[index] = { ...newExtraOutputs[index], [field]: value };
    onChange(newExtraOutputs);
  };

  const removeExtraOutput = (index: number) => {
    const newExtraOutputs = extraOutputs.filter((_, i) => i !== index);
    onChange(newExtraOutputs);
  };

  // 上移功能
  const moveUp = (index: number) => {
    if (index > 0) {
      const newExtraOutputs = [...extraOutputs];
      [newExtraOutputs[index - 1], newExtraOutputs[index]] = [newExtraOutputs[index], newExtraOutputs[index - 1]];
      onChange(newExtraOutputs);
    }
  };

  // 下移功能
  const moveDown = (index: number) => {
    if (index < extraOutputs.length - 1) {
      const newExtraOutputs = [...extraOutputs];
      [newExtraOutputs[index], newExtraOutputs[index + 1]] = [newExtraOutputs[index + 1], newExtraOutputs[index]];
      onChange(newExtraOutputs);
    }
  };

  const outputTypeOptions = [
    { value: 'qrcode', label: '二维码', description: '将数据渲染为二维码图片' },
    { value: 'image', label: '图片', description: '显示图片链接内容' },
    { value: 'imageList', label: '图片列表', description: '显示多张图片的网格列表' }, // 添加 imageList 类型选项
    { value: 'link', label: '链接', description: '显示为可点击链接' },
    { value: 'text', label: '文本', description: '显示为纯文本' },
    { value: 'json', label: 'JSON', description: '格式化显示JSON数据' }
  ];

  return (
    <Card
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={5} style={{ margin: 0 }}>输出配置</Title>
          <Space>
            <Tag color="green">{extraOutputs.length} 个输出项</Tag>
            <Button
              type="dashed"
              size="small"
              icon={<PlusOutlined />}
              onClick={addExtraOutput}
            >
              添加输出
            </Button>
          </Space>
        </div>
      }
      size="small"
      style={{ height: '100%', overflow: 'auto' }}
    >
      <div style={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
        {extraOutputs.map((output, index) => (
          <Card
            key={index}
            size="small"
            style={{ marginBottom: 8 }}
            extra={
              <Space size="small">
                <Button
                  type="text"
                  size="small"
                  icon={<UpOutlined />}
                  onClick={() => moveUp(index)}
                  disabled={index === 0}
                  title="上移"
                />
                <Button
                  type="text"
                  size="small"
                  icon={<DownOutlined />}
                  onClick={() => moveDown(index)}
                  disabled={index === extraOutputs.length - 1}
                  title="下移"
                />
                <Button
                  type="text"
                  danger
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => removeExtraOutput(index)}
                  title="删除"
                />
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>输出类型:</label>
                <Select
                  value={output.type}
                  onChange={(value) => updateExtraOutput(index, 'type', value)}
                  size="small"
                  style={{ width: '100%' }}
                >
                  {outputTypeOptions.map((option) => (
                    <Option key={option.value} value={option.value}>
                      <div>
                        <div>{option.label}</div>
                        <div style={{ fontSize: '11px', color: '#999' }}>
                          {option.description}
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>显示名称:</label>
                <Input
                  placeholder="如: 直播间链接"
                  value={output.name}
                  onChange={(e) => updateExtraOutput(index, 'name', e.target.value)}
                  size="small"
                />
              </div>

              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>JSON路径:</label>
                <Input
                  placeholder="如: JSON.liveRoomLink"
                  value={output.valueJsonPath}
                  onChange={(e) => updateExtraOutput(index, 'valueJsonPath', e.target.value)}
                  size="small"
                />
                <Text type="secondary" style={{ fontSize: '11px', display: 'block', marginTop: '4px' }}>
                  用于从执行结果中提取对应的值，支持嵌套路径
                </Text>
              </div>

              {/* 输出类型说明 */}
              <div style={{ padding: '8px', backgroundColor: '#f8f9fa', borderRadius: '4px', fontSize: '11px' }}>
                <Text type="secondary">
                  <strong>{outputTypeOptions.find(opt => opt.value === output.type)?.label}类型：</strong>
                  {outputTypeOptions.find(opt => opt.value === output.type)?.description}
                </Text>
              </div>
            </Space>
          </Card>
        ))}

        {extraOutputs.length === 0 && (
          <div style={{
            textAlign: 'center',
            color: '#999',
            padding: '20px',
            border: '1px dashed #d9d9d9',
            borderRadius: '4px'
          }}>
            暂无输出配置
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              点击"添加输出"按钮配置执行结果的额外输出渲染
            </Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ExtraOutputEditor;
