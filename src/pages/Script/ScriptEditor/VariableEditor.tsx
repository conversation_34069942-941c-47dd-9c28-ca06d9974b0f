import React, { useState } from 'react';
import { Input, Select, Card, Typography, Space, Tag, Button, Radio, Modal, message, Checkbox } from 'antd';
import { PlusOutlined, DeleteOutlined, ImportOutlined } from '@ant-design/icons';
import type { Variable } from '@/types/script';

const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface VariableEditorProps {
  variables: Variable[];
  onChange: (variables: Variable[]) => void;
}

const VariableEditor: React.FC<VariableEditorProps> = ({ variables, onChange }) => {
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importJsonText, setImportJsonText] = useState('');
  const [currentVariableIndex, setCurrentVariableIndex] = useState<number>(-1);

  const updateVariable = (index: number, field: keyof Variable, value: any) => {
    const newVariables = [...variables];
    newVariables[index] = { ...newVariables[index], [field]: value };

    // 如果改变类型，重置相关字段
    if (field === 'type') {
      if (value === 'select' || value === 'radio') {
        newVariables[index].options = [{ label: '选项1', value: 'option1' }];
      } else {
        delete newVariables[index].options;
      }
      // 重置默认值
      newVariables[index].defaultValue = '';
    }

    onChange(newVariables);
  };

  const addOption = (variableIndex: number) => {
    const newVariables = [...variables];
    const variable = newVariables[variableIndex];
    if (!variable.options) {
      variable.options = [];
    }
    variable.options.push({
      label: `选项${variable.options.length + 1}`,
      value: `option${variable.options.length + 1}`
    });
    onChange(newVariables);
  };

  const updateOption = (variableIndex: number, optionIndex: number, field: 'label' | 'value', value: string) => {
    const newVariables = [...variables];
    const variable = newVariables[variableIndex];
    if (variable.options && variable.options[optionIndex]) {
      variable.options[optionIndex][field] = value;
      onChange(newVariables);
    }
  };

  const removeOption = (variableIndex: number, optionIndex: number) => {
    const newVariables = [...variables];
    const variable = newVariables[variableIndex];
    if (variable.options) {
      variable.options.splice(optionIndex, 1);
      onChange(newVariables);
    }
  };

  const openImportModal = (variableIndex: number) => {
    setCurrentVariableIndex(variableIndex);
    setImportModalVisible(true);
    setImportJsonText('');
  };

  const handleImportJson = () => {
    try {
      const jsonData = JSON.parse(importJsonText);
      let options: { label: string; value: string }[] = [];

      if (Array.isArray(jsonData)) {
        // 如果是数组，处理每个元素
        options = jsonData.map((item, index) => {
          if (typeof item === 'string' || typeof item === 'number') {
            return { label: String(item), value: String(item) };
          } else if (typeof item === 'object' && item !== null) {
            // 如果是对象，尝试找到合适的 label 和 value 字段
            const label = item.label || item.name || item.text || item.title || String(item.value || index);
            const value = item.value || item.id || item.key || String(item.label || item.name || index);
            return { label: String(label), value: String(value) };
          }
          return { label: `选项${index + 1}`, value: `option${index + 1}` };
        });
      } else if (typeof jsonData === 'object' && jsonData !== null) {
        // 如果是对象，将键值对转换为选项
        options = Object.entries(jsonData).map(([key, value]) => ({
          label: String(value),
          value: String(key)
        }));
      } else {
        throw new Error('不支持的数据格式');
      }

      if (options.length === 0) {
        message.warning('未能解析出有效的选项数据');
        return;
      }

      // 更新变量的选项
      const newVariables = [...variables];
      const variable = newVariables[currentVariableIndex];
      variable.options = options;
      onChange(newVariables);

      message.success(`成功导入 ${options.length} 个选项`);
      setImportModalVisible(false);
      setImportJsonText('');
    } catch (error) {
      message.error('JSON 格式错误，请检查数据格式');
    }
  };

  return (
    <>
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={5} style={{ margin: 0 }}>变量配置</Title>
            <Tag color="blue">{variables.length} 个变量</Tag>
          </div>
        }
        size="small"
        style={{ height: '100%', overflow: 'auto' }}
      >
        <div style={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
          {variables.map((variable, index) => (
            <Card
              key={variable.id}
              size="small"
              style={{ marginBottom: 8 }}
            >
              <Space direction="vertical" style={{ width: '100%' }} size="small">
                <div>
                  <Text strong style={{ color: '#1890ff' }}>${variable.name}</Text>
                </div>

                <div>
                  <label style={{ fontSize: '12px', color: '#666' }}>显示名:</label>
                  <Input
                    placeholder="如: 用户ID"
                    value={variable.displayName}
                    onChange={(e) => updateVariable(index, 'displayName', e.target.value)}
                    size="small"
                  />
                </div>

                <div>
                  <label style={{ fontSize: '12px', color: '#666' }}>类型:</label>
                  <Select
                    value={variable.type}
                    onChange={(value) => updateVariable(index, 'type', value)}
                    size="small"
                    style={{ width: '100%' }}
                  >
                    <Option value="input">文本输入</Option>
                    <Option value="number">数字</Option>
                    <Option value="boolean">布尔值</Option>
                    <Option value="select">下拉选择</Option>
                    <Option value="radio">单选按钮</Option>
                    <Option value="textarea">多行文本</Option>
                  </Select>
                </div>

                {/* 选项配置 - 仅对 select 和 radio 类型显示 */}
                {(variable.type === 'select' || variable.type === 'radio') && (
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                      <label style={{ fontSize: '12px', color: '#666' }}>选项配置:</label>
                      <Space size="small">
                        <Button
                          type="dashed"
                          size="small"
                          icon={<ImportOutlined />}
                          onClick={() => openImportModal(index)}
                        >
                          JSON导入
                        </Button>
                        <Button
                          type="dashed"
                          size="small"
                          icon={<PlusOutlined />}
                          onClick={() => addOption(index)}
                        >
                          添加选项
                        </Button>
                      </Space>
                    </div>
                    {variable.options?.map((option, optionIndex) => (
                      <div key={optionIndex} style={{ display: 'flex', gap: '4px', marginBottom: '4px' }}>
                        <Input
                          placeholder="显示文本"
                          value={option.label}
                          onChange={(e) => updateOption(index, optionIndex, 'label', e.target.value)}
                          size="small"
                          style={{ flex: 1 }}
                        />
                        <Input
                          placeholder="值"
                          value={option.value}
                          onChange={(e) => updateOption(index, optionIndex, 'value', e.target.value)}
                          size="small"
                          style={{ flex: 1 }}
                        />
                        <Button
                          type="text"
                          danger
                          size="small"
                          icon={<DeleteOutlined />}
                          onClick={() => removeOption(index, optionIndex)}
                        />
                      </div>
                    ))}
                  </div>
                )}

                <div>
                  <label style={{ fontSize: '12px', color: '#666' }}>默认值:</label>
                  {variable.type === 'boolean' ? (
                    <Select
                      value={variable.defaultValue}
                      onChange={(value) => updateVariable(index, 'defaultValue', value)}
                      size="small"
                      style={{ width: '100%' }}
                    >
                      <Option value={true}>是</Option>
                      <Option value={false}>否</Option>
                    </Select>
                  ) : variable.type === 'select' || variable.type === 'radio' ? (
                    <Select
                      value={variable.defaultValue}
                      onChange={(value) => updateVariable(index, 'defaultValue', value)}
                      size="small"
                      style={{ width: '100%' }}
                      placeholder="选择默认值"
                    >
                      {variable.options?.map((option) => (
                        <Option key={option.value} value={option.value}>
                          {option.label}
                        </Option>
                      ))}
                    </Select>
                  ) : variable.type === 'textarea' ? (
                    <TextArea
                      placeholder="默认值"
                      value={variable.defaultValue}
                      onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
                      size="small"
                      rows={2}
                    />
                  ) : (
                    <Input
                      placeholder="默认值"
                      value={variable.defaultValue}
                      onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
                      size="small"
                      type={variable.type === 'number' ? 'number' : 'text'}
                    />
                  )}
                </div>

                {/* 必填选项配置 */}
                <div>
                  <Checkbox
                    checked={variable.required || false}
                    onChange={(e) => updateVariable(index, 'required', e.target.checked)}
                  >
                    <Text style={{ fontSize: '12px' }}>
                      必填字段
                      <Text type="secondary" style={{ fontSize: '11px', marginLeft: '8px' }}>
                        (勾选后在执行时必须填写)
                      </Text>
                    </Text>
                  </Checkbox>
                </div>
              </Space>
            </Card>
          ))}

          {variables.length === 0 && (
            <div style={{
              textAlign: 'center',
              color: '#999',
              padding: '20px',
              border: '1px dashed #d9d9d9',
              borderRadius: '4px'
            }}>
              在脚本中使用 $变量名 格式，系统会自动识别并生成变量配置
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                例如: $userId, $userName
              </Text>
            </div>
          )}
        </div>
      </Card>

      {/* JSON 导入模态框 */}
      <Modal
        title="JSON 数据导入"
        open={importModalVisible}
        onOk={handleImportJson}
        onCancel={() => setImportModalVisible(false)}
        width={600}
        okText="导入"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            支持以下 JSON 格式：
          </Text>
          <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
            <Text code style={{ fontSize: '12px' }}>
              {`// 数组格式
["选项1", "选项2", "选项3"]

// 对象数组格式
[
  {"label": "显示名1", "value": "value1"},
  {"name": "显示名2", "id": "value2"}
]

// 键值对格式
{
  "key1": "显示名1",
  "key2": "显示名2"
}`}
            </Text>
          </div>
        </div>
        <TextArea
          placeholder="请粘贴 JSON 数据..."
          value={importJsonText}
          onChange={(e) => setImportJsonText(e.target.value)}
          rows={10}
          style={{ fontFamily: 'monospace' }}
        />
      </Modal>
    </>
  );
};

export default VariableEditor;
