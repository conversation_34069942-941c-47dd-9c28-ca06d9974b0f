import React, { useState, useEffect, useRef } from 'react';
import { Button, message, Typography, Space, Card, Alert, Spin } from 'antd';
import { SaveOutlined, BugOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import Editor from '@monaco-editor/react';
import VariableEditor from './VariableEditor';
import ExtraOutputEditor from './ExtraOutputEditor';
import <PERSON><PERSON>Viewer from '@/components/JsonViewer';
import { ScriptDebug, scriptGet, scriptUpdate } from '@/common/API/ScriptAPI';
import type { Variable, ScriptData, ExecuteResult, ExtraOutput } from '@/types/script';

const { Title } = Typography;

const ScriptEditor: React.FC = () => {
  const navigate = useNavigate();
  const editorRef = useRef<any>(null);

  const [variables, setVariables] = useState<Variable[]>([]);
  const [extraOutputs, setExtraOutputs] = useState<ExtraOutput[]>([]);
  const [saving, setSaving] = useState(false);
  const [debugging, setDebugging] = useState(false);
  const [debugResult, setDebugResult] = useState<ExecuteResult | null>(null);
  const [scriptKsn, setScriptKsn] = useState<string>('');
  const [currentScript, setCurrentScript] = useState<any>(null);

  const defaultScriptContent = `// Groovy脚本示例
  // 使用 $变量名 来引用变量，如 $userId, $userName
  
  def processUser() {
      println "处理用户: \${userId}"
      println "用户名: \${userName}"
      // 你的业务逻辑
      def result = [
          userId: userId,
          userName: userName,
          processTime: new Date(),
          status: 'success',
          // 额外输出示例数据
          liveRoomLink: "kwai://live/play/~" + userId,
          avatarUrl: "https://example.com/avatar/" + userId + ".jpg",
          userProfile: [
              email: userName + "@example.com",
              phone: "138****" + userId.toString().substring(0,4),
              level: "VIP"
          ]
      ]
  
      return result
  }
  
  // 执行主逻辑
  return processUser()`;

  const [scriptContent, setScriptContent] = useState<string | undefined>('');

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const scriptId = urlParams.get('scriptId');
    
    if (scriptId) {
      const loadScript = async () => {
        try {
          const script = await scriptGet({ id: scriptId });
          if (script) {
            setScriptKsn(script.ksn || '');
            setCurrentScript(script);
            if (script.content) {
              setScriptContent(script.content);
            }
            if (script.variables && Array.isArray(script.variables)) {
              setVariables(script.variables);
            } else {
              extractVariablesFromScript(script.content || '');
            }
            if (script.extraOutput && Array.isArray(script.extraOutput)) {
              setExtraOutputs(script.extraOutput);
            }
          }
        } catch (error) {
          console.error('加载脚本失败:', error);
          message.error('加载脚本失败');
        }
      };
      
      loadScript();
    }
  }, []);

  const extractVariablesFromScript = (content: string) => {
    if (!content) return;

    const variableRegex = /\$([a-zA-Z_][a-zA-Z0-9_]*)/g;
    const matches = content.match(variableRegex);

    const currentVariableNames = matches ? [...new Set(matches.map(match => match.substring(1)))] : [];

    if (currentVariableNames.length === 0) {
      setVariables([]);
      return;
    }

    const newVariables: Variable[] = [];

    currentVariableNames.forEach(name => {
      const existingVar = variables.find(v => v.name === name);
      if (existingVar) {
        newVariables.push(existingVar);
      } else {
        const displayName = name
          ?.replace(/([A-Z])/g, ' $1')
          ?.replace(/^./, str => str.toUpperCase())
          ?.trim();

        newVariables.push({
          id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name,
          displayName,
          type: 'input',
          defaultValue: ''
        });
      }
    });

    setVariables(newVariables);
  };

  const registerGroovyLanguage = (monaco: any) => {
    monaco.languages.register({ id: 'groovy' });

    monaco.languages.setMonarchTokensProvider('groovy', {
      tokenizer: {
        root: [
          [/\$[a-zA-Z_][a-zA-Z0-9_]*/, 'variable'],
          [/"([^"\\]|\\.)*$/, 'string.invalid'],
          [/"/, 'string', '@string_double'],
          [/'([^'\\]|\\.)*$/, 'string.invalid'],
          [/'/, 'string', '@string_single'],
          [/"""/, 'string', '@string_multiline'],
          [/\/\*/, 'comment', '@comment'],
          [/\/\/.*$/, 'comment'],
          [/\d*\.\d+([eE][\-+]?\d+)?[fFdD]?/, 'number.float'],
          [/0[xX][0-9a-fA-F]+[Ll]?/, 'number.hex'],
          [/0[0-7]+[Ll]?/, 'number.octal'],
          [/\d+[lL]?/, 'number'],
          [/\b(abstract|as|assert|boolean|break|byte|case|catch|char|class|const|continue|def|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|in|instanceof|int|interface|long|native|new|package|private|protected|public|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|transient|try|void|volatile|while)\b/, 'keyword'],
          [/[=><!~?:&|+\-*\/\^%]+/, 'operator'],
          [/[;,.]/, 'delimiter'],
          [/[{}()\[\]]/, 'bracket'],
          [/[a-zA-Z_$][\w$]*/, 'identifier'],
        ],

        string_double: [
          [/[^\\"]+/, 'string'],
          [/\\./, 'string.escape'],
          [/"/, 'string', '@pop']
        ],

        string_single: [
          [/[^\\']+/, 'string'],
          [/\\./, 'string.escape'],
          [/'/, 'string', '@pop']
        ],

        string_multiline: [
          [/[^"]+/, 'string'],
          [/"""/, 'string', '@pop'],
          [/"/, 'string']
        ],

        comment: [
          [/[^\/*]+/, 'comment'],
          [/\*\//, 'comment', '@pop'],
          [/[\/*]/, 'comment']
        ]
      }
    });
  };

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    registerGroovyLanguage(monaco);

    monaco.editor.defineTheme('groovy-rich-theme', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'variable', foreground: '#FF6B6B', fontStyle: 'bold' },
        { token: 'string', foreground: '#4ECDC4' },
        { token: 'string.escape', foreground: '#96CEB4' },
        { token: 'string.invalid', foreground: '#FF7675' },
        { token: 'comment', foreground: '#6C7B7F', fontStyle: 'italic' },
        { token: 'keyword', foreground: '#45B7D1', fontStyle: 'bold' },
        { token: 'number', foreground: '#FFEAA7' },
        { token: 'number.float', foreground: '#FFEAA7' },
        { token: 'number.hex', foreground: '#FFEAA7' },
        { token: 'number.octal', foreground: '#FFEAA7' },
        { token: 'operator', foreground: '#96CEB4' },
        { token: 'delimiter', foreground: '#DDA0DD' },
        { token: 'bracket', foreground: '#DDA0DD' },
        { token: 'identifier', foreground: '#f8f8f2' },
      ],
      colors: {
        'editor.background': '#1a1a1a',
        'editor.foreground': '#f8f8f2',
        'editor.lineHighlightBackground': '#2d2d2d',
        'editor.selectionBackground': '#44475a',
        'editor.selectionHighlightBackground': '#424450',
        'editorCursor.foreground': '#f8f8f0',
        'editorWhitespace.foreground': '#3b3a32',
        'editorIndentGuide.background': '#3b3a32',
        'editorIndentGuide.activeBackground': '#9d550fb0',
        'editor.selectionHighlightBorder': '#222218'
      }
    });

    monaco.editor.setTheme('groovy-rich-theme');

    extractVariablesFromScript(scriptContent || '');
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      const urlParams = new URLSearchParams(window.location.search);
      const scriptId = urlParams.get('scriptId');

      if (!scriptId) {
        message.error('无法获取脚本ID');
        return;
      }

      const scriptData: ScriptData = {
        id: scriptId,
        content: scriptContent ?? defaultScriptContent,
        variables: variables,
        extraOutput: extraOutputs
      };

      await scriptUpdate(scriptData);

      message.success('脚本保存成功！');
      console.log('保存的脚本数据:', scriptData);

    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  const handleDebug = async () => {
    if (!scriptContent || !scriptContent.trim()) {
      message.warning('请先编写脚本内容');
      return;
    }
    const startTime = Date.now();

    try {
      setDebugging(true);
      setDebugResult(null);

      const variableValues = variables.reduce((acc, variable) => {
        let value = variable.defaultValue;

        switch (variable.type) {
          case 'number':
            value = value ? Number(value) : 0;
            break;
          case 'boolean':
            value = value === 'true' || value === true;
            break;
          case 'select':
          case 'radio':
            if (!value && variable.options && variable.options.length > 0) {
              value = variable.options[0].value;
            }
            break;
          default:
            value = value || '';
        }

        acc[variable.name] = value;
        return acc;
      }, {} as Record<string, any>);

      let processedScript = scriptContent;
      variables.forEach(variable => {
        const placeholder = '$' + variable.name;
        const value = variableValues[variable.name];
        
        let replacement: string;
        if (typeof value === 'string') {
          replacement = '"' + value.replace(/"/g, '\\"') + '"';
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          replacement = String(value);
        } else {
          replacement = '"' + String(value).replace(/"/g, '\\"') + '"';
        }
        if (replacement.includes('\n')) {
          replacement = replacement.replace(/\n/g, '\\n');
        }

        const regex = new RegExp('\\$' + variable.name + '\\b', 'g');
        processedScript = processedScript.replace(regex, replacement);
      });

      const debugParams = {
        script: processedScript,
        variables: variableValues,
        ksn: scriptKsn
      };

      console.log('调试参数:', debugParams);

      const apiResult = await ScriptDebug(debugParams);

      const executionTime = Date.now() - startTime;

      const debugResult: ExecuteResult = {
        success: apiResult.success !== false,
        result: apiResult.success !== false ? apiResult : undefined,
        error: apiResult.success === false ? (apiResult.error || apiResult.message || '调试执行失败') : undefined,
        executionTime
      };

      setDebugResult(debugResult);

      if (debugResult.success) {
        message.success('调试执行成功');
      } else {
        message.error('调试执行失败');
      }

    } catch (error) {
      console.error('调试执行错误:', error);
      const executionTime = Date.now() - startTime;

      setDebugResult({
        success: false,
        error: error instanceof Error ? error.message : '调试执行失败，请检查网络连接',
        executionTime
      });

      message.error('调试执行失败');
    } finally {
      setDebugging(false);
    }
  };

  const handlePreview = () => {
    if (!scriptContent || !scriptContent.trim()) {
      message.warning('请先编写脚本内容');
      return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const scriptId = urlParams.get('scriptId');
    
    if (scriptId) {
      window.open('/script-execute?scriptId=' + scriptId, '_blank');
    } else {
      message.warning('无法预览：缺少脚本ID');
    }
  };

  const handleScriptChange = (value: string | undefined) => {
    const newContent = value || '';
    setScriptContent(newContent);
    extractVariablesFromScript(newContent);
  };

  return (
    <div style={{ padding: '24px', display: 'flex', flex: 1, flexDirection: 'column' }}>
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>Groovy 脚本编辑器</Title>
          <Space>
            <Button
              type="default"
              icon={<SaveOutlined />}
              loading={saving}
              onClick={handleSave}
            >
              {saving ? '保存中...' : '保存'}
            </Button>
            <Button
              type="default"
              icon={<BugOutlined />}
              loading={debugging}
              onClick={handleDebug}
            >
              {debugging ? '调试中...' : '调试'}
            </Button>
            <Button
              type="primary"
              icon={<EyeOutlined />}
              onClick={handlePreview}
            >
              预览执行
            </Button>
          </Space>
        </div>
      </div>

      <div style={{ flex: 1, display: 'flex', gap: '16px' }}>
        <div style={{ flex: 2, overflow: 'auto', minHeight: 0 }}>
          <Card
            title="脚本编辑"
            style={{ height: 600, display: 'flex', flexDirection: 'column' }}
            styles={{ body: { flex: 1, padding: 0 } }}
          >
            <Editor
              height="100%"
              defaultLanguage="groovy"
              value={scriptContent}
              onChange={handleScriptChange}
              onMount={handleEditorDidMount}
              options={{
                minimap: { enabled: false },
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                automaticLayout: true,
                tabSize: 2,
                wordWrap: 'on',
                folding: true,
                lineDecorationsWidth: 10,
                lineNumbersMinChars: 3,
                renderLineHighlight: 'line',
                theme: 'vs-dark'
              }}
            />
          </Card>

          <Card
            title="调试结果"
            style={{
              height: 600,
              display: 'flex',
              flexDirection: 'column'
            }}
            styles={{ body: { flex: 1, padding: 0 } }}
          >
            {debugging && (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px', color: '#666' }}>
                  正在调试执行...
                </div>
              </div>
            )}

            {debugResult && !debugging && (
              <div style={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden'
              }}>
                {debugResult.success && debugResult.result && (
                  <div style={{ 
                    marginBottom: '16px',
                    flex: 1,
                    overflow: 'hidden'
                  }}>
                    {(() => {
                      try {
                        const jsonData = JSON.parse(debugResult.result?.resultJson);
                        return (
                          <div style={{
                            height: '100%',
                            overflow: 'hidden'
                          }}>
                            <JsonViewer data={jsonData} title="调试结果" />
                          </div>
                        );
                      } catch (error) {
                        return (
                          <div style={{
                            height: '100%',
                            overflow: 'auto'
                          }}>
                            <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>调试结果:</div>
                            <pre style={{
                              background: '#f5f5f5',
                              padding: '12px',
                              borderRadius: '4px',
                              border: '1px solid #d9d9d9',
                              fontSize: '12px',
                              overflow: 'auto',
                              height: 'calc(100% - 30px)',
                              whiteSpace: 'pre-wrap',
                              wordBreak: 'break-all',
                              margin: 0
                            }}>
                              {debugResult.result?.resultJson || JSON.stringify(debugResult.result, null, 2)}
                            </pre>
                          </div>
                        );
                      }
                    })()}
                  </div>
                )}

                <Alert
                  type={debugResult.success ? 'success' : 'error'}
                  message={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>{debugResult.success ? '调试成功' : '调试失败'}</span>
                      {debugResult.success && debugResult.executionTime && (
                        <span style={{ fontSize: '12px', opacity: 0.8 }}>
                          执行时间: {debugResult.executionTime}ms
                        </span>
                      )}
                    </div>
                  }
                  description={!debugResult.success ? debugResult.error : undefined}
                  style={{ flexShrink: 0 }}
                />
              </div>
            )}

            {!debugResult && !debugging && (
              <div style={{
                textAlign: 'center',
                color: '#999',
                padding: '40px',
                border: '1px dashed #d9d9d9',
                borderRadius: '4px'
              }}>
                点击"调试"按钮开始调试执行
              </div>
            )}
          </Card>
        </div>

        <div style={{ flex: 1, minWidth: '300px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div style={{ flex: 1 }}>
            <VariableEditor
              variables={variables}
              onChange={setVariables}
            />
          </div>

          <div style={{ flex: 1 }}>
            <ExtraOutputEditor
              extraOutputs={extraOutputs}
              onChange={setExtraOutputs}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScriptEditor;
