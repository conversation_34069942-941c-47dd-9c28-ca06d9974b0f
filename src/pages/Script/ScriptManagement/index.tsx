import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON>Complete, Button, Form, Input, Layout, message, Popconfirm, Space, Table} from 'antd';
import type {ColumnsType} from 'antd/es/table';
import {Content} from "antd/es/layout/layout";
import * as API from "@/common/API/ScriptAPI";
import EditingForm from "./EditingForm";

// 定义表格数据类型
export interface ToolScript {
  id: number;
  name: string;
  content: string;
  params: string;
  owner: string;
  scene: string;
  tags: string;
  ksn: string;
  desc: string;
  createTime: string;
  updateTime: string;
}

const ScriptManagement: React.FC = () => {
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);

  const [allScriptList, setAllScriptList] = useState<ToolScript[]>([]);
  const [scriptList, setScriptList] = useState<ToolScript[]>([]);
  const [editingRecord, setEditingRecord] = useState<ToolScript | null>(null);

  const [searchHistory, setSearchHistory] = useState<string[]>(() => {
    const stored = localStorage.getItem('scriptSearchHistory');
    return stored ? JSON.parse(stored) : [];
  });

  // 表格列定义
  const columns: ColumnsType<ToolScript> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 150,
    },
    { 
      title: '脚本名称', 
      dataIndex: 'name', 
      key: 'name',
      width: 150, 
    },
    { 
      title: '拥有者', 
      dataIndex: 'owner', 
      key: 'owner',
      width: 120,
    },
    { 
      title: '场景', 
      dataIndex: 'scene', 
      key: 'scene',
      width: 120,
    },
    { 
      title: 'KSN', 
      dataIndex: 'ksn', 
      key: 'ksn',
      width: 120,
    },
    { 
      title: '描述', 
      dataIndex: 'desc', 
      key: 'desc',
      width: 200,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      sorter: (a, b) => Number(a.createTime) - Number(b.createTime),
      sortDirections: ['ascend', 'descend'],
      render: ts => new Date(Number(ts)).toLocaleString()
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
      sorter: (a, b) => Number(a.updateTime) - Number(b.updateTime),
      sortDirections: ['ascend', 'descend'],
      render: ts => new Date(Number(ts)).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
          <Space size="middle">
            <a onClick={() => handleEdit(record)}>变更</a>
            <a onClick={() => handleDebug(record)}>调试</a>
            <a onClick={() => handleExecute(record)}>使用</a>
            <Popconfirm
                title="确定删除这个脚本吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="是"
                cancelText="否"
            >
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
          </Space>
      ),
    },
  ];

  const fetchData = useCallback(async (params: any = {}) => {
    try {
      const info = await API.scriptList(params);
      // 解析后端传过来的脚本列表
      const parsedList: ToolScript[] = info?.map?.((item: any) => ({
        ...item,
      }));
      setAllScriptList(parsedList);
      setScriptList(parsedList);
    } catch {
      message.error('获取脚本列表失败');
    }
  },[]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    localStorage.setItem('scriptSearchHistory', JSON.stringify(searchHistory));
  }, [searchHistory]);

  const handleNew = () => {
    setEditingRecord(null);
    setModalVisible(true);
  };

  const handleEdit = (record: ToolScript) => {
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await API.scriptDel({ id });
      message.success('删除成功');
      fetchData();
    } catch {
      message.error('删除失败');
    }
  };

  const handleDebug = (record: ToolScript) => {
    const url = `/script-editor?scriptId=${record.id}`;
    window.open(url, '_blank');
  };

  const handleExecute = (record: ToolScript) => {
    const url = `/script-execute?scriptId=${record.id}`;
    window.open(url, '_blank');
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  const handleSuccess = () => {
    setModalVisible(false);
    fetchData();
  };

  // 查询数据
  const handleSearch = (value: string) => {
    const v = value ?? '';
    const keyword = v?.trim()?.toLowerCase?.();
    if (keyword) {
      // 更新历史：去重、截断到 10 条
      setSearchHistory(prev => {
        const next = [keyword, ...prev.filter(item => item !== keyword)];
        return next.slice(0, 10);
      });
      // 过滤逻辑
      setScriptList(allScriptList.filter(item => {
        const name     = (item.name     ?? '').toLowerCase();
        const content  = (item.content  ?? '').toLowerCase();
        const params   = (item.params   ?? '').toLowerCase();
        const owner    = (item.owner    ?? '').toLowerCase();
        const scene    = (item.scene    ?? '').toLowerCase(); // 支持对 scene 字段的搜索
        const tags     = (item.tags     ?? '').toLowerCase();
        const ksn      = (item.ksn      ?? '').toLowerCase();
        const desc     = (item.desc     ?? '').toLowerCase();

        return (
            name.includes(keyword) ||
            content.includes(keyword) ||
            params.includes(keyword) ||
            owner.includes(keyword) ||
            scene.includes(keyword) || // 支持对 scene 字段的搜索
            tags.includes(keyword) ||
            ksn.includes(keyword) ||
            desc.includes(keyword)
        );
      }));
    } else {
      setScriptList(allScriptList);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
  };

  return (
      <Layout style={{padding: 24, background: '#fff'}}>
        <Content>
          <Form
              form={form}
              layout="inline"
              style={{ display: 'flex', justifyContent: 'center', marginBottom: 24 }}
          >
            {/* 搜索栏 */}
            <Form.Item name="search" style={{ width: 500, marginRight: 16 }}>
              <AutoComplete
                  options={searchHistory.map(h => ({ value: h }))}
                  onSelect={handleSearch}
              >
                <Input.Search
                    placeholder="请输入搜索内容: 脚本名称/内容/参数/拥有者/场景/标签/KSN/描述"
                    allowClear
                    onSearch={handleSearch}
                    onChange={e=>{
                      const v = e.target.value;
                      if(v==''){
                        handleSearch('');
                      }
                    }}
                />
              </AutoComplete>
            </Form.Item>
            {/* 新增按钮 */}
            <Form.Item>
              <Button type="primary" onClick={handleNew}>
                新增脚本
              </Button>
            </Form.Item>
          </Form>

          <Table<ToolScript>
              columns={columns}
              dataSource={scriptList}
              rowKey="id"
              scroll={{ x: 1200 }}
              pagination={{
                pageSize: 30,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => `共 ${total} 条，当前显示第 ${range?.[0]}-${range?.[1]} 条`,
              }}
              locale={{ emptyText: '暂无数据' }}
          />
          <EditingForm
              visible={modalVisible}
              onCancel={handleCancel}
              onSuccess={handleSuccess}
              initialValues={editingRecord || undefined}
          />
        </Content>
      </Layout>
  );
};

export default ScriptManagement;
