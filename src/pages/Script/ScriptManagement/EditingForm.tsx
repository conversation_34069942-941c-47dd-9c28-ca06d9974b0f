import React, {useEffect, useState} from 'react';
import {Form, Input, message, Modal} from 'antd';
import * as API from "@/common/API/ScriptAPI";
import type {ToolScript} from "./index";

interface EditingFormProps {
    visible: boolean;
    onCancel: () => void;
    onSuccess: () => void;
    initialValues?: Partial<ToolScript>;
}

const EditingForm: React.FC<EditingFormProps> = ({ visible, onCancel, onSuccess, initialValues }) => {
    const [form] = Form.useForm();
    const [submitting, setSubmitting] = useState(false);

    useEffect(() => {
        if (visible) {
            if (initialValues) {
                // —— 编辑：加载已有值
                form.setFieldsValue(initialValues);
            } else {
                // —— 新增：彻底清空表单
                form.resetFields();
            }
        }
    }, [visible, initialValues]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            setSubmitting(true);
            const payload = {
                ...values,
            };
            const response = initialValues?.id
                ? await API.scriptUpdate({ id: initialValues.id, ...payload })
                : await API.scriptAdd(payload);

            if (response.result === 1) {
                // 成功
                message.success(initialValues?.id ? '编辑成功' : '新增成功');
                form.resetFields();
                onSuccess();
            } else {
                // 失败
                console.error(response.error_msg);
                message.error(response.data || (initialValues?.id ? '编辑失败' : '新增失败'));
            }
        } catch (error) {
            console.error(error);
            message.error(initialValues?.id ? '编辑失败' : '新增失败');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Modal
            title={initialValues?.id ? '编辑脚本信息' : '新增脚本信息'}
            visible={visible}
            onCancel={onCancel}
            onOk={handleOk}
            confirmLoading={submitting}
            width={600}
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    name="name"
                    label="脚本名称"
                    rules={[{ required: true, message: '请输入脚本名称' }]}
                >
                    <Input placeholder="请输入脚本名称" />
                </Form.Item>

                <Form.Item
                    name="content"
                    label="脚本内容"
                    // rules={[{ required: true, message: '请输入脚本内容' }]}
                    hidden={true}
                >
                    <Input.TextArea
                        rows={6}
                        placeholder="请输入脚本内容"
                        showCount
                        maxLength={5000}
                    />
                </Form.Item>

                <Form.Item
                    name="desc"
                    label="描述"
                >
                    <Input.TextArea
                        rows={3}
                        placeholder="请输入脚本描述"
                        showCount
                        maxLength={500}
                    />
                </Form.Item>

                <Form.Item
                    name="params"
                    label="参数"
                    hidden={true}
                >
                    <Input.TextArea 
                        rows={3} 
                        placeholder="请输入参数（JSON格式）"
                    />
                </Form.Item>

                <Form.Item
                    name="owner"
                    label="拥有者"
                    // rules={[{ required: true, message: '请输入拥有者' }]}
                    hidden={true}
                >
                    <Input placeholder="请输入拥有者" />
                </Form.Item>

                <Form.Item
                    name="scene"
                    label="场景"
                >
                    <Input placeholder="请输入使用场景" />
                </Form.Item>

                <Form.Item
                    name="ksn"
                    label="KSN"
                >
                    <Input placeholder="请输入KSN" />
                </Form.Item>

                <Form.Item
                    name="tags"
                    label="标签"
                >
                    <Input placeholder="请输入标签，多个标签用逗号分隔" />
                </Form.Item>

            </Form>
        </Modal>
    );
};

export default EditingForm;
