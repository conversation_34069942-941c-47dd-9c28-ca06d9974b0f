import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Typography, Space, Alert, Spin, InputNumber, Switch, Select, Radio, message } from 'antd';
import { PlayCircleOutlined, ArrowLeftOutlined, CopyOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import type { Variable, ExecuteResult, ExtraOutput } from '@/types/script';
import <PERSON><PERSON><PERSON>ie<PERSON> from '@/components/JsonViewer';
import ExtraOutputRenderer from '@/components/ExtraOutputRenderer';
import { scriptGet, ScriptDebug } from '@/common/API/ScriptAPI';

const { Title, Text } = Typography;
const { TextArea } = Input;

const ScriptExecute: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const [scriptContent, setScriptContent] = useState<string>('');
  const [scriptName, setScriptName] = useState<string>('');
  const [scriptDesc, setScriptDesc] = useState<string>('');
  const [scriptKsn, setScriptKsn] = useState<string>('');
  const [variables, setVariables] = useState<Variable[]>([]);
  const [extraOutputs, setExtraOutputs] = useState<ExtraOutput[]>([]);
  const [executing, setExecuting] = useState(false);
  const [result, setResult] = useState<ExecuteResult | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadScript = async () => {
      try {
        setLoading(true);
        
        // 从URL参数中获取scriptId
        const urlParams = new URLSearchParams(window.location.search);
        const scriptId = urlParams.get('scriptId');
        
        if (!scriptId) {
          message.error('无法获取脚本ID');
          navigate(-1);
          return;
        }

        // 调用API获取脚本数据
        const script = await scriptGet({ id: scriptId });
        
        if (script) {
          setScriptContent(script.content || '');
          setVariables(script.variables || []);
          setExtraOutputs(script.extraOutput || []);
          setScriptName(script.name || '工具名称');
          setScriptDesc(script.desc || '');
          setScriptKsn(script.ksn || '');
          // 设置表单默认值 - 支持三种赋值方式
          const initialValues: Record<string, any> = {};
          let hasLocalStorageValues = false; // 标记是否有从localStorage获取的值
          
          script.variables?.forEach((variable: Variable) => {
            let value = variable.defaultValue; // 3. 默认值来自script.variables
            
            // 2. 尝试从localStorage获取值（按scriptId隔离）
            try {
              const localStorageKey = `script_form_${scriptId}`;
              const savedValues = localStorage.getItem(localStorageKey);
              if (savedValues) {
                const parsedValues = JSON.parse(savedValues);
                if (parsedValues[variable.name] !== undefined) {
                  value = parsedValues[variable.name];
                  hasLocalStorageValues = true;
                }
              }
            } catch (error) {
              console.warn('从localStorage读取表单值失败:', error);
            }
            
            // 1. 优先从URL参数获取值（优先级最高）
            const urlValue = urlParams.get(variable.name);
            if (urlValue !== null) {
              hasLocalStorageValues = false; // URL参数优先级更高，重置标记
              // 根据变量类型转换URL参数值
              switch (variable.type) {
                case 'number':
                  const numValue = Number(urlValue);
                  if (!isNaN(numValue)) {
                    value = numValue;
                  }
                  break;
                case 'boolean':
                  value = urlValue === 'true' || urlValue === '1';
                  break;
                default:
                  value = urlValue;
                  break;
              }
            }
            
            initialValues[variable.name] = value;
          });
          
          form.setFieldsValue(initialValues);
          
          // 如果表单值来自localStorage，尝试恢复对应的历史执行结果
          if (hasLocalStorageValues) {
            try {
              const resultKey = `script_result_${scriptId}`;
              const savedResult = localStorage.getItem(resultKey);
              if (savedResult) {
                const parsedResult = JSON.parse(savedResult);
                // 验证保存的结果对应的表单值是否与当前表单值匹配
                const savedFormValues = parsedResult.formValues;
                const currentFormValues = initialValues;
                
                // 简单比较表单值是否一致
                const isFormValuesMatch = JSON.stringify(savedFormValues) === JSON.stringify(currentFormValues);
                if (isFormValuesMatch && parsedResult.result) {
                  setResult(parsedResult.result);
                }
              }
            } catch (error) {
              console.warn('从localStorage读取执行结果失败:', error);
            }
          }
        } else {
          message.error('脚本不存在');
          navigate(-1);
        }
      } catch (error) {
        console.error('加载脚本失败:', error);
        message.error('加载脚本失败');
        navigate(-1);
      } finally {
        setLoading(false);
      }
    };

    loadScript();
  }, [form, navigate]);

  const executeScript = async () => {
    console.log('1. executeScript 开始执行');
    
    const startTime = Date.now(); // 将startTime定义移到try块之外
    
    try {
      console.log('2. 设置 executing 为 true');
      setExecuting(true);
      setResult(null);
      console.log('3. 开始表单验证');
      const formValues = await form.validateFields();
      console.log('4. 表单验证完成:', formValues);
      
      // 保存表单值到localStorage（按scriptId隔离）
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const scriptId = urlParams.get('scriptId');
        if (scriptId) {
          const localStorageKey = `script_form_${scriptId}`;
          localStorage.setItem(localStorageKey, JSON.stringify(formValues));
        }
      } catch (error) {
        console.warn('保存表单值到localStorage失败:', error);
      }
      
      // 替换脚本中的变量
      let finalScriptContent = scriptContent;
      variables.forEach((variable: Variable) => {
        // 修改正则表达式，匹配 $variableName 而不是 ${variableName}
        const regex = new RegExp(`\\$${variable.name}\\b`, 'g');
        const valueToReplace = formValues[variable.name];

        if (valueToReplace !== undefined) {
          let replacement = valueToReplace.toString();
          if (variable.type  === 'string') {
            // 字符串类型需要加引号
            replacement = `"${valueToReplace.replace(/"/g, '\\"')}"`;
          } else if (variable.type  === 'number' || variable.type  === 'boolean') {
            // 数字和布尔值直接转换
            replacement = String(valueToReplace);
          } else {
            // 其他类型转为字符串并加引号
            replacement = `"${String(valueToReplace).replace(/"/g, '\\"')}"`;
          }

          //如果换行，转移符号
          if (replacement.includes('\n')) {
            replacement = replacement.replace(/\n/g, '\\n');
          }
          finalScriptContent = finalScriptContent.replace(regex, replacement);
        }
      });
      console.log('5. 脚本变量替换完成:', finalScriptContent);

      // 调用API执行脚本
      console.log('6. 开始调用 ScriptDebug API');
      const apiResult = await ScriptDebug({ script: finalScriptContent, variables: formValues, ksn: scriptKsn });
      console.log('7. ScriptDebug API 调用完成:', apiResult);
      
      const executionTime = Date.now() - startTime; // 计算实际执行时间
      
      // 处理API返回结果，转换为ExecuteResult格式
      const executionResult: ExecuteResult = {
        success: apiResult.success !== false,
        result: apiResult.success !== false ? apiResult.resultJson : undefined,
        error: apiResult.success === false ? (apiResult.error || apiResult.message || '脚本执行失败') : undefined,
        executionTime
      };
      
      setResult(executionResult);
      console.log('8. 设置执行结果完成');

      // 保存执行结果到localStorage（按scriptId隔离）
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const scriptId = urlParams.get('scriptId');
        if (scriptId) {
          const resultKey = `script_result_${scriptId}`;
          const resultData = {
            result: executionResult,
            formValues: formValues,
            extraOutputs: apiResult.extraOutputs || [],
            timestamp: Date.now()
          };
          localStorage.setItem(resultKey, JSON.stringify(resultData));
        }
      } catch (error) {
        console.warn('保存执行结果到localStorage失败:', error);
      }

      if (executionResult.success) {
        message.success('脚本执行成功');
      } else {
        message.error('脚本执行失败');
      }
    } catch (error) {
      console.log('9. 捕获到错误:', error);
      const executionTime = Date.now() - startTime; // 现在可以访问startTime了
      
      setResult({
        success: false,
        error: error instanceof Error ? error.message : '脚本执行失败，请检查网络连接',
        executionTime
      });
      
      message.error('脚本执行失败');
    } finally {
      console.log('10. 设置 executing 为 false');
      setExecuting(false);
    }
  };

  const renderVariableInput = (variable: Variable) => {
    const commonProps = {
      placeholder: `请输入${variable.displayName}`,
      size: 'middle' as const
    };

    switch (variable.type) {
      case 'number':
        return <InputNumber {...commonProps} style={{ width: '100%' }} />;
      case 'boolean':
        return <Switch checkedChildren="是" unCheckedChildren="否" />;
      case 'select':
        return (
          <Select {...commonProps} style={{ width: '100%' }}>
            {variable.options?.map((option) => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        );
      case 'radio':
        return (
          <Radio.Group>
            {variable.options?.map((option) => (
              <Radio key={option.value} value={option.value}>
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        );
      case 'textarea':
        return <Input.TextArea {...commonProps} rows={3} />;
      case 'input':
      default:
        return <Input {...commonProps} />;
    }
  };

  return (
    <div style={{ padding: '24px', width: '100%', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px', textAlign: 'center' }}>
        <Title level={2}>{scriptName}</Title>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '100px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', color: '#666' }}>
            正在加载脚本...
          </div>
        </div>
      ) : (
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', gap: '24px' }}>
          {/* 参数输入 */}
          <Card title="执行参数" style={{ flex: '1', marginBottom: '24px' }}>
            {variables.length > 0 ? (
              <Form
                form={form}
                layout="vertical"
                onFinish={executeScript}
              >
                {variables.map(variable => (
                  <Form.Item
                    key={variable.id}
                    label={
                      <Space>
                        <Text strong>{variable.displayName}</Text>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          [{variable.name}]
                        </Text>
                        {variable.required && (
                          <Text type="danger" style={{ fontSize: '12px' }}>
                            *
                          </Text>
                        )}
                      </Space>
                    }
                    name={variable.name}
                    rules={
                      variable.required 
                        ? [{ required: true, message: `请输入${variable.displayName}` }]
                        : []
                    }
                  >
                    {renderVariableInput(variable)}
                  </Form.Item>
                ))}

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<PlayCircleOutlined />}
                    loading={executing}
                    size="large"
                    block
                  >
                    {executing ? '运行中...' : '运行'}
                  </Button>
                </Form.Item>
                {scriptDesc && (
                    <Text type="secondary" style={{ fontSize: '14px', display: 'block', marginTop: '8px' }}>
                      {scriptDesc}
                    </Text>
                )}
              </Form>
            ) : (
              <div style={{
                textAlign: 'center',
                color: '#999',
                padding: '40px',
                border: '1px dashed #d9d9d9',
                borderRadius: '4px'
              }}>
                该工具直接点击运行
                <br />
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={executeScript}
                  loading={executing}
                  style={{ marginTop: '16px' }}
                >
                  {executing ? '运行中...' : '运行'}
                </Button>
              </div>
            )}
          </Card>

          {/* 执行结果 */}
          <Card title="执行结果" style={{ flex: '3' }}>
            {executing && (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px', color: '#666' }}>
                  运行中，请稍候...
                </div>
              </div>
            )}

            {result && !executing && (
              <div>
                {/* 额外输出渲染 */}
                { (
                    (() => {
                      try {
                        const jsonData = JSON.parse(result.result);
                        return (
                            <ExtraOutputRenderer
                                extraOutputs={extraOutputs}
                                resultData={jsonData}
                            />
                        );
                      } catch (error) {
                        // 如果JSON解析失败，尝试直接使用result.result
                        return (
                            <ExtraOutputRenderer
                                extraOutputs={extraOutputs}
                                resultData={result.result}
                            />
                        );
                      }
                    })()
                )}

                {result.success && result.result && (
                  <div style={{ marginBottom: '16px' }}>
                    <Text strong style={{ fontSize: '16px', marginBottom: '12px', display: 'block' }}>
                      执行结果:
                    </Text>
                    {(() => {
                      try {
                        // 尝试解析JSON
                        const jsonData = JSON.parse(result.result);
                        return (
                          <div style={{
                            height: '100%',
                            overflow: 'hidden'
                          }}>
                            <JsonViewer data={jsonData}  theme="light" />
                          </div>
                        );
                      } catch (error) {
                        // JSON解析失败，直接展示原始结果
                        return (
                          <div style={{
                            height: '100%',
                            overflow: 'auto'
                          }}>
                            <pre style={{
                              background: '#f5f5f5',
                              padding: '12px',
                              borderRadius: '4px',
                              border: '1px solid #d9d9d9',
                              fontSize: '12px',
                              overflow: 'auto',
                              whiteSpace: 'pre-wrap',
                              wordBreak: 'break-all',
                              margin: 0
                            }}>
                              {result.result?.resultJson || JSON.stringify(result.result, null, 2)}
                            </pre>
                          </div>
                        );
                      }
                    })()}
                  </div>
                )}


                <Alert
                  type={result.success ? 'success' : 'error'}
                  message={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>{result.success ? '执行成功' : '执行失败'}</span>
                      {result.success && result.executionTime && (
                        <span style={{ fontSize: '12px', opacity: 0.8 }}>
                          执行时间: {result.executionTime}ms
                        </span>
                      )}
                    </div>
                  }
                  description={!result.success ? result.error : undefined}
                />
              </div>
            )}

            {!result && !executing && (
              <div style={{
                textAlign: 'center',
                color: '#999',
                padding: '40px',
                border: '1px dashed #d9d9d9',
                borderRadius: '4px'
              }}>
                点击"运行"按钮开始执行
              </div>
            )}
          </Card>
        </div>
      )}
    </div>
  );
};

export default ScriptExecute;
