import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Button, Card, Drawer, Dropdown, message, Slider, Space, TimePicker, Tooltip,} from 'antd';
import {BackwardOutlined, ForwardOutlined, InfoCircleOutlined} from "@ant-design/icons";
import * as API from "@/common/API/StreamLabelAPI";
import {PauseCircleOutlined, PlayCircleOutlined, ShoppingCartOutlined} from "@m-ui/icons";
import dayjs from "dayjs";
import type {KwaiPlayer} from "@ks-video/kwai-player-web/react";
import MatchedGoodsCard from "@/components/Cards/MatchedGoodsCard";
import type {HistoryItem} from "@/pages/AgentLiveV2/Tabs/HistoryRecord";
import {formatSecondsToHMS, timeStringToSeconds} from "@/pages/AgentLiveV2/constant";
import OnSaleItemTable from "@/components/Table/OnSaleItemTable";
import FrameListRowCard from "@/components/Cards/LiveFrameListRowCard";
import LiveSegmentCompareGraph from "@/pages/AgentLiveV3/Tabs/AgentLiveInfo/LiveSegmentCompareGraph";
import {Frame} from "@/interfaces/Frame";

interface AgentLiveInfoProps {
    liveStreamId: number;
    onSelectChange: (selectedKeys: string[]) => void;
    playerRef: React.RefObject<KwaiPlayer | null>;
    duration: number;
    playerCurrentTime: number;
    fetchInfo: (id: number | undefined, timestamp?: number) => Promise<void>;
    isLiving: boolean;
    isReplaying: boolean;
    onReplayingChange: (r: boolean) => void;
    timestamp?: number;
    // 展示信息
    currentItem: any;
    matchedGoodsAgent: any; // 这里可以替换成具体的类型
    matchedGoodsAlgo: any;  // 这里可以替换成具体的类型
    matchedGoodsReplay: any;  // 这里可以替换成具体的类型
    itemTimeRecords: HistoryItem[];
    startTime?: string;
    endTime?: string;
    llmAnswer?: any;
    llmModelName?: string;
    frameList: Frame[];
    windowRelatedTime: number;
    windowTimeStamp: number;
    // 新增：用于商品评价的参数
    evaluationLiveStreamId?: number;
    evaluationTimestamp?: number;
    // 新增：URL参数
    mmuItemId?: number;
    tmItemId?: number;
    taskId?: string;
    mode?: string;
    replayId?: number;
    replayModelVersion?: string;
    // agent
    agentRequest: any;
    agentResponse: any;
    // 直播片段比对信息
    segmentCompareInfo: any;
    replayWindowData: any;
    onWindowEntryClick: (id: number, timestamp: number, mmuItemId:number,tmItemId:number,segmentStartTime?:number,segmentEndTime?:number,replayId?:number) => void;

}

// 商品数据类型（表格
export interface GoodItem {
    serialNumber: number;
    itemId: number;
    title: string;
    imageUrl: string;
    imageUrls: string[];
    itemCategoryPropInfo: Record<string, string | undefined>;
    anchorRecordStartTime: number | null;
    anchorRecordEndTime: number | null;
}

const AgentLiveInfo: React.FC<AgentLiveInfoProps> = ({
                                                         liveStreamId,
                                                         playerCurrentTime,
                                                         onSelectChange,
                                                         playerRef,
                                                         duration,
                                                         fetchInfo,
                                                         isLiving,
                                                         isReplaying,
                                                         onReplayingChange,
                                                         matchedGoodsAgent,
                                                         matchedGoodsAlgo,
                                                         currentItem,
                                                         itemTimeRecords,
                                                         startTime,
                                                         endTime,
                                                         llmAnswer,
                                                         llmModelName,
                                                         frameList,
                                                         windowRelatedTime,
                                                         windowTimeStamp,
                                                         evaluationLiveStreamId,
                                                         evaluationTimestamp,
                                                         mmuItemId,
                                                         tmItemId,
                                                         taskId,
                                                         mode,
                                                         replayId, replayModelVersion,
                                                         agentRequest,
                                                         agentResponse,matchedGoodsReplay,
                                                         segmentCompareInfo,
                                                         replayWindowData,
                                                         onWindowEntryClick,
}) => {

    // 视频播放相关状态参数
    const [playbackRate, setPlaybackRate] = useState(1);
    const [sliderValue, setSliderValue] = useState(0);
    const initializedRef = useRef(false);
    const [jumpPicker, setJumpPicker] = useState(dayjs('00:00:00', 'HH:mm:ss'));  // 跳转时间选择器
    // 控制拖拽和播放
    const [isDragging, setIsDragging] = useState(false);

    // 原始数据（从API获取的更新数据 or  搜索过滤前的数据）
    const [originalDataList, setOriginalDataList] = useState<GoodItem[]>();
    // 展示数据（可能是原始数据 or 全量商品数据 or 搜索过滤后的数据）
    const [displayDataList, setDisplayDataList] = useState<GoodItem[]>();

    //商品快照捕获时间
    const [captureTime, setCaptureTime] = useState('');


    // 鼠标悬浮slider滑轨时显示值
    const [hoverSliderValue, setHoverSliderValue] = useState<number | null>(null);

    const intervalRef = useRef<NodeJS.Timer | null>(null)      // 用来存定时器 id
    const controlsDisabled = isLiving && !isReplaying; // 控制播放器按钮的禁用状态

    const [isPlaying, setIsPlaying] = useState(false);

    // 抽屉开关
    const [drawerOpen, setDrawerOpen] = useState(false);



    useEffect(() => {
        const videoEl = playerRef.current?.video;
        if (!videoEl) return;

        const onPlay = () => {
            setIsPlaying(true);
        };

        const onPause = () => {
            setIsPlaying(false);
        };

        // 绑定事件
        videoEl.addEventListener('play', onPlay);
        videoEl.addEventListener('pause', onPause);

        // 初始化同步一次状态
        if (!videoEl.paused) {
            onPlay();
        }

        return () => {
            videoEl.removeEventListener('play', onPlay);
            videoEl.removeEventListener('pause', onPause);
        };
    }, [playerRef, liveStreamId]);




    // 播放/暂停
    const handleTogglePlay = () => {
        const inst = playerRef.current as any
        if (!inst) return
        if (isPlaying) {
            inst.pause();
        } else {
            inst.play()
                .then(() => {
                    // 真正开始播放了
                    // setIsPlaying(true);
                })
                .catch(err => {
                    // 如果被浏览器拦截，就会走到这里
                    console.warn('播放被阻止', err);
                    // setIsPlaying(false);
                });
        }
    };

    // 快退 10s
    const handleRewind = () => {
        const newTime = Math.max(sliderValue - 10, 0);
        setSliderValue(newTime);
        if (playerRef.current) playerRef.current.currentTime = newTime;
    };

    // 快进 10s
    const handleForward = () => {
        if (duration === undefined) return; // 无效时不前进
        const maxTime = duration;
        const newTime = Math.min(sliderValue + 10, maxTime);
        setSliderValue(newTime);
        if (playerRef.current) playerRef.current.currentTime = newTime;
    };

    // 设置播放速度
    const speedMenuProps = {
        items: [0.5, 1, 1.5, 2, 2.5, 3].map(rate => ({
            key: rate.toString(),
            label: `${rate}×`,
        })),
        onClick: ({ key }: { key: string }) => {
            const rate = Number(key);
            setPlaybackRate(rate);
            if (playerRef.current) {
                playerRef.current.playbackRate = rate;
            }
        },
    };

    // 视频进度跳转，number为相对时间，相对视频开始xx秒
   const handleJump = (time: number) => {
        if (playerRef.current) {
            console.log("handleJump",time);
            playerRef.current.currentTime = time;
        }
    };


    // 当鼠标悬浮在 Slider 的轨道时，显示该位置对应的时间
    const handleSliderMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
        const { left, width } = e.currentTarget?.getBoundingClientRect?.();
        const percent = (e.clientX - left) / width;
        const clampedPercent = Math.max(0, Math.min(1, percent));
        const rawValue = duration * clampedPercent;
        const steppedValue = Math.round(rawValue);
        setHoverSliderValue(steppedValue);
    };
    const handleSliderMouseLeave = () => {
        setHoverSliderValue(null);
    };


    // 接口调用，捕获当前在车商品列表
    const updateDataList = async (currentTimeInSeconds: number): Promise<GoodItem[]> => {
        try {
            const params = {
                liveStreamId,
                specifiedTime: currentTimeInSeconds,
            }
            const result = await API.OnSaleItemUpdate(params);
            const updatedData = result.itemInfos;
            return updatedData;
        } catch (error) {
            console.error('API调用失败', error);
            message.error('获取数据失败，请重试');
            return [];
        }
    };

    useEffect(() => {
        if (!isDragging) {
            setSliderValue(playerCurrentTime);
        }
    }, [playerCurrentTime, isDragging]);

    // 只要滑块的值改变，就立即触发一次
    const handleSliderChange = (val: number) => {
        setIsDragging(true);
        setSliderValue(val);
    };

    // 只有当用户“松开”滑块（或者键盘操作结束）时才触发一次。
    const handleSliderChangeComplete = (val: number) => {
        setIsDragging(false);
        setSliderValue(val); // 最终值同步
        if (playerRef.current) {
            playerRef.current.currentTime = val;
        }
    };

    /** 进度条读取 - 组件挂载或 liveStreamId 变更时，从 storage 读取进度， */
    useEffect(() => {
        if (!liveStreamId) return;
        initializedRef.current = false;

        const key = `sliderValue-${liveStreamId}`;
        const saved = localStorage.getItem(key);
        const val = saved !== null ? Number(saved) : 0;
        setSliderValue(val);

        if (playerRef.current) {
            playerRef.current.currentTime = val;
        }
        // 标记“已完成第一次读取”
        initializedRef.current = true;
    }, [liveStreamId]);

    /** 进度条保存 -  任何时候 sliderValue 变化，都存一次 */
    useEffect(() => {
        if (!liveStreamId || !initializedRef.current) return;
        localStorage.setItem(`sliderValue-${liveStreamId}`, String(sliderValue));
    }, [sliderValue, liveStreamId]);

    // 捕获在车商品快照
    const handleCapture = async () => {
        setDrawerOpen(true)
        setCaptureTime(formatSecondsToHMS(sliderValue));
        try {
            const capturedDataList = await updateDataList(sliderValue);
            setOriginalDataList(capturedDataList);
            setDisplayDataList(capturedDataList);
        } catch (err) {
            message.error('获取在车商品失败');
        }
    };

    const getSliderTrackStyle = useCallback((): string => {
        if (!duration || duration <= 0) {
            return 'transparent';
        }
        const segments: string[] = [];
        const total = duration;
        let lastEnd = 0;
        let maxend = 0;
        const sortedHistory = [...itemTimeRecords]
            .sort((a, b) => timeStringToSeconds(a.startTime) - timeStringToSeconds(b.startTime));
        for (const item of sortedHistory) {
            const start = timeStringToSeconds(item.startTime);
            const end = timeStringToSeconds(item.endTime);
            if (sliderValue >= end) {
                maxend = end
                // 蓝色段（非标注区域）
                if (start > lastEnd) {
                    segments.push(`#1677ff ${(lastEnd / total) * 100}% ${(start / total) * 100}%`);
                }
                // 红色段（标注区域）
                segments.push(`#fd7f07  ${(start / total) * 100}% ${(end / total) * 100}%`);
                lastEnd = end;
            } else if (sliderValue < end)
                segments.push(`#1677ff ${(lastEnd / total) * 100}% ${(sliderValue / total) * 100}%`);
        }

        if (maxend == lastEnd) {
            segments.push(`#1677ff ${(lastEnd / total) * 100}% ${(sliderValue / total) * 100}%`);
        }
        if (lastEnd < total) {
            segments.push(`rgba(0,0,0,0.04) ${(sliderValue / total) * 100}% 100%`);
        }
        return `linear-gradient(to right, ${segments.join(', ')})`;
    }, [itemTimeRecords, duration, sliderValue]);

    const gridStyle: React.CSSProperties = {
        width: '100%',
        // flex: 1, // 自动平分父容器高度
        height: '40px',
        boxSizing: 'border-box',
        display: 'flex',
        alignItems: 'center',
        padding: '14px 12px',
        borderBottom: '1px solid #f0f0f0',
    };

    // 常量：分界线（秒）
    const BOUNDARY_SEC = 1753243286;

    // 工具：把一个时间戳统一成“秒”
    const toSeconds = (ts: number) => (ts > 1e11 ? Math.floor(ts / 1000) : ts);

    // 根据规则把 relatedTime 归一化成秒
    const normalizeRelatedTime = (relatedTime: number, absTs: number) => {
        const absSec = toSeconds(absTs);               // 绝对时间戳统一成秒
        const needMs2Sec = absSec > BOUNDARY_SEC;      // 大于分界线 -> relatedTime 是毫秒
        return needMs2Sec ? Math.floor(relatedTime / 1000) : relatedTime;
    };

    // 使用处
    const relSeconds = normalizeRelatedTime(windowRelatedTime, windowTimeStamp);

    return (
        <div style={{display: 'flex', flexDirection: 'column', padding: 0, width: '100%'}}>
            {/* —— 视频进度 —— */}
            <Card
                size="small"
                title={<span style={{fontSize: '14px', fontWeight: 'bold'}}>播放控制</span>}
                style={{marginBottom: 6, width: '100%'}}
            >
                {/* 视频进度卡片：进度控制部分*/}
                <div style={{display: 'flex', alignItems: 'center', padding: '8px 16px'}}>

                    <div style={{display: 'flex', justifyContent: 'space-between', marginRight: 10}}>
                        <span>{formatSecondsToHMS(sliderValue)} </span>
                        <span> / {formatSecondsToHMS(duration)}</span>
                    </div>

                    <Tooltip title="后退 10s">
                        <Button
                            onClick={handleRewind}
                            type="text"                      // 文字按钮，无边框
                            icon={<BackwardOutlined style={{fontSize: 26}}/>}     // 只渲染图标
                            style={{marginRight: 5}}
                            disabled={controlsDisabled}
                        />
                    </Tooltip>

                    <Tooltip title="播放/暂停">
                        <Button
                            onClick={handleTogglePlay}
                            icon={isPlaying ? <PauseCircleOutlined style={{fontSize: 26}}/> : <PlayCircleOutlined style={{fontSize: 26}}/>}
                            style={{marginRight: 5}}
                            type={"text"}
                            disabled={controlsDisabled}
                        />
                    </Tooltip>

                    <Tooltip title="前进 10s">
                        <Button
                            onClick={handleForward}
                            type="text"
                            icon={<ForwardOutlined style={{fontSize: 26}}/>}
                            disabled={controlsDisabled}
                        />
                    </Tooltip>

                    <Dropdown menu={speedMenuProps} trigger={['click']} disabled={controlsDisabled}>
                        <Button
                            type="text"
                            size="large"
                            style={{width: 60, height: 30, marginRight: 20, fontSize: 14}}
                        >
                            倍速 {playbackRate}×
                        </Button>
                    </Dropdown>

                    {/* 跳转 */}
                    <Space.Compact>
                        <TimePicker
                            value={jumpPicker}
                            onChange={(val) => val && setJumpPicker(val)}
                            showNow={false}
                            disabled={controlsDisabled}
                            style={{width: 110}}
                        />
                        <Button
                            onClick={() => {
                                if (jumpPicker) {
                                    handleJump(timeStringToSeconds(jumpPicker.format('HH:mm:ss')));
                                }
                            }}
                            disabled={controlsDisabled}
                        >
                            跳转
                        </Button>
                    </Space.Compact>

                    {/* 在车商品快照按钮+抽屉 */}
                    <Button onClick={handleCapture} style={{marginLeft: 'auto'}}>
                        <ShoppingCartOutlined/>在车商品快照
                        <Tooltip
                            title={
                                <div style={{fontSize: 12, width: 110}}>
                                    点击获取直播当前时刻小黄车商品列表
                                </div>
                            }
                        >
                            <InfoCircleOutlined style={{color: 'rgba(0,0,0,0.45)', marginLeft: 4}}/>
                        </Tooltip>
                    </Button>
                    <Drawer
                        title={
                            <span style={{fontWeight: 'bold'}}>
                                <span style={{color: '#1890ff'}}>{captureTime}</span> 时刻在车商品列表
                            </span>
                        }
                        width='1000px'
                        closable={{'aria-label': 'Close Button'}}
                        onClose={() => {
                            setDrawerOpen(false)
                        }}
                        open={drawerOpen}
                    >
                        <OnSaleItemTable
                            dataSource={displayDataList}
                            playerRef={playerRef}
                        >
                        </OnSaleItemTable>

                    </Drawer>
                </div>

                {/* 视频进度卡片：进度条部分 */}
                <div style={{padding: '0 6 6'}}>
                    <div
                        style={{
                            height: 30,
                            flex: 1,
                            margin: '0 20px 0 2px ',
                            position: 'relative',
                            overflow: 'visible'
                        }}
                        onMouseMove={handleSliderMouseMove}
                        onMouseLeave={handleSliderMouseLeave}

                    >
                        {hoverSliderValue !== null && (
                            <div
                                style={{
                                    position: 'absolute',
                                    top: -25,
                                    left: `${(hoverSliderValue / duration) * 100}%`,
                                    transform: 'translateX(-50%)',
                                    background: '#fff',
                                    padding: '2px 6px',
                                    borderRadius: 4,
                                    boxShadow: '0 0 4px rgba(0,0,0,0.15)',
                                    fontSize: 13,
                                    whiteSpace: 'nowrap',
                                    pointerEvents: 'none',
                                    zIndex: 1000,
                                }}
                            >
                                {formatSecondsToHMS(hoverSliderValue)}
                            </div>
                        )}
                        <Slider
                            min={0}
                            max={duration}
                            step={1}
                            value={sliderValue}
                            disabled={controlsDisabled}
                            onChange={controlsDisabled ? undefined : handleSliderChange}
                            onChangeComplete={controlsDisabled ? undefined : handleSliderChangeComplete}
                            // marks={marks}
                            tooltip={{
                                formatter: val => (val !== undefined ? formatSecondsToHMS(val) : ''),
                                getPopupContainer: () => document.body
                            }}
                            defaultValue={0}
                            style={{
                                marginTop: 4
                            }}
                            styles={{
                                handle: {
                                    display: 'none',
                                },

                                track: {
                                    background: 'transparent',
                                },
                                rail: {
                                    height: 20,
                                    marginTop: -4,
                                    background: getSliderTrackStyle(),
                                },
                            }}
                        />
                    </div>
                </div>
            </Card>
            <LiveSegmentCompareGraph segmentCompareInfo = {segmentCompareInfo} replayWindowData = {replayWindowData} onWindowEntryClick={onWindowEntryClick} />
            {/* —— 数据返回时间段 ——*/}
            <Card
                size="small"
                style={{marginBottom: 6, width: '100%', textAlign: 'center'}}
            >
                {startTime != null && endTime != null && (
                    <span
                        style={{
                            fontWeight: 'bold',
                            fontSize: 18,
                            whiteSpace: 'nowrap',      // 不换行
                            display: 'inline-block',   // 保证 whiteSpace 生效
                        }}
                    >
                    以下数据返回时间段：
                        {relSeconds > 0 ? (
                            <>
                                <Tooltip title="点击定位到时间段开始">
                                    <a onClick={() => handleJump(relSeconds - 40)}>
                                        {formatSecondsToHMS(relSeconds - 40)}
                                    </a>
                                </Tooltip>
                                {' - '}
                                <Tooltip title="点击定位到时间段结束">
                                    <a onClick={() => handleJump(relSeconds)}>
                                        {formatSecondsToHMS(relSeconds)}
                                    </a>
                                </Tooltip>
                            </>
                        ) : (
                            <span>-</span>
                        )}
                    </span>
                )}
            </Card>

            {/* —— 匹配商品卡片 —— */}
            <MatchedGoodsCard
                title={`${isLiving ? '实时' : '回放'} 商品检测`}
                currentItem={currentItem}
                matchedAlgo={matchedGoodsAlgo}
                matchedAgent={matchedGoodsAgent}
                matchedReplay={matchedGoodsReplay}
                llmAnswer={llmAnswer}
                llmModelName={llmModelName}
                liveStreamId={evaluationLiveStreamId ?? liveStreamId}
                timestamp={evaluationTimestamp ?? sliderValue}
                mmuItemId={mmuItemId}
                tmItemId={tmItemId}
                taskId={taskId}
                replayId={replayId}
                agentRequest={agentRequest}
                agentResponse={agentResponse}
                mode={mode}
                replayModelVersion = {replayModelVersion}
            />

            {/* —— 卡片并排展示：耗时、asr、抽帧 —— */}
            {mode !== 'label' && (
                <FrameListRowCard frameList={frameList ?? []} liveStreamId={liveStreamId}/>
            )}

        </div>
    );
};

export default AgentLiveInfo;
