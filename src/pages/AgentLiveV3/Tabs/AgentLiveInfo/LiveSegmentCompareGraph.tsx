import React, { useEffect, useRef, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import {Drawer, message, Table, Tag} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

// 表格数据类型定义
interface TableDataType {
  key: string;
  time: string;
  itemId: string;
  sequence: number;
  color: string;
  timestamp: number;
  score: number;
}

// 组件属性类型定义
interface LiveSegmentCompareGraphProps {
  segmentCompareInfo?: any;
  replayWindowData?: any;
  onWindowEntryClick?: (id: number, timestamp: number, mmuItemId:number,tmItemId:number) => void;
}

const LiveSegmentCompareGraph: React.FC<LiveSegmentCompareGraphProps> = ({
                                                                           segmentCompareInfo,
                                                                           replayWindowData,
  onWindowEntryClick
}) => {
  const chartRef = useRef<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [tableData, setTableData] = useState<TableDataType[]>([]);

  // 处理窗口大小变化和初始化后的尺寸调整
  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current) {
        const chartInstance = chartRef.current?.getEchartsInstance?.();
        if (chartInstance) {
          // 延迟调整大小，确保容器尺寸已更新
          setTimeout(() => {
            chartInstance.resize();
          }, 100);
        }
      }
    };

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);

    // 初始化后调整大小，解决初始渲染窄的问题
    const timer = setTimeout(() => {
      handleResize();
    }, 200);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timer);
    };
  }, []);

  // 全局颜色映射函数 - 确保甘特图和表格颜色一致
  const createGlobalColorMap = () => {
    const allItemIds = new Set();

    // 收集甘特图中的所有itemId
    let rawData = segmentCompareInfo ?? [];
    if (rawData && rawData.length > 0) {
      allItemIds.add(rawData[0]?.replayItemId); // 添加回放itemId
      rawData.forEach(item => allItemIds.add(item.labelItemId)); // 添加所有标注itemId
    }

    if(replayWindowData && replayWindowData.length > 0){
     // 收集回放窗口数据中的itemId
      replayWindowData.forEach(item => {
        if (item.scoreList && item.scoreList.length > 0) {
          allItemIds.add(item.scoreList[0].itemId.toString());
        }
      });
    }


    // 统一的颜色数组
    const colors = [
      '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
      '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f',
      '#87ceeb', '#32cd32', '#ffd700', '#ff6347', '#40e0d0'
    ];

    const itemIdColorMap = new Map();
    Array.from(allItemIds).forEach((itemId, index) => {
      itemIdColorMap.set(itemId, colors[index % colors.length]);
    });

    return itemIdColorMap;
  };

  // 处理表格数据 - 根据时间段过滤
  const processTableData = (startTime: number, endTime: number) => {
    const globalColorMap = createGlobalColorMap();
    // 过滤在时间段内的数据并转换格式
    const filteredData = replayWindowData
      .filter(item => {
        const timestamp = item.timestamp;
        return timestamp >= startTime && timestamp <= endTime;
      })
      .map((item, index) => ({
        key: `${index}`,
        time: dayjs(item.timestamp).format('YYYY-MM-DD HH:mm:ss'),
        itemId: item.scoreList[0]?.itemId?.toString() || '',
        sequence: index + 1,
        color: globalColorMap.get(item.scoreList[0]?.itemId?.toString()) || '#666',
        timestamp: item.timestamp,
        score: item.scoreList[0]?.fineTuneScore || 0
      }))
      .sort((a, b) => dayjs(b.time).valueOf() - dayjs(a.time).valueOf());

    return filteredData;
  };

  // 处理图表点击事件
  const handleChartClick = (params: any) => {
    // 获取点击的数据项
    const dataItem = params.data;
    if (dataItem && dataItem.length >= 3) {
      const startTime = dataItem[1]; // 开始时间
      const endTime = dataItem[2];   // 结束时间

      // 根据时间段过滤表格数据
      const data = processTableData(startTime, endTime);
      setTableData(data);
      setDrawerVisible(true);
    }
  };

  // 表格列定义
  const columns: ColumnsType<TableDataType> = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      sorter: (a, b) => dayjs(a.time).valueOf() - dayjs(b.time).valueOf(),
      defaultSortOrder: 'descend',
    },
    {
      title: '商品ID',
      dataIndex: 'itemId',
      key: 'itemId',
      render: (itemId: string, record: TableDataType) => (
        <Tag color={record.color}>{itemId}</Tag>
      ),
    },
    {
      title: '小黄车序号',
      dataIndex: 'sequence',
      key: 'sequence',
    },
  ];

  // 处理数据，转换为甘特图格式
  const processData = () => {
    if (!segmentCompareInfo) {
      return {};
    }
    const categories = ['算法识别'];

    segmentCompareInfo.forEach((item, index) => {
      categories.push(`人工标注${index + 1}`);
    });

    const series: any[] = [];

    // 使用全局颜色映射确保与表格颜色一致
    const itemIdColorMap = createGlobalColorMap();
    let rawData = segmentCompareInfo;

    // 回放片段数据（只有一个，所有行都是同一个回放片段）
    const replayStart = dayjs(rawData?.[0]?.replayStartTime);
    const replayEnd = dayjs(rawData?.[0]?.replayEndTime);
    const replayItemId = rawData?.[0]?.replayItemId;
    const replayColor = itemIdColorMap.get(replayItemId);

    series.push({
      name: '回放片段',
      type: 'custom',
      renderItem: (params: any, api: any) => {
        const categoryIndex = api.value(0);
        const start = api.coord([api.value(1), categoryIndex]);
        const end = api.coord([api.value(2), categoryIndex]);
        const height = api?.size([0, 1])?.[1] * 0.3; // 减少高度从0.6到0.3
        const itemId = api.value(3);
        const color = api.value(4);

        return {
          type: 'group',
          children: [
            {
              type: 'rect',
              shape: {
                x: start?.[0],
                y: start?.[1] - height / 2,
                width: end?.[0] - start?.[0],
                height: height
              },
              style: {
                fill: color,
                opacity: 0.8
              }
            },
            {
              type: 'text',
              style: {
                text: itemId,
                x: start?.[0] + (end?.[0] - start?.[0]) / 2,
                y: start?.[1],
                textAlign: 'center',
                textVerticalAlign: 'middle',
                fontSize: 12,
                fill: '#fff',
                fontWeight: 'bold'
              }
            }
          ]
        };
      },
      encode: {
        x: [1, 2],
        y: 0
      },
      data: [
        [0, replayStart.valueOf(), replayEnd.valueOf(), replayItemId, replayColor]
      ]
    });

    // 标注片段数据
    rawData.forEach((item, index) => {
      const annotationStart = dayjs(item.labelStartTime);
      const annotationEnd = dayjs(item.labelEndTime);
      const annotationItemId = item.labelItemId;
      const annotationColor = itemIdColorMap.get(annotationItemId);

      series.push({
        name: `标注片段${index + 1}`,
        type: 'custom',
        renderItem: (params: any, api: any) => {
          const categoryIndex = api.value(0);
          const start = api.coord([api.value(1), categoryIndex]);
          const end = api.coord([api.value(2), categoryIndex]);
          const height = api?.size([0, 1])?.[1] * 0.3; // 减少高度从0.6到0.3
          const itemId = api.value(3);
          const color = api.value(4);

          return {
            type: 'group',
            children: [
              {
                type: 'rect',
                shape: {
                  x: start?.[0],
                  y: start?.[1] - height / 2,
                  width: end?.[0] - start?.[0],
                  height: height
                },
                style: {
                  fill: color,
                  opacity: 0.8
                }
              },
              {
                type: 'text',
                style: {
                  text: itemId,
                  x: start?.[0] + (end?.[0] - start?.[0]) / 2,
                  y: start?.[1],
                  textAlign: 'center',
                  textVerticalAlign: 'middle',
                  fontSize: 11,
                  fill: '#fff',
                  fontWeight: 'bold'
                }
              }
            ]
          };
        },
        encode: {
          x: [1, 2],
          y: 0
        },
        data: [
          [index + 1, annotationStart.valueOf(), annotationEnd.valueOf(), annotationItemId, annotationColor]
        ]
      });
    });

    return { categories, series };
  };

  const { categories, series } = processData();

  const option = {
    title: {
      text: '算法识别与人工标注对比',
      left: 'center'
    },
    tooltip: {
      formatter: (params: any) => {
        const data = params.data;
        const startTime = dayjs(data?.[1])?.format?.('YYYY-MM-DD HH:mm:ss');
        const endTime = dayjs(data?.[2])?.format?.('YYYY-MM-DD HH:mm:ss');
        const duration = dayjs(data?.[2])?.diff?.(dayjs(data?.[1]), 'minute');
        return `${data?.[3]}<br/>开始时间: ${startTime}<br/>结束时间: ${endTime}<br/>持续时间: ${duration}分钟`;
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      top: '15%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      position: 'top',
      splitLine: {
        lineStyle: {
          color: ['#E9EDFF']
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        lineStyle: {
          color: '#929ABA'
        }
      },
      axisLabel: {
        color: '#929ABA',
        formatter: (value: number) => {
          return dayjs(value)?.format?.('HH:mm:ss');
        }
      }
    },
    yAxis: {
      type: 'category',
      data: categories,
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#E9EDFF'],
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#929ABA',
        margin: 8
      },
      boundaryGap: [0.1, 0.1] // 减少类别间的间距
    },
    series: series
  };

  return (
    <div style={{
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      // minHeight: '100vh'
    }}>
      <div style={{
        width: '100%',
        minWidth: '800px',
        height: '400px',
        border: '1px solid #e8e8e8',
        borderRadius: '4px',
      }}>
        <ReactECharts
          ref={chartRef}
          option={option}
          style={{ height: '100%', width: '100%' }}
          opts={{
            renderer: 'canvas',
            width: 'auto',
            height: 'auto'
          }}
          onChartReady={(chartInstance) => {
            // 图表准备就绪后立即调整大小
            setTimeout(() => {
              chartInstance.resize();
            }, 100);
          }}
          onEvents={{
            click: handleChartClick
          }}
        />
      </div>

      <Drawer
        title="窗口信息查看"
        placement="right"
        width={600}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        <Table
          columns={columns}
          dataSource={tableData}
          pagination={{
            pageSize: 200,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          size="small"
          onRow={(record: TableDataType) => ({
            onClick: () => {
              if (onWindowEntryClick) {
                onWindowEntryClick(13843398480,21901,24887780541859,24887780541859);
                setDrawerVisible(false);
                message.success('窗口详情切换');
              }
            },
            style: { cursor: 'pointer' }
          })}
        />
      </Drawer>
    </div>
  );
};

export default LiveSegmentCompareGraph;
