import React, { useCallback, useEffect, useRef, useState } from 'react';
import Flex from '@/components/Flex';
import { Typography, Button, Input } from '@m-ui/react';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import cls from 'classnames/bind';
import style from './index.module.less';
import VideoListTable from './Tabs/VideoListTable';
import { message } from 'antd';
import * as VideoListAPI from '@/common/API/VideoListAPI';
import * as VideoLabelAPI from '@/common/API/VideoLabelAPI';

const cx = cls.bind(style);

export interface VideoItem {
  id: number;
  userId: number;
  liveStreamId: number;
  videoCdnUri: string;
  sliceStartTime: number;
  sliceEndTime: number;
  bizType: number;
  sliceId: number;
  labelStatus: number; // 0: 未标注, 1: 已标注
  labelItemResult: string;
  labelUserName: string;
  createTime: number;
  updateTime: number;
  extend: any;
  labelType: number;
  labelTypeDescription?: string; // 标注集名称，需要从labelType映射
}

export interface FilterParams {
  labelUserName?: string;
  pageSize?: number;
  pageNo?: number;
}

export default function VideoList() {
  const [loading, setLoading] = useState(false);
  const [videoList, setVideoList] = useState<VideoItem[]>([]);
  const [total, setTotal] = useState(0);
  const [filterParams, setFilterParams] = useState<FilterParams>({
    pageSize: 20,
    pageNo: 1,
  });
  const [searchKeyword, setSearchKeyword] = useState('');

  // 获取视频列表
  const fetchVideoList = useCallback(async (params: FilterParams) => {
    setLoading(true);
    try {
      const result = await VideoLabelAPI.LabelListQuery(params);
      console.log('fetchVideoList result:', result);

      if (result.result === 1) {
        setVideoList(result.data?.labelList || []);
        setTotal(result.data?.totalCount || 0);
      } else {
        message.error(`获取视频列表失败: ${result.error_msg}`);
      }
    } catch (error) {
      console.error('获取视频列表异常:', error);
      message.error('获取视频列表异常');
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始加载
  useEffect(() => {
    fetchVideoList(filterParams);
  }, [fetchVideoList, filterParams]);

  // 处理筛选
  const handleFilter = useCallback(() => {
    setFilterParams((prev) => ({
      ...prev,
      labelUserName: searchKeyword || undefined,
      pageNo: 1, // 重置到第一页
    }));
  }, [searchKeyword]);

  // 处理分页
  const handlePageChange = useCallback((page: number, pageSize: number) => {
    setFilterParams((prev) => ({
      ...prev,
      pageNo: page,
      pageSize,
    }));
  }, []);

  // 处理刷新
  const handleRefresh = useCallback(() => {
    fetchVideoList(filterParams);
  }, [fetchVideoList, filterParams]);

  // 处理视频点击
  const handleVideoClick = useCallback((video: VideoItem) => {
    // 跳转到视频标注页面
    const params = new URLSearchParams({
      layoutType: '1',
      labelType: video.labelType?.toString() || '0',
      sliceId: video.sliceId?.toString() || '',
    });

    window.open(`/video-label?${params.toString()}`, '_blank');
  }, []);

  return (
    <Flex vertical gap={16} className={cx('video-list')}>
      <Flex
        className={cx('top')}
        gap={16}
        align="center"
        style={{ flexWrap: 'nowrap', paddingTop: '20px' }}
      >
        <Flex
          gap={8}
          align="center"
          style={{
            flex: 'none',
            float: 'left',
            paddingRight: '15px',
            paddingLeft: '20px',
            height: 30,
            lineHeight: '30px',
            fontSize: '18px',
          }}
        >
          <Typography.Text strong className={cx('title')}>
            视频列表
          </Typography.Text>
        </Flex>

        <Flex gap={8} style={{ marginLeft: 'auto' }}>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
            刷新
          </Button>
        </Flex>
      </Flex>

      <Flex
        className={cx('content')}
        gap={16}
        style={{ display: 'flex', flex: 1, width: '100%', paddingRight: '40px' }}
      >
        <Flex className={cx('main')} flex={1} style={{ minWidth: 0 }}>
          {/* 筛选区域 */}
          <div
            style={{
              marginBottom: 16,
              padding: '16px',
              backgroundColor: '#f8f8f8',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 16,
                flexWrap: 'nowrap',
                minWidth: 0,
                width: '100%',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 8,
                  flexShrink: 0,
                  minWidth: 0,
                }}
              >
                <span
                  style={{
                    fontSize: '14px',
                    color: '#666',
                    whiteSpace: 'nowrap',
                    flexShrink: 0,
                  }}
                >
                  标注人：
                </span>
                <Input
                  placeholder="请输入标注人"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  style={{
                    width: 200,
                    flexShrink: 0,
                    minWidth: 200,
                  }}
                  allowClear
                />
              </div>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleFilter}
                style={{
                  flexShrink: 0,
                  whiteSpace: 'nowrap',
                  minWidth: 'fit-content',
                }}
              >
                搜索
              </Button>
            </div>
          </div>

          {/* 表格区域 */}
          <VideoListTable
            dataSource={videoList}
            loading={loading}
            total={total}
            pagination={{
              current: filterParams.pageNo || 1,
              pageSize: filterParams.pageSize || 20,
              total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range?.[0]}-${range?.[1]} 条/共 ${total} 条`,
            }}
            onPageChange={handlePageChange}
            onVideoClick={handleVideoClick}
            onRefresh={handleRefresh}
          />
        </Flex>
      </Flex>
    </Flex>
  );
}
