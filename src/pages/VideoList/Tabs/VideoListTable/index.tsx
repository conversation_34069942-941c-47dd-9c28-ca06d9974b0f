import React, { useMemo } from 'react';
import { Table, Button, Tag, Tooltip, Modal, message } from '@m-ui/react';
import { DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from '@m-ui/react/lib/table';
import type { VideoItem } from '../../index';
import * as API from '@/common/API/VideoListAPI';

interface VideoListTableProps {
  dataSource: VideoItem[];
  loading: boolean;
  total: number;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger: boolean;
    showQuickJumper: boolean;
    showTotal: (total: number, range: [number, number]) => string;
  };
  onPageChange: (page: number, pageSize: number) => void;
  onVideoClick: (video: VideoItem) => void;
  onRefresh: () => void;
}

const VideoListTable: React.FC<VideoListTableProps> = ({
  dataSource,
  loading,
  total,
  pagination,
  onPageChange,
  onVideoClick,
  onRefresh,
}) => {
  // 标注状态映射
  const labelStatusMap = {
    0: { text: '未标注', color: 'orange' },
    1: { text: '已标注', color: 'green' },
  };

  const columns: ColumnsType<VideoItem> = useMemo(
    () => [
      {
        title: '标注集名称',
        dataIndex: 'labelTypeDescription',
        key: 'labelTypeDescription',
        width: 250,
        ellipsis: true,
        render: (labelTypeDescription: string) => labelTypeDescription || '未知类型',
      },
      {
        title: '切片ID',
        dataIndex: 'sliceId',
        key: 'sliceId',
        width: 250,
        ellipsis: true,
        render: (sliceId: number) => sliceId,
      },
      {
        title: '标注人',
        dataIndex: 'labelUserName',
        key: 'labelUserName',
        width: 250,
        ellipsis: true,
        render: (labelUserName: string) => labelUserName || '-',
      },
      {
        title: '标注状态',
        dataIndex: 'labelStatus',
        key: 'labelStatus',
        width: 250,
        ellipsis: true,
        render: (labelStatus: number) => (
          <Tag color={labelStatusMap[labelStatus as keyof typeof labelStatusMap]?.color}>
            {labelStatusMap[labelStatus as keyof typeof labelStatusMap]?.text}
          </Tag>
        ),
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        ellipsis: true,
        render: (_, record: VideoItem) => (
          <div style={{ display: 'flex', gap: 8 }}>
            <Button type="primary" size="small" onClick={() => onVideoClick(record)}>
              去标注
            </Button>
          </div>
        ),
      },
    ],
    [onVideoClick],
  );

  return (
    <div style={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ flex: 1, width: '100%' }}>
        <Table<VideoItem>
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            onChange: onPageChange,
          }}
          size="middle"
          style={{ width: '100%' }}
          className="no-wrap-table"
        />
      </div>
    </div>
  );
};

export default VideoListTable;
