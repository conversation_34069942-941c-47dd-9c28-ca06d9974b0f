import React from 'react';
import { Result } from '@m-ui/react';
import { UserDeleteOutlined } from '@m-ui/icons';
import styles from './index.module.less';

/**
 * 无权限页面组件
 * 当用户没有权限时显示
 */
const NoPermission: React.FC = () => {
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Result
          icon={<UserDeleteOutlined style={{ color: '#ff4d4f' }} />}
          title="无权限访问"
          subTitle="您当前没有访问权限，请联系管理员授权"
        />
        <div className={styles.contactInfo}>
          <div className={styles.contactTitle}>联系方式：</div>
          <div className={styles.contactItem}>Kim: @张振忠、@朱欢欢</div>
        </div>
      </div>
    </div>
  );
};

export default NoPermission;
