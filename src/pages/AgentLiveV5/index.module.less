.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.title {
  text-align: center;
  margin-bottom: 32px;
  color: #1890ff;
}

.imageCard {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  :global(.ant-card-head) {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    background: #1890ff;
    color: white;
    border-radius: 12px 12px 0 0;
  }

  :global(.ant-card-body) {
    padding: 20px;
  }
}

.inputCard {
  height: auto;
  min-height: 200px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  :global(.ant-card-head) {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    background: #1890ff;
    color: white;
    border-radius: 12px 12px 0 0;
  }

  :global(.ant-card-body) {
    padding: 16px;
  }
}

.inputPanel {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.inputLabel {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.inputField {
  width: 100%;
  height: 40px;
  font-size: 14px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #40a9ff;
  }
  
  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  
  :global(.ant-input) {
    border: none;
    box-shadow: none;
    font-size: 14px;
    
    &:focus {
      box-shadow: none;
    }
  }
}

.confirmButton {
  width: 100%;
  height: 40px;
  font-size: 14px;
  font-weight: bold;
  border-radius: 8px;
  background: #1890ff;
  border: none;
  
  &:hover {
    background: #40a9ff;
  }
}

.imageContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.imageWrapper {
  text-align: center;
  
  img {
    width: 100%;
    max-width: 300px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .imageLabel {
    display: block;
    margin-top: 8px;
    color: #666;
    font-size: 12px;
  }
}

.similarityCard {
  text-align: center;

  .similarityImage {
    width: 100%;
    max-width: 300px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .similarityInfo {
    margin-top: 16px;
    
    .infoItem {
      margin-top: 8px;
      
      .infoLabel {
        font-weight: bold;
        color: #333;
      }
      
      .infoValue {
        color: #666;
      }
    }
  }
}

.userInfo {
  margin-top: 32px;
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .userLabel {
    color: #666;
    font-size: 14px;
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.noDataContainer {
  text-align: center;
  padding: 50px;
  
  .noDataText {
    color: #999;
    font-size: 16px;
  }
}