import React, { useEffect, useState } from 'react';
import { Card, Col, Row, Spin, message, Typography, Select, Button } from 'antd';
import * as API from '@/common/API/AgentLiveV5API';
import cls from 'classnames/bind';
import styles from './index.module.less';

const { Title, Text } = Typography;
const cx = cls.bind(styles);

interface LiveInfoData {
    mainImage: string[];
    liveItemSimilarityList: Array<{
        itemId: number;
        fineTuneScore: number;
        imgUrl: string;
        missStreak: number;
    }>;
    screenSnapshot: string[];
    userId: number;
}

const AgentLiveV5: React.FC = () => {
    const [data, setData] = useState<LiveInfoData | null>(null);
    const [loading, setLoading] = useState(false);
    const [currentUserId, setCurrentUserId] = useState<string>('871240728'); // 当前显示的用户ID
    const [selectedUserId, setSelectedUserId] = useState<string>('');
    
    // 用户ID选项数据
    const userIdOptions = [
        '871240728',
        '3024146350',
        '157364375',
        '4517039687',
        '1285083058',
        '32537147',
        '720848651',
        '4533315493',
        '2317811456',
        '3036034184',
        '81386930',
        '705267765',
        '487711849',
        '120645635',
        '745218372',
        '1317163002',
        '1095204427',
        '2335803382',
        '982605476',
        '3634778836',
        '3089449472',
        '1164758184',
        '27818039',
        '1875157739',
        '1780476389',
        '108622829',
        '103894839',
        '792956717',
        '94677806',
        '880133460',
        '2587917485',
        '2587266986',
        '579523564',
        '377508132',
        '1112502312',
        '480188305',
        '460942663'
    ];
    
    // 添加useEffect初始化逻辑
    useEffect(() => {
        const fetchInitialData = async () => {
            try {
                const result = await API.LiveInfo({ userId: currentUserId });
                setData(result);
            } catch (error) {
                console.error('初始化数据获取失败:', error);
                message.error('初始化数据获取失败');
            }
        };
        
        fetchInitialData();
    }, []); // 空依赖数组，只在组件挂载时执行

    // 处理确定按钮点击
    const handleConfirm = async () => {
        if (!selectedUserId) {
            message.warning('请选择主播ID');
            return;
        }
        
        try {
            setLoading(true);
            const result = await API.LiveInfo({ userId: selectedUserId });
            setData(result);
            setCurrentUserId(selectedUserId); // 更新当前用户ID
            message.success('数据获取成功');
        } catch (error) {
            console.error('获取数据失败:', error);
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className={cx('container')}>
            <Title level={2} className={cx('title')}>
                智能直播图片展示
            </Title>
            
            <Row gutter={[24, 24]}>
                {/* 左侧输入模块 */}
                <Col span={6}>
                    <Card 
                        title="参数输入" 
                        bordered={false}
                        className={cx('inputCard')}
                    >
                        <div className={cx('inputPanel')}>
                            <Text className={cx('inputLabel')}>当前用户ID: {currentUserId}</Text>
                            <Text className={cx('inputLabel')}>请选择主播ID:</Text>
                            <Select
                                value={selectedUserId}
                                onChange={setSelectedUserId}
                                placeholder="请选择主播ID"
                                className={cx('inputField')}
                                size="large"
                                style={{ width: '100%' }}
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.value as string)?.toLowerCase().includes(input.toLowerCase())
                                }
                            >
                                {userIdOptions.map((userId) => (
                                    <Select.Option key={userId} value={userId}>
                                        {userId}
                                    </Select.Option>
                                ))}
                            </Select>
                            <Button 
                                type="primary" 
                                onClick={handleConfirm}
                                className={cx('confirmButton')}
                                loading={loading}
                                size="large"
                            >
                                确认
                            </Button>
                        </div>
                    </Card>
                </Col>
                
                {/* 第一列：screenSnapshot */}
                <Col span={6}>
                    <Card 
                        title="屏幕截图" 
                        bordered={false}
                        className={cx('imageCard')}
                    >
                        <div className={cx('imageContainer')}>
                            {data?.screenSnapshot?.map?.((url, index) => (
                                <div key={index} className={cx('imageWrapper')}>
                                    <img
                                        src={url}
                                        alt={`屏幕截图 ${index + 1}`}
                                        className={cx('image')}
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.style.display = 'none';
                                        }}
                                    />
                                    <Text className={cx('imageLabel')}>
                                        截图 {index + 1}
                                    </Text>
                                </div>
                            ))}
                        </div>
                    </Card>
                </Col>

                {/* 第二列：mainImage */}
                <Col span={6}>
                    <Card 
                        title="主图"
                        bordered={false}
                        className={cx('imageCard')}
                    >
                        <div className={cx('imageContainer')}>
                            {data?.mainImage?.map?.((url, index) => (
                                <div key={index} className={cx('imageWrapper')}>
                                    <img
                                        src={url}
                                        alt={`主要图片 ${index + 1}`}
                                        className={cx('image')}
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.style.display = 'none';
                                        }}
                                    />
                                    <Text className={cx('imageLabel')}>
                                        图片 {index + 1}
                                    </Text>
                                </div>
                            ))}
                        </div>
                    </Card>
                </Col>

                {/* 第三列：liveItemSimilarityList[0].imgUrl */}
                <Col span={6}>
                    <Card 
                        title="相似商品图片" 
                        bordered={false}
                        className={cx('imageCard')}
                    >
                        <div className={cx('similarityCard')}>
                            {data?.liveItemSimilarityList && data.liveItemSimilarityList.length > 0 && data?.liveItemSimilarityList?.[0]?.imgUrl ? (
                                <div>
                                    <img
                                        src={data?.liveItemSimilarityList?.[0]?.imgUrl}
                                        alt="相似商品"
                                        className={cx('similarityImage')}
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.style.display = 'none';
                                        }}
                                    />
                                    <div className={cx('similarityInfo')}>
                                        <div className={cx('infoItem')}>
                                            <Text className={cx('infoLabel')}>商品ID: </Text>
                                            <Text className={cx('infoValue')}>{data?.liveItemSimilarityList?.[0]?.itemId}</Text>
                                        </div>
                                        <div className={cx('infoItem')}>
                                            <Text className={cx('infoLabel')}>相似度评分: </Text>
                                            <Text className={cx('infoValue')}>{data?.liveItemSimilarityList?.[0]?.fineTuneScore?.toFixed?.(4)}</Text>
                                        </div>
                                        <div className={cx('infoItem')}>
                                            <Text className={cx('infoLabel')}>连续未命中次数: </Text>
                                            <Text className={cx('infoValue')}>{data?.liveItemSimilarityList?.[0]?.missStreak}</Text>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <Text type="secondary">暂无相似商品图片</Text>
                            )}
                        </div>
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default AgentLiveV5;