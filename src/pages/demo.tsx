import React, {useEffect, useRef} from 'react';
import ReactECharts from 'echarts-for-react';
import dayjs from 'dayjs';

// 原始数据
const rawData = [
  {
    replay_start_time: '2025-07-30 06:07:17',
    replay_end_time: '2025-07-30 06:33:07',
    replay_item_id: '23782195634347',
    label_start_time: '2025-07-30 06:10:34',
    label_end_time: '2025-07-30 06:15:19',
    label_item_id: '24910884729347'
  },
  {
    replay_start_time: '2025-07-30 06:07:17',
    replay_end_time: '2025-07-30 06:33:07',
    replay_item_id: '23782195634347',
    label_start_time: '2025-07-30 06:21:28',
    label_end_time: '2025-07-30 06:24:34',
    label_item_id: '24910930550347'
  },
  {
    replay_start_time: '2025-07-30 06:07:17',
    replay_end_time: '2025-07-30 06:33:07',
    replay_item_id: '23782195634347',
    label_start_time: '2025-07-30 06:28:26',
    label_end_time: '2025-07-30 06:30:51',
    label_item_id: '24542882836347'
  },
  {
    replay_start_time: '2025-07-30 06:07:17',
    replay_end_time: '2025-07-30 06:33:07',
    replay_item_id: '23782195634347',
    label_start_time: '2025-07-30 06:16:06',
    label_end_time: '2025-07-30 06:19:06',
    label_item_id: '23822330427347'
  },
  {
    replay_start_time: '2025-07-30 06:07:17',
    replay_end_time: '2025-07-30 06:33:07',
    replay_item_id: '23782195634347',
    label_start_time: '2025-07-30 06:30:58',
    label_end_time: '2025-07-30 06:34:58',
    label_item_id: '24472156479347'
  }
];

const Demo = () => {
  const chartRef = useRef<any>(null);

  // 处理窗口大小变化和初始化后的尺寸调整
  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current) {
        const chartInstance = chartRef.current.getEchartsInstance();
        if (chartInstance) {
          // 延迟调整大小，确保容器尺寸已更新
          setTimeout(() => {
            chartInstance.resize();
          }, 100);
        }
      }
    };

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);

    // 初始化后调整大小，解决初始渲染窄的问题
    const timer = setTimeout(() => {
      handleResize();
    }, 200);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timer);
    };
  }, []);

  // 处理数据，转换为甘特图格式
  const processData = () => {
    const categories = ['回放片段', '标注片段1', '标注片段2', '标注片段3', '标注片段4', '标注片段5'];
    const series: any[] = [];

    // 收集所有唯一的itemId并分配颜色
    const allItemIds = new Set();
    allItemIds.add(rawData[0].replay_item_id); // 添加回放itemId
    rawData.forEach(item => allItemIds.add(item.label_item_id)); // 添加所有标注itemId

    // 预定义颜色数组
    const colors = [
      '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
      '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f',
      '#87ceeb', '#32cd32', '#ffd700', '#ff6347', '#40e0d0'
    ];

    // 为每个itemId分配颜色
    const itemIdColorMap = new Map();
    Array.from(allItemIds).forEach((itemId, index) => {
      itemIdColorMap.set(itemId, colors[index % colors.length]);
    });

    // 回放片段数据（只有一个，所有行都是同一个回放片段）
    const replayStart = dayjs(rawData[0].replay_start_time);
    const replayEnd = dayjs(rawData[0].replay_end_time);
    const replayItemId = rawData[0].replay_item_id;
    const replayColor = itemIdColorMap.get(replayItemId);

    series.push({
      name: '回放片段',
      type: 'custom',
      renderItem: (params: any, api: any) => {
        const categoryIndex = api.value(0);
        const start = api.coord([api.value(1), categoryIndex]);
        const end = api.coord([api.value(2), categoryIndex]);
        const height = api.size([0, 1])[1] * 0.3; // 减少高度从0.6到0.3
        const itemId = api.value(3);
        const color = api.value(4);

        return {
          type: 'group',
          children: [
            {
              type: 'rect',
              shape: {
                x: start[0],
                y: start[1] - height / 2,
                width: end[0] - start[0],
                height: height
              },
              style: {
                fill: color,
                opacity: 0.8
              }
            },
            {
              type: 'text',
              style: {
                text: itemId,
                x: start[0] + (end[0] - start[0]) / 2,
                y: start[1],
                textAlign: 'center',
                textVerticalAlign: 'middle',
                fontSize: 12,
                fill: '#fff',
                fontWeight: 'bold'
              }
            }
          ]
        };
      },
      encode: {
        x: [1, 2],
        y: 0
      },
      data: [
        [0, replayStart.valueOf(), replayEnd.valueOf(), replayItemId, replayColor]
      ]
    });

    // 标注片段数据
    rawData.forEach((item, index) => {
      const annotationStart = dayjs(item.label_start_time);
      const annotationEnd = dayjs(item.label_end_time);
      const annotationItemId = item.label_item_id;
      const annotationColor = itemIdColorMap.get(annotationItemId);

      series.push({
        name: `标注片段${index + 1}`,
        type: 'custom',
        renderItem: (params: any, api: any) => {
          const categoryIndex = api.value(0);
          const start = api.coord([api.value(1), categoryIndex]);
          const end = api.coord([api.value(2), categoryIndex]);
          const height = api.size([0, 1])[1] * 0.3; // 减少高度从0.6到0.3
          const itemId = api.value(3);
          const color = api.value(4);

          return {
            type: 'group',
            children: [
              {
                type: 'rect',
                shape: {
                  x: start[0],
                  y: start[1] - height / 2,
                  width: end[0] - start[0],
                  height: height
                },
                style: {
                  fill: color,
                  opacity: 0.8
                }
              },
              {
                type: 'text',
                style: {
                  text: itemId,
                  x: start[0] + (end[0] - start[0]) / 2,
                  y: start[1],
                  textAlign: 'center',
                  textVerticalAlign: 'middle',
                  fontSize: 11,
                  fill: '#fff',
                  fontWeight: 'bold'
                }
              }
            ]
          };
        },
        encode: {
          x: [1, 2],
          y: 0
        },
        data: [
          [index + 1, annotationStart.valueOf(), annotationEnd.valueOf(), annotationItemId, annotationColor]
        ]
      });
    });

    return { categories, series };
  };

  const { categories, series } = processData();

  const option = {
    title: {
      text: '回放片段与标注片段甘特图',
      left: 'center'
    },
    tooltip: {
      formatter: (params: any) => {
        const data = params.data;
        const startTime = dayjs(data[1]).format('YYYY-MM-DD HH:mm:ss');
        const endTime = dayjs(data[2]).format('YYYY-MM-DD HH:mm:ss');
        const duration = dayjs(data[2]).diff(dayjs(data[1]), 'minute');
        return `${data[3]}<br/>开始时间: ${startTime}<br/>结束时间: ${endTime}<br/>持续时间: ${duration}分钟`;
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      top: '15%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      position: 'top',
      splitLine: {
        lineStyle: {
          color: ['#E9EDFF']
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        lineStyle: {
          color: '#929ABA'
        }
      },
      axisLabel: {
        color: '#929ABA',
        formatter: (value: number) => {
          return dayjs(value).format('HH:mm:ss');
        }
      }
    },
    yAxis: {
      type: 'category',
      data: categories,
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#E9EDFF'],
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#929ABA',
        margin: 8
      },
      boundaryGap: [0.1, 0.1] // 减少类别间的间距
    },
    series: series
  };

  return (
    <div style={{
      padding: '20px',
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      minHeight: '100vh'
    }}>
      <h1>回放片段与标注片段甘特图</h1>
      <div style={{
        width: '80%',
        minWidth: '800px',
        height: '400px',
        border: '1px solid #e8e8e8',
        borderRadius: '4px',
        padding: '10px'
      }}>
        <ReactECharts
          ref={chartRef}
          option={option}
          style={{ height: '100%', width: '100%' }}
          opts={{
            renderer: 'canvas',
            width: 'auto',
            height: 'auto'
          }}
          onChartReady={(chartInstance) => {
            // 图表准备就绪后立即调整大小
            setTimeout(() => {
              chartInstance.resize();
            }, 100);
          }}
        />
      </div>
    </div>
  );
};

export default Demo;
