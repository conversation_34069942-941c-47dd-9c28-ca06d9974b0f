import React, {useCallback, useEffect, useRef, useState} from 'react';
import Flex from '@/components/Flex';
import {Input, Tabs, Tooltip, Typography} from '@m-ui/react';
import StickyBox from 'react-sticky-box';
import {QuestionCircleOutlined} from '@m-ui/icons';
import type {KwaiPlayer} from '@ks-video/kwai-player-web/react';
import {KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import cls from 'classnames/bind';
import style from './index.module.less';
import {Avatar, Button, Card, message, Radio} from "antd";
import * as API from '@/common/API/AgentLiveV4API';
import type {GoodItem} from "@/pages/AgentLiveV4/Tabs/AgentLiveInfo";
import AgentLiveInfo from "@/pages/AgentLiveV4/Tabs/AgentLiveInfo";
import type {MatchedGood} from "@/components/Cards/MatchedGoodsCard";
import type {HistoryItem} from "@/pages/AgentLiveV4/Tabs/HistoryRecord";
import {formatSecondsToHMS, LIVESTREAM_ID_KEY} from "@/pages/AgentLiveV4/constant";
import {useNavigate, useSearchParams} from 'react-router-dom';
import type {Frame4} from "@/interfaces/Frame4";
import * as StreamLabelAPI from '@/common/API/StreamLabelAPI';
import Cookies from 'js-cookie';

const {Meta} = Card;
const cx = cls.bind(style);


export default function AgentLiveV4() {

    const playerRef = useRef<KwaiPlayer | null>(null);

    const storedLiveStreamId = localStorage.getItem(LIVESTREAM_ID_KEY) ?? '';

    const [searchValue, setSearchValue] = useState(storedLiveStreamId);
    const [liveStreamId, setLiveStreamId] = useState<number>(() => {
        return storedLiveStreamId ? Number(storedLiveStreamId) : 0;
    });
    const [duration, setDuration] = useState(0)
    const [currentTime, setCurrentTime] = useState(0)
    const [activeKey, setActiveKey] = useState<'current' | 'history'>('current');
    const [videoUrls, setVideoUrls] = useState<string[]>([]);
    const [srcIndex, setSrcIndex] = useState(0);
    const videoSrc = videoUrls[srcIndex] || '';

    // 商品列表和视频URL
    const [products, setProducts] = useState<GoodItem[]>([]);
    const [filteredProducts, setFilteredProducts] = useState<GoodItem[]>(products);

    // 历史记录
    const [itemTimeRecordsAlgo, setItemTimeRecordsAlgo] = useState<HistoryItem[]>([]);


    const [startTime, setStartTime] = useState<number>(0);
    const [endTime, setEndTime] = useState<number>(0);

    const timestampRef = useRef<number | null>(null);
    const [specifiedTimestamp, setSpecifiedTimestamp] = useState(0);

    const [isLiving, setIsLiving] = useState(false);
    // 新增：标记用户是否在“回看”模式
    const [isReplaying, setIsReplaying] = useState(false);

    const [searchParams] = useSearchParams();
    const navigate = useNavigate();

    const [windowTracInfoList, setWindowTraceInfoList] = useState<any>([])

    const [liveUtcInfo, setLiveUtcInfo] = useState<{
        startUtc?: number;
        playbackTotalMs?: number;
        totalUtcMs?: number;
    } | null>(null);

    // URL参数状态
    const [urlReplayId, setUrlReplayId] = useState<number | undefined>(undefined);

    // 是否在讲品的选择状态
    const [isTalkingAboutProduct, setIsTalkingAboutProduct] = useState<string>('');
    // 后端返回的是否在讲品状态
    const [isShowGood, setIsShowGood] = useState<number>(0);
    // 后端返回的标注状态
    const [labelStatus, setLabelStatus] = useState<number>(0);
    // 抽帧图片的选择状态
    const [frameSelections, setFrameSelections] = useState<Record<string, string>>({});
    // 保存原始的后端数据
    const [originalBackendData, setOriginalBackendData] = useState<any>(null);
    // 标注人信息
    const [labelUserInfo, setLabelUserInfo] = useState<any>(null);
    // 是否处于编辑模式
    const [isEditMode, setIsEditMode] = useState(false);

    // 新增：标注后的字段状态
    const [isShowGoodLabel, setIsShowGoodLabel] = useState<number>(0);
    const [isGoodFrameLabels, setIsGoodFrameLabels] = useState<Record<string, number>>({});

    //获取 UTC 基准数据
    useEffect(() => {
        const fetchUtcData = async () => {
            if (!liveStreamId) return;
            try {
                const utcResponse = await StreamLabelAPI.LabelUTCTime({liveStreamId});
                let parsedData = utcResponse.data;
                if (typeof parsedData === "string") {
                    parsedData = JSON.parse(parsedData);
                }
                setLiveUtcInfo(parsedData);
            } catch (err) {
                console.error('获取 UTC 数据失败：', err);
            }
        };
        fetchUtcData();
    }, [liveStreamId]);

    //播放秒数转为 UTC 时间字符串
    const secondsToUtcTime = (seconds: number) => {
        if (!liveUtcInfo) return '——';
        const {startUtc, playbackTotalMs, totalUtcMs} = liveUtcInfo;
        if (!startUtc || !playbackTotalMs || !totalUtcMs) return '--';

        // 计算 UTC 时间（核心逻辑：按比例映射本地时间到 UTC 时间）
        const ratio = seconds / (playbackTotalMs / 1000);
        const utcTime = new Date(startUtc + ratio * totalUtcMs);

        // 格式化 UTC 时间为 "YYYY-MM-DD HH:MM:SS"
        return `${utcTime.getUTCFullYear()}-${
            (utcTime.getUTCMonth() + 1).toString().padStart(2, '0')
        }-${
            utcTime.getUTCDate().toString().padStart(2, '0')
        } ${
            utcTime.getUTCHours().toString().padStart(2, '0')
        }:${
            utcTime.getUTCMinutes().toString().padStart(2, '0')
        }:${
            utcTime.getUTCSeconds().toString().padStart(2, '0')
        }`;
    };
    useEffect(() => {
        setFilteredProducts(products);
    }, [products]);


    // —— 当 liveStreamId 变化时，保存到 localStorage ——
    useEffect(() => {
        if (!liveStreamId) return;
        localStorage.setItem(LIVESTREAM_ID_KEY, String(liveStreamId));
    }, [liveStreamId]);


    /**
     * 带 replayId 参数的接口调用（用于 URL 参数场景）
     * @param id 直播流 ID
     * @param timestamp 可选的回放时间（单位：秒），仅回放模式下传入
     * @param replayId replayId 参数
     */
    const fetchInfo = useCallback(async (replayId?: number) => {
            try {
                const params: { replayId: number | null } = {
                    replayId: replayId || null
                };

                const liveReplayLabelRecord = await API.LiveInfo(params);

                // 解析labelResult字段中的JSON数据，增加错误处理
                let labelResult;
                try {
                    labelResult = JSON.parse(liveReplayLabelRecord?.labelResult || '[]');
                } catch (parseError) {
                    console.error('解析labelResult失败:', parseError);
                    message.error('数据格式错误，请重试');
                    return null;
                }
                const info = labelResult;

                // 优先使用clipVideoUrl（公网地址），如果不存在则使用videoUrl
                const videoUrl = info?.[0]?.clipVideoUrl || info?.[0]?.videoUrl;
                setVideoUrls(videoUrl ? [videoUrl] : []);
                setSrcIndex(0);

                setWindowTraceInfoList(info);

                // 设置后端返回的标注状态
                setLabelStatus(liveReplayLabelRecord?.labelStatus ?? 0);
                // 保存原始的后端数据
                setOriginalBackendData(liveReplayLabelRecord);
                
                // 设置标注后的字段状态
                setIsShowGoodLabel(info?.[0]?.isShowGoodLabel ?? info?.[0]?.isShowGood ?? 0);
                
                // 设置标注后的帧字段状态
                const frameLabels: Record<string, number> = {};
                if (info?.[0]?.frameTraceInfoList && info[0].frameTraceInfoList.length > 0) {
                    info[0].frameTraceInfoList.forEach((frame: any) => {
                        frame.frame2imageList.forEach((imageItem: any, itemIdx: number) => {
                            const key = `0-${frame.index}-${itemIdx}`;
                            frameLabels[key] = imageItem.isGoodFrameLabel ?? imageItem.frameIndex ?? 0;
                        });
                    });
                }
                setIsGoodFrameLabels(frameLabels);

                // 如果已标注，获取标注人信息
                if (liveReplayLabelRecord?.labelStatus !== 0 && replayId) {
                    fetchLabelUserInfo(replayId);
                } else {
                    setLabelUserInfo(null); // 未标注时清空标注人信息
                }

                return info;

            } catch (err) {
                message.error('接口调用失败');
                console.error(err);
                return null;
            }
        },
        [] // 保持空依赖数组，因为内部使用的都是setState函数
    );


    // 获取标注人信息的函数
    const fetchLabelUserInfo = useCallback(async (replayId: number) => {
        try {
            const response = await API.QueryLabelUserInfoForReplay({replayId: replayId});
            if (response.code === "0") {
                const labelUserInfo = JSON.parse(response.data);
                setLabelUserInfo(labelUserInfo);
            } else {
                console.error("获取标注人信息失败", response.error_msg);
            }
        } catch (error) {
            console.error("获取标注人信息失败", error);
        }
    }, []);

    const onTimeUpdate = useCallback((_: any) => {
        const inst = (playerRef.current as any)
        if (!inst) return
        let t: number;
        if (typeof inst.currentTime === 'number') {
            t = inst.currentTime;
        } else if (typeof inst.getCurrentTime === 'function') {
            t = inst.getCurrentTime();
        } else {
            t = 0;
        }
        setCurrentTime(t)
    }, [playerRef])

    const onLoadedMetadata = useCallback(() => {
        const inst = playerRef.current
        if (!inst) return
        const d = (inst as any)?.duration
        if (typeof d === 'number') setDuration(d)
        console.log('视频元数据加载 -- duration: ', d)
        // 如果有 timestamp 参数，跳转到对应时间并自动播放
        if (timestampRef.current !== null) {
            try {
                (inst as any).currentTime = timestampRef.current;
                if (isLiving) {
                    setIsReplaying(true)
                }
            } catch (e) {
                console.warn('设置跳转时间失败', e);
            }
        }
        // 确保视频自动播放
        try {
            (inst as any)?.play();
        } catch (e) {
            console.warn('自动播放失败，可能需要用户交互:', e);
        }
    }, [])


    const handleTabClick = (key: string) => {
        if (key === 'history') {
            // fetchHistory(); // 重新调用接口
        }
    };

    const handleTabChange = (key: string) => {
        setActiveKey(key as 'current' | 'history');
    };

    // 入口 - 进入页面时立马调用，根据url的参数拼接规则调用不同接口
    useEffect(() => {
        const urlLiveStreamId = searchParams.get("liveStreamId");
        const urlTimestamp = searchParams.get("timestamp");
        const urlReplayIdParam = searchParams.get("replayId");

        // 保存URL参数到状态
        const replayIdValue = urlReplayIdParam ? Number(urlReplayIdParam) : undefined;
        setUrlReplayId(replayIdValue);

        fetchInfo(replayIdValue);

    }, [searchParams]);

    // 进入编辑模式
    const handleEnterEditMode = useCallback(() => {
        setIsEditMode(true);
        // 根据标注后的字段设置当前的选择状态
        setIsTalkingAboutProduct(isShowGoodLabel === 1 ? 'yes' : 'no');

        // 根据标注后的字段设置frameSelections
        const newFrameSelections: Record<string, string> = {};
        Object.entries(isGoodFrameLabels).forEach(([key, value]) => {
            newFrameSelections[key] = value === 1 ? "yes" : "no";
        });
        setFrameSelections(newFrameSelections);
    }, [isShowGoodLabel, isGoodFrameLabels]);

    // 取消编辑模式
    const handleCancelEditMode = useCallback(() => {
        setIsEditMode(false);
        // 重置选择状态
        setIsTalkingAboutProduct('');
        setFrameSelections({});
    }, []);

    // 提交标注结果
    const handleSubmitLabelResult = useCallback(async () => {
        if (labelStatus !== 0 && !isEditMode) {
            message.warning('当前状态不是未标注状态，无法提交');
            return;
        }

        // 检查是否选择了"该片段是否在讲品"
        if (!isTalkingAboutProduct) {
            message.warning('请先选择"该片段是否在讲品"');
            return;
        }

        try {
            if (!originalBackendData) {
                message.error('原始数据不存在，无法提交');
                return;
            }

            // 从原始后端数据中获取labelResult
            const originalLabelResult = JSON.parse(originalBackendData.labelResult || '[]')[0];

            // 构建新的标注结果数据，保持原始数据的其他字段不变
            const labelResult = {
                ...originalLabelResult, // 保持原始数据的所有字段
                isShowGoodLabel: isTalkingAboutProduct === 'yes' ? 1 : 0, // 新增：标注后的字段
            };

            // 处理frameSelections，将用户选择映射到frameIndex和isGoodFrameLabel
            if (labelResult.frameTraceInfoList && labelResult.frameTraceInfoList.length > 0) {
                labelResult.frameTraceInfoList = labelResult.frameTraceInfoList.map((frame: any) => {
                    const frame2imageList = frame.frame2imageList.map((imageItem: any, itemIdx: number) => {
                        const selectionKey = `0-${frame.index}-${itemIdx}`;
                        const userSelection = frameSelections[selectionKey];

                        // 将用户选择映射到frameIndex: "yes" -> 1, "no" -> 0
                        const frameIndex = userSelection === "yes" ? 1 : 0;

                        return {
                            ...imageItem,
                            isGoodFrameLabel: frameIndex // 新增：标注后的字段
                        };
                    });

                    return {
                        ...frame,
                        frame2imageList: frame2imageList
                    };
                });
            }

            // 获取当前用户名
            const currentUserName = Cookies.get('userName') || Cookies.get('_did') || '未知用户';

            // 构建LiveReplayLabelRecord对象，保持原始数据的结构
            const liveReplayLabelRecord = {
                ...originalBackendData, // 保持原始LiveReplayLabelRecord的所有字段
                labelStatus: 1, // 标注完成，状态设为1
                labelResult: JSON.stringify([labelResult]), // 更新标注结果
                labelUserName: currentUserName, // 更新标记者为真实用户名
            };

            // 调用API提交标注结果
            console.log('提交数据:', liveReplayLabelRecord);
            const result = await API.submitLabelResult(liveReplayLabelRecord);
            console.log('API响应结果:', result);

            // 检查API响应是否成功
            if (result && result.code === '0') {
                message.success('标注结果提交成功');
                
                // 更新本地标注人信息，确保显示正确的标注人
                const currentUserName = Cookies.get('userName') || Cookies.get('_did') || '未知用户';
                setLabelUserInfo({
                    labelUserName: currentUserName,
                    labelTime: Date.now()
                });
                
                // 更新标注状态
                setLabelStatus(1);
                
                // 重新获取数据以更新状态
                await fetchInfo(urlReplayId);

                // 如果是编辑模式，退出编辑模式
                if (isEditMode) {
                    setIsEditMode(false);
                }
            } else {
                const errorMsg = result?.error_msg || result?.message || '未知错误';
                message.error(`提交标注结果失败: ${errorMsg}`);
                console.error('API返回错误:', result);
            }

        } catch (error) {
            message.error('提交标注结果失败');
            console.error('提交标注结果失败:', error);
        }
    }, [labelStatus, isTalkingAboutProduct, frameSelections, fetchInfo, originalBackendData, isEditMode, urlReplayId]);

    return (
        <Flex vertical gap={16} className={cx('video')}>
            <Flex className={cx('columns')} gap={16} style={{display: 'flex', flex: 1, width: '100%', paddingRight: "40px"}}>

                <Flex className={cx('left')} vertical gap={16} style={{flex: '0 0 420px'}}>
                    <StickyBox offsetTop={20} className={cx('sticky-box')}>

                        {/* 标题 + Tooltip */}
                        <Flex gap={8} align="center" style={{padding: '0 16px', height: 30, lineHeight: '30px', fontSize: '18px', marginBottom: 16}}>
                            <Typography.Text strong className={cx('title')}>
                                智能直播讲解
                            </Typography.Text>
                            <Tooltip title="输入 liveStreamId 搜索">
                                <QuestionCircleOutlined style={{color: '#999'}}/>
                            </Tooltip>
                            <Input
                                style={{width: '100%', maxWidth: 220, marginLeft: 10}}
                                placeholder='请输入直播间ID'
                                value={searchValue}
                                onChange={e => setSearchValue(e.target.value)}
                                onPressEnter={(e) => {
                                    const val = (e.target as HTMLInputElement).value;
                                    setSearchValue(val);
                                    const id = Number(val);
                                    setLiveStreamId(id);
                                    fetchInfo(undefined);
                                    navigate(`/agent-live?layoutType=1`, {replace: true});
                                }}
                            />
                        </Flex>


                        {/* 播放器 */}
                        <Flex vertical gap={8} className={cx('player')} style={{padding: '0 16px'}}>
                            <div style={{position: 'relative', width: '100%', height: '100%'}}>
                                <KwaiPlayerReact
                                    key={`${liveStreamId}-${srcIndex}`}
                                    style={{width: '100%', height: '100%'}}
                                    id="player"
                                    ref={playerRef}
                                    src={videoSrc}
                                    autoPlay={true}
                                    showProgress={false}
                                    controls={true}
                                    preload="auto"
                                    onError={() => {
                                        if (srcIndex + 1 < videoUrls.length) setSrcIndex(srcIndex + 1);
                                    }}
                                    onTimeUpdate={onTimeUpdate}
                                    onLoadedMetadata={onLoadedMetadata}
                                />
                                <div
                                    style={{
                                        position: 'absolute',
                                        bottom: 40, // 距离视频底部 40px（假设视频时间在底部 30px 位置，这里在其上方 10px）
                                        left: 10,   // 距离视频左侧 10px（与视频时间左侧对齐）
                                        background: 'rgba(0,0,0,0.6)',
                                        color: '#fff',
                                        padding: '3px 8px',
                                        borderRadius: 4,
                                        fontSize: 12,
                                        pointerEvents: 'none',
                                        zIndex: 10,
                                    }}
                                >
                                    {secondsToUtcTime(currentTime)}
                                </div>
                            </div>
                        </Flex>

                        {/* 该片段是否在讲品选项 */}
                        <div style={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            padding: "16px 0",
                            margin: "0 16px"
                        }}>
                            <div style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "16px"
                            }}>
                                <div style={{
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                    color: "#333"
                                }}>
                                    该片段是否在讲品
                                </div>
                                {labelStatus === 0 || isEditMode ? (
                                    // 未标注状态或编辑模式：显示可选择的选项
                                    <Radio.Group
                                        value={isTalkingAboutProduct}
                                        onChange={(e) => setIsTalkingAboutProduct(e.target.value)}
                                        style={{
                                            display: "flex",
                                            gap: "12px"
                                        }}
                                    >
                                        <Radio
                                            value="yes"
                                            style={{
                                                fontSize: "14px",
                                                fontWeight: "500",
                                                margin: 0,
                                                padding: 0
                                            }}
                                        >
                                            <div style={{
                                                backgroundColor: "#e6f7ff",
                                                border: "1px solid #91d5ff",
                                                borderRadius: "6px",
                                                padding: "8px 16px",
                                                color: "#1890ff",
                                                fontSize: "14px",
                                                fontWeight: "500",
                                                textAlign: "center",
                                                minWidth: "100px",
                                                cursor: "pointer",
                                                transition: "all 0.2s ease"
                                            }}>
                                                在讲品
                                            </div>
                                        </Radio>
                                        <Radio
                                            value="no"
                                            style={{
                                                fontSize: "14px",
                                                fontWeight: "500",
                                                margin: 0,
                                                padding: 0
                                            }}
                                        >
                                            <div style={{
                                                backgroundColor: "#f6ffed",
                                                border: "1px solid #b7eb8f",
                                                borderRadius: "6px",
                                                padding: "8px 16px",
                                                color: "#389e0d",
                                                fontSize: "14px",
                                                fontWeight: "500",
                                                textAlign: "center",
                                                minWidth: "100px",
                                                cursor: "pointer",
                                                transition: "all 0.2s ease"
                                            }}>
                                                不在讲品
                                            </div>
                                        </Radio>
                                    </Radio.Group>
                                ) : (
                                    // 已标注状态：显示标注结果和修改按钮
                                    <div style={{
                                        display: "flex",
                                        gap: "12px",
                                        alignItems: "center"
                                    }}>
                                        <div style={{
                                            backgroundColor: isShowGoodLabel === 1 ? "#e6f7ff" : "#f6ffed",
                                            border: `1px solid ${isShowGoodLabel === 1 ? "#91d5ff" : "#b7eb8f"}`,
                                            borderRadius: "6px",
                                            padding: "8px 16px",
                                            color: isShowGoodLabel === 1 ? "#1890ff" : "#389e0d",
                                            fontSize: "14px",
                                            fontWeight: "500",
                                            textAlign: "center",
                                            minWidth: "100px",
                                            transition: "all 0.2s ease"
                                        }}>
                                            {isShowGoodLabel === 1 ? "在讲品" : "不在讲品"}
                                        </div>
                                        <Button
                                            type="default"
                                            size="small"
                                            onClick={handleEnterEditMode}
                                            style={{
                                                fontSize: "12px",
                                                height: "32px",
                                                padding: "0 12px"
                                            }}
                                        >
                                            修改
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* 标注人信息显示 */}
                        {labelStatus !== 0 && labelUserInfo && !isEditMode && (
                            <div style={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                padding: "16px 0",
                                margin: "0 16px",
                                backgroundColor: "#f5f5f5",
                                borderRadius: "8px",
                                fontSize: "14px",
                                color: "#666"
                            }}>
                                <div style={{display: "flex", gap: "20px"}}>
                  <span>
                    <strong>标注人：</strong>{labelUserInfo.labelUserName}
                  </span>
                                    <span>
                    <strong>标注时间：</strong>{labelUserInfo.labelTime ? new Date(labelUserInfo.labelTime).toLocaleString() : '未知'}
                  </span>
                                </div>
                            </div>
                        )}

                        {/* 提交标注结果按钮 */}
                        {(labelStatus === 0 || isEditMode) && (
                            <div style={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                gap: "12px",
                                padding: "16px 0",
                                margin: "0 16px"
                            }}>
                                <Button
                                    type="primary"
                                    size="large"
                                    onClick={handleSubmitLabelResult}
                                    style={{
                                        backgroundColor: "#1890ff",
                                        borderColor: "#1890ff",
                                        fontSize: "16px",
                                        fontWeight: "500",
                                        padding: "8px 32px",
                                        height: "auto"
                                    }}
                                >
                                    {isEditMode ? "保存修改" : "提交标注结果"}
                                </Button>
                                {isEditMode && (
                                    <Button
                                        size="large"
                                        onClick={handleCancelEditMode}
                                        style={{
                                            fontSize: "16px",
                                            fontWeight: "500",
                                            padding: "8px 32px",
                                            height: "auto"
                                        }}
                                    >
                                        取消
                                    </Button>
                                )}
                            </div>
                        )}
                    </StickyBox>
                </Flex>


                <Flex className={cx('right')} style={{justifyContent: 'center', flex: '0 0 1260px'}}>
                    <Tabs activeKey={activeKey} onChange={handleTabChange} onTabClick={handleTabClick}>
                        <Tabs.TabPane tab="当前商品识别" key="current">
                            <AgentLiveInfo
                                liveStreamId={liveStreamId ?? 0}
                                onSelectChange={keys => console.log('select', keys)}
                                playerRef={playerRef}
                                duration={playerRef.current?.duration ?? 0}
                                playerCurrentTime={currentTime}
                                fetchInfo={fetchInfo}
                                timestamp={specifiedTimestamp}

                                startTime={formatSecondsToHMS(startTime)}
                                endTime={formatSecondsToHMS(endTime)}
                                windowTraceInfoList={windowTracInfoList}
                                isLiving={isLiving}
                                itemTimeRecords={itemTimeRecordsAlgo ?? []}
                                isReplaying={true}
                                onReplayingChange={setIsReplaying}
                                labelStatus={isEditMode ? 0 : labelStatus}
                                frameSelections={frameSelections}
                                onFrameSelectionsChange={setFrameSelections}
                                isShowGoodLabel={isShowGoodLabel}
                                isGoodFrameLabels={isGoodFrameLabels}

                                // URL参数
                                replayId={urlReplayId}

                            />
                        </Tabs.TabPane>
                    </Tabs>

                </Flex>
            </Flex>
        </Flex>
    );
}