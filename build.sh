#!/bin/bash
# 编译脚本
# JIA_BUILD_ENV 为流水线自定义环境变量
# 目前支持 prod/test


echo "============== 构建环境信息 ==================="
echo "产物模式 is $JIA_BUILD_ENV"
echo "分支信息 is ${BUILD_BRANCH}"
echo "============== 构建环境信息 ==================="

# 安装依赖
#source kinstall @es/jia-cmd jia

# 安装依赖
yarn --frozen-lockfile

# 构建产物只有两种，测试产物和生产产物，staging环境发测试产物，prt发生产产物
if [[ $JIA_BUILD_ENV == "prod" ]]; then
	yarn kmi build --mode production --app-env online
else
	yarn kmi build --mode development
fi

echo "============== 构建产物信息 ==================="
ls -l dist
echo "============== 构建产物信息 ==================="
