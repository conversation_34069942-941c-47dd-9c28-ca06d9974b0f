import theme from "./src/theme";
import pkg from "./package.json";
import type { Configuration } from "@es/jia-types";

const ProxyUrl = "flowlive-admin.staging.kuaishou.com";
const isDev = process.env.WEB_ENV === "local";
export default {
  devOptions: {
    hostname: `dev.${ProxyUrl}`,
    proxy: {
      "/rest/*": {
        target: `https://${ProxyUrl}/`,
        changeOrigin: true,
        secure: false,
      },
      "/gateway/*": {
        target: `https://${ProxyUrl}`,
        changeOrigin: true,
        secure: false,
      },
    },
    disabledErrorOverlay: true,
  },
  buildOptions: {
    output: (mode) => ({
      publicPath:
        mode === "production"
          ? `https://w2.eckwai.com/kos/nlav12333/web-assets/${pkg.name}/`
          : `https://w2.eckwai.com/kos/nlav12333/web-assets-staging/${pkg.name}/`,
    }),
    rootId: "main_root",
    codeSplitStrategy: "compatible",
    useForkTsCheckerDefaultDiagnosticOpts: true,
  },
  loaders: {
    less: {
      lessOptions: {
        modifyVars: theme,
        javascriptEnabled: true,
      },
    },
  },
  autoExternals: true,
} as Configuration;
